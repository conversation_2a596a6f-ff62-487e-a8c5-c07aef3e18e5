import { <PERSON>, Button, message, Typography, Space, Tooltip } from "antd";
import { ExternalLink, Copy, Video, Calendar } from "lucide-react";

const { Text, Link } = Typography;

interface ExternalEventLinkProps {
    /**
     * The external event link (e.g., Google Meet link)
     */
    extEventLink?: string;
    /**
     * Custom class name for styling
     */
    className?: string;
}

/**
 * Component that displays an external event link (e.g., Google Meet)
 * Shows the link with actions to open externally and copy to clipboard
 */
export default function ExternalEventLink({
    extEventLink,
    className,
}: ExternalEventLinkProps) {
    /**
     * Copies the external event link to clipboard and shows success message
     */
    const handleCopyToClipboard = async () => {
        if (!extEventLink) return;

        try {
            await navigator.clipboard.writeText(extEventLink);
            message.success("Enlace del evento copiado al portapapeles");
        } catch (error) {
            message.error("Error al copiar el enlace");
        }
    };

    /**
     * Opens the external event link in a new tab
     */
    const handleOpenExternal = () => {
        if (!extEventLink) return;
        window.open(extEventLink, "_blank", "noopener,noreferrer");
    };

    /**
     * Determines the platform based on the URL
     */
    const getPlatformInfo = (url: string) => {
        const lowerUrl = url.toLowerCase();

        if (lowerUrl.includes("meet.google.com")) {
            return { name: "Google Meet", color: "#4285f4", icon: Video };
        }
        if (lowerUrl.includes("zoom.us")) {
            return { name: "Zoom", color: "#2d8cff", icon: Video };
        }
        if (lowerUrl.includes("teams.microsoft.com")) {
            return { name: "Microsoft Teams", color: "#6264a7", icon: Video };
        }

        // Default for any other link
        return { name: "Enlace del Evento", color: "#52c41a", icon: Calendar };
    };

    /**
     * Truncates long URLs for better display
     */
    const truncateUrl = (url: string, maxLength: number = 40) => {
        if (url.length <= maxLength) return url;
        return `${url.substring(0, maxLength)}...`;
    };

    // Don't render if no external link
    if (!extEventLink) {
        return null;
    }

    const platformInfo = getPlatformInfo(extEventLink);
    const IconComponent = platformInfo.icon;

    return (
        <div className={className}>
            <Card
                size="small"
                className="border border-green-200 bg-green-50"
                bodyStyle={{ padding: "16px" }}
            >
                <Space direction="vertical" size={8} className="w-full">
                    <Space align="center" className="w-full justify-between">
                        <Space align="center">
                            <IconComponent
                                size={18}
                                style={{ color: platformInfo.color }}
                            />
                            <Text strong style={{ color: platformInfo.color }}>
                                {platformInfo.name}
                            </Text>
                        </Space>
                        <Space>
                            <Tooltip title="Abrir en nueva pestaña">
                                <Button
                                    type="text"
                                    size="small"
                                    icon={<ExternalLink size={14} />}
                                    onClick={handleOpenExternal}
                                    className="hover:bg-green-100"
                                    style={{ color: platformInfo.color }}
                                />
                            </Tooltip>
                            <Tooltip title="Copiar enlace">
                                <Button
                                    type="text"
                                    size="small"
                                    icon={<Copy size={14} />}
                                    onClick={handleCopyToClipboard}
                                    className="hover:bg-green-100"
                                    style={{ color: platformInfo.color }}
                                />
                            </Tooltip>
                        </Space>
                    </Space>

                    <Link
                        href={extEventLink}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-sm break-all"
                        style={{ color: platformInfo.color }}
                    >
                        {truncateUrl(extEventLink)}
                    </Link>

                    <Text type="secondary" className="text-xs">
                        Enlace para acceder al evento virtual
                    </Text>
                </Space>
            </Card>
        </div>
    );
}
