import { Form, Input, Button } from "antd";
import type { FormInstance } from "antd";
import { useMutation } from "@tanstack/react-query";
import { updateMajor } from "@/features/crm/services/portals/major";
import type { Major } from "@/features/crm/types/major";

type ErrorData = { name?: unknown; detail?: unknown };
type APIError = { response?: { data?: ErrorData } };

export type EditMajorFormProps = {
    form: FormInstance;
    major: Major | null;
    onClose: () => void;
    onUpdated: () => void;
    notify: (type: "success" | "error", message: string, description?: string) => void;
};

export default function EditMajorForm({
    form,
    major,
    onClose,
    onUpdated,
    notify,
}: EditMajorFormProps) {
    const { mutate, isPending } = useMutation({
        mutationFn: async (name: string) => updateMajor(major!.mid, { name }),
        onSuccess: () => {
            notify(
                "success",
                "Carrera actualizada",
                "Los cambios se guardaron correctamente",
            );
            onUpdated();
            onClose();
        },
        onError: (err: unknown) => {
            const res = (err as APIError).response?.data ?? {};
            const nameVal = res.name;
            const fieldMsg = Array.isArray(nameVal) ? nameVal[0] : nameVal;
            const detail = res.detail;
            if (fieldMsg) {
                form.setFields([{ name: "name", errors: [String(fieldMsg)] }]);
            }
            const friendly =
                fieldMsg ||
                detail ||
                "No se pudo actualizar la carrera. Verifica el nombre e inténtalo nuevamente.";
            notify("error", "Error al actualizar", friendly);
        },
    });

    return (
        <Form
            form={form}
            layout="vertical"
            onFinish={(v: { name: string }) => mutate(v.name)}
            initialValues={{ name: major?.name }}
        >
            <Form.Item
                name="name"
                label="Nombre de la Carrera"
                rules={[{ required: true, message: "Ingrese el nombre" }]}
            >
                <Input placeholder="Ej. Ingeniería de Sistemas" disabled={isPending} />
            </Form.Item>
            <div className="flex justify-end gap-2">
                <Button onClick={onClose} disabled={isPending}>
                    Cancelar
                </Button>
                <Button type="primary" htmlType="submit" loading={isPending}>
                    Guardar
                </Button>
            </div>
        </Form>
    );
}
