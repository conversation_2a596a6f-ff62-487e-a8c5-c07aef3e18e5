import { Route, Routes } from "react-router-dom";

import AdminDashboardPage from "@pages/admin/DashboardPage";
import InstructorListPage from "@pages/admin/InstructorListPage";
import InstructorDetailPage from "@pages/admin/InstructorDetailPage";
import InstructorEditPage from "@pages/admin/InstructorEditPage";
import TestimonialListPage from "@pages/admin/TestimonialListPage";
import TestimonialDetailPage from "@pages/admin/TestimonialDetailPage";
import TestimoniaEditPage from "@pages/admin/TestimonialEditPage";
import OfferingListPage from "@pages/admin/OfferingListPage";
import OfferingDetailPage from "@pages/admin/OfferingDetailPage";
import OfferingEditPage from "@pages/admin/OfferingEditPage";

import NotFoundPage from "@pages/admin/NotFoundPage";

export default function AdminRoutes() {
    return (
        <Routes>
            <Route path="" element={<AdminDashboardPage />} />

            {/** Instructor Routes */}
            <Route path="instructor" element={<InstructorListPage />} />
            <Route path="instructor/:iid" element={<InstructorDetailPage />} />
            <Route path="instructor/:iid/edit" element={<InstructorEditPage />} />

            {/** Testimonial Routes */}
            <Route path="testimonial" element={<TestimonialListPage />} />
            <Route path="testimonial/:tid" element={<TestimonialDetailPage />} />
            <Route path="testimonial/:tid/edit" element={<TestimoniaEditPage />} />

            {/** Offering Routes */}
            <Route path="offering" element={<OfferingListPage />} />
            <Route path="offering/:oid" element={<OfferingDetailPage />} />
            <Route path="offering/:oid/edit" element={<OfferingEditPage />} />

            {/** Category Routes */}

            {/** Not Found */}
            <Route path="*" element={<NotFoundPage />} />
        </Routes>
    );
}
