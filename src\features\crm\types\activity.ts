// Base audit model types (simplified based on actual API response)
export type AuditBaseModel = {
    readonly createdAt: string; // ISO date string from "created_at"
    readonly updatedAt: string; // ISO date string from "updated_at"
    // Note: deleted, deletedAt, deletedBy not present in API responses
};

// Activity status enum
export enum ActivityStatus {
    COMPLETED = "completed",
    PENDING = "pending",
    IN_PROGRESS = "in_progress",
}

// Activity status choices for display
export const ACTIVITY_STATUS_CHOICES = [
    { value: ActivityStatus.COMPLETED, label: "Completada" },
    { value: ActivityStatus.PENDING, label: "Pendiente" },
    { value: ActivityStatus.IN_PROGRESS, label: "En Progreso" },
] as const;

// Related entity types
export type ResponsibleUser = {
    readonly uid: string;
    readonly fullName: string;
    readonly email: string;
    readonly phoneNumber: string;
};

export type OrderSummary = {
    readonly oid: string;
    readonly stage: string;
    readonly owner: string;
};

// Base Activity type (minimal fields for create/update)
export type Activity = {
    aid: string; // UUID
    title: string | null;
    description: string | null;
    deadline: string | null; // ISO date string or null
    responsible: string | null; // User ID (UUID) or null
    status: ActivityStatus;
    order: string | null; // Order ID (UUID) or null
};

// Create variant - excludes auto-generated fields
export type ActivityCreate = Partial<
    Pick<
        Activity,
        "title" | "description" | "deadline" | "responsible" | "status" | "order"
    >
>;

// Update variant - all fields optional for partial updates
export type ActivityUpdate = Partial<
    Pick<
        Activity,
        "title" | "description" | "deadline" | "responsible" | "status" | "order"
    >
>;

// Retrieve variant - includes populated relations and audit fields
export type ActivityRetrieve = AuditBaseModel & {
    readonly key: string; // Same as aid, used as identifier
    readonly aid: string; // UUID
    title: string | null;
    description: string | null;
    deadline: string | null; // ISO date string or null
    responsible: ResponsibleUser | null; // Populated user object
    status: ActivityStatus;
    order: OrderSummary | null; // Populated order object
};

// List variant - same structure as retrieve (based on your API response)
export type ActivityList = ActivityRetrieve;

// Utility types for forms and API responses
export type ActivityFormData = ActivityCreate;
export type ActivityUpdateData = ActivityUpdate;

// Query parameters for filtering/searching
export interface ActivityQueryParams {
    status?: ActivityStatus;
    responsible?: string; // User ID
    order?: string; // Order ID
    deadline?: string; // ISO date string
    deadlineFrom?: string; // ISO date string for date range filtering
    deadlineTo?: string; // ISO date string for date range filtering
    deadlineDate?: string; // ISO date string for specific date filtering
    search?: string;
    page?: number;
    pageSize?: number;
    ordering?: string;
}

// Soft delete operations
export interface ActivityDelete {
    aid: string;
    deletedBy: string; // User ID who performed the deletion
}
