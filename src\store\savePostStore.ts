import { BlogPost } from "@myTypes/blog";
import { create } from "zustand";

export enum SavePostStatusEnum {
    IDLE = "idle",
    SAVING = "saving",
    SAVED = "saved",
    ERROR = "error",
}

interface SavePostStore {
    saveStatus: SavePostStatusEnum;
    lastSavedAt: number | null;
    errorMessage: string | null;
    currentBlogId: string | null;
    setSaveStatus: (status: SavePostStatusEnum) => void;
    setCurrentBlogId: (id: string | null) => void;
    save: <T = BlogPost>(callback: () => Promise<T>) => Promise<void>;
    resetStatus: () => void;
}

export const useSavePostStore = create<SavePostStore>((set) => ({
    saveStatus: SavePostStatusEnum.IDLE,
    lastSavedAt: null,
    errorMessage: null,
    currentBlogId: null,

    setSaveStatus: (status) =>
        set({
            saveStatus: status,
            // Si se establece como SAVED, registrar la marca de tiempo
            ...(status === SavePostStatusEnum.SAVED ? { lastSavedAt: Date.now() } : {}),
            // Si no es un error, limpiar el mensaje de error
            ...(status !== SavePostStatusEnum.ERROR ? { errorMessage: null } : {}),
        }),

    setCurrentBlogId: (id) =>
        set({
            currentBlogId: id,
            saveStatus: SavePostStatusEnum.IDLE,
            lastSavedAt: null,
            errorMessage: null,
        }),

    save: async (callback) => {
        set({ saveStatus: SavePostStatusEnum.SAVING });

        try {
            await callback();
            set({
                saveStatus: SavePostStatusEnum.SAVED,
                lastSavedAt: Date.now(),
                errorMessage: null,
            });

            // Volver a IDLE después de un tiempo
            setTimeout(() => {
                set((state) => {
                    // Solo volver a IDLE si el estado aún es SAVED
                    // (para evitar sobreescribir un nuevo estado de guardado)
                    if (state.saveStatus === SavePostStatusEnum.SAVED) {
                        return { saveStatus: SavePostStatusEnum.IDLE };
                    }
                    return {};
                });
            }, 3000);
        } catch (error) {
            console.error("Error durante el guardado:", error);
            set({
                saveStatus: SavePostStatusEnum.ERROR,
                errorMessage:
                    error instanceof Error
                        ? error.message
                        : "Ocurrió un error desconocido",
            });

            // Volver a IDLE después de mostrar el error
            setTimeout(() => {
                set((state) => {
                    if (state.saveStatus === SavePostStatusEnum.ERROR) {
                        return { saveStatus: SavePostStatusEnum.IDLE };
                    }
                    return {};
                });
            }, 5000);
        }
    },

    resetStatus: () =>
        set({
            saveStatus: SavePostStatusEnum.IDLE,
            errorMessage: null,
        }),
}));
