import { ActivityStatus, ACTIVITY_STATUS_CHOICES } from "../../types/activity";
import { CheckCircle, Clock, Play } from "lucide-react";

interface ActivityStatusCellProps {
    status: ActivityStatus;
}

const ActivityStatusCell = ({ status }: ActivityStatusCellProps) => {
    const statusChoice = ACTIVITY_STATUS_CHOICES.find(
        (choice) => choice.value === status,
    );

    const getStatusConfig = (status: ActivityStatus) => {
        switch (status) {
            case ActivityStatus.COMPLETED:
                return {
                    color: "success",
                    icon: <CheckCircle size={14} strokeWidth={2.5} />,
                    className: "bg-green-100 text-green-800 border border-green-200",
                };
            case ActivityStatus.IN_PROGRESS:
                return {
                    color: "processing",
                    icon: <Play size={14} strokeWidth={2.5} />,
                    className: "bg-blue-100 text-blue-800 border border-blue-200",
                };
            case ActivityStatus.PENDING:
                return {
                    color: "warning",
                    icon: <Clock size={14} strokeWidth={2.5} />,
                    className: "bg-amber-50 text-amber-800 border border-amber-200",
                };
            default:
                return {
                    color: "default",
                    icon: <Clock size={14} strokeWidth={2.5} />,
                    className: "bg-gray-100 text-gray-800 border border-gray-200",
                };
        }
    };

    const config = getStatusConfig(status);

    return (
        <span
            className={`text-xs font-medium px-3 py-1 rounded-full ${config.className}`}
        >
            <div className="flex items-center gap-1">
                {config.icon}
                <span>{statusChoice?.label || status}</span>
            </div>
        </span>
    );
};

export default ActivityStatusCell;
