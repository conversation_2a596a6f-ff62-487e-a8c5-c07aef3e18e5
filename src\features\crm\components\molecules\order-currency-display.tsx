import { useEffect, useState } from "react";
import { Form, Tag, Tooltip } from "antd";
import { DollarSign } from "lucide-react";
import { OrderCurrency, OrderCurrencyLabels } from "@/features/crm/types/order";
import { getCurrencyByPhoneNumber } from "@/features/crm/utils/currency";
import { useContact } from "@/features/crm/hooks/use-contact";

/**
 * Componente que muestra la moneda determinada en base al número telefónico del contacto seleccionado.
 * Si el contacto tiene un número de teléfono que comienza con '+51' o '51', se muestra PEN (soles).
 * De lo contrario, se muestra USD (dólares).
 */
export default function OrderCurrencyDisplay() {
    const contactId = Form.useWatch("owner");
    const [currency, setCurrency] = useState<OrderCurrency>(OrderCurrency.PEN);
    const { contact, isLoading } = useContact(contactId);

    useEffect(() => {
        if (contact?.phoneNumber) {
            const determinedCurrency = getCurrencyByPhoneNumber(contact.phoneNumber);
            setCurrency(determinedCurrency);
        }
    }, [contact]);

    if (!contactId || isLoading) {
        return null;
    }

    const currencyColor = currency === OrderCurrency.USD ? "blue" : "green";
    const currencySymbol = currency === OrderCurrency.USD ? "$" : "S/";

    return (
        <div className="flex items-center mt-2 mb-4">
            <Tooltip title="Moneda determinada por el código de país del teléfono del contacto">
                <div className="flex items-center">
                    <DollarSign size={16} className="text-gray-500 mr-1" />
                    <span className="text-gray-700 font-medium">Moneda:</span>
                    <div className="ml-2 flex items-center">
                        <span className="font-bold mr-1">{currencySymbol}</span>
                        <Tag color={currencyColor}>{OrderCurrencyLabels[currency]}</Tag>
                    </div>
                </div>
            </Tooltip>
        </div>
    );
}
