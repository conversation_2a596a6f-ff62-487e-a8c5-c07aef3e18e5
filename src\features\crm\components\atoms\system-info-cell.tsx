import { Typo<PERSON>, Tooltip, Tag } from "antd";
import { BadgeChe<PERSON>, User, Key } from "lucide-react";
import { ContactListItem } from "@/features/crm/types/contact";

const { Text } = Typography;

type SystemInfoCellProps = {
    contact: ContactListItem;
};

const SystemInfoCell = ({ contact }: SystemInfoCellProps) => {
    const { username, isActive } = contact;

    return (
        <div className="flex flex-col gap-2">
            {/* Username */}
            <div className="flex items-center gap-2">
                <Key size={14} className="text-gray-400" />
                <Text className="text-sm">{username || "Sin usuario"}</Text>
            </div>

            {/* Account Status */}
            <div className="flex items-center">
                {isActive ? (
                    <Tooltip title="Cuenta activa en plataforma">
                        <Tag color="success" className="flex items-center gap-1">
                            <BadgeCheck size={14} />
                            <span>Activo</span>
                        </Tag>
                    </Tooltip>
                ) : (
                    <Tooltip title="Sin cuenta en plataforma">
                        <Tag color="default" className="flex items-center gap-1">
                            <User size={14} />
                            <span>Inactivo</span>
                        </Tag>
                    </Tooltip>
                )}
            </div>
        </div>
    );
};

export default SystemInfoCell;
