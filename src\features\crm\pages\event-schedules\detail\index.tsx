import { usePara<PERSON>, useNavi<PERSON>, <PERSON> } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { Typography, App, Breadcrumb, Tabs, TabsProps } from "antd";
import { useEffect } from "react";
import CrmLayout from "@/features/crm/layout";
import Spinner from "@components/shared/atoms/Spinner";
import WelcomeBar from "@components/shared/molecules/WelcomeBar";
import { getEventSchedule } from "@/features/crm/services/portals/event-schedule";
import GeneralEventScheduleDetail from "@/features/crm/components/organisms/general-event-schedule-detail";
import EventScheduleEnrollmentsTab from "@/features/crm/components/organisms/event-schedule-enrollments-tab";
import EventScheduleRemindersTab from "@/features/crm/components/organisms/event-schedule-reminders-tab";

const { Text } = Typography;

export default function EventScheduleDetailPage() {
    const { esid } = useParams<{ esid: string }>();
    const navigate = useNavigate();
    const { notification } = App.useApp();

    const {
        data: eventSchedule,
        isLoading,
        error,
    } = useQuery({
        queryKey: ["event-schedule", esid],
        queryFn: () => getEventSchedule(esid as string),
        enabled: !!esid,
    });

    useEffect(() => {
        if (error) {
            notification.error({
                message: "Error",
                description: "Error al cargar el evento",
            });
            navigate("/crm/events", { replace: true });
        }
    }, [error, notification, navigate]);

    const tabItems: TabsProps["items"] = [
        {
            key: "general",
            label: "General",
            children: eventSchedule && (
                <GeneralEventScheduleDetail eventSchedule={eventSchedule} />
            ),
        },
        {
            key: "enrollments",
            label: "Inscripciones",
            children: eventSchedule && (
                <EventScheduleEnrollmentsTab eventSchedule={eventSchedule} />
            ),
        },
        {
            key: "invitations",
            label: "Invitaciones",
            children: eventSchedule && (
                <EventScheduleRemindersTab eventSchedule={eventSchedule} />
            ),
        },
    ];

    if (isLoading) {
        return (
            <CrmLayout>
                <Spinner />
            </CrmLayout>
        );
    }

    if (!eventSchedule) {
        return (
            <CrmLayout>
                <div className="flex items-center justify-center h-full">
                    <Text type="danger">
                        Error al cargar los detalles del horario del evento
                    </Text>
                </div>
            </CrmLayout>
        );
    }

    return (
        <CrmLayout>
            <div className="max-w-7xl w-full h-full space-y-5">
                <div className="flex justify-between items-center">
                    <WelcomeBar helperText="Edita aquí los detalles de un Evento." />
                </div>
                <div className="p-5 bg-white-full rounded-lg space-y-5 shadow-sm">
                    <div className="flex items-center justify-between">
                        <Breadcrumb
                            separator=">"
                            items={[
                                {
                                    title: (
                                        <Link to="/crm/events" className="text-base">
                                            Horarios de Eventos
                                        </Link>
                                    ),
                                },
                                {
                                    title: (
                                        <Link
                                            to={`/crm/events/${eventSchedule.esid}`}
                                            className="text-base"
                                        >
                                            {eventSchedule.name}
                                        </Link>
                                    ),
                                },
                                {
                                    title: (
                                        <Text className="text-base">Ver & Editar</Text>
                                    ),
                                },
                            ]}
                        />
                    </div>
                </div>
                <Tabs items={tabItems} />
            </div>
        </CrmLayout>
    );
}
