import { useMemo, useCallback } from "react";
import { Table, Button, Dropdown, Tooltip, ConfigProvider, Empty } from "antd";
import type { TableProps } from "antd";
import { MoreVertical, Hash, Scissors } from "lucide-react";
import { Link } from "react-router-dom";
import { EnrollmentListItem } from "../../types/enrollment";
import EnrollmentUserCell from "../atoms/enrollment-user-cell";
import EnrollmentOfferingCell from "../atoms/enrollment-offering-cell";
import EnrollmentStatusCell from "../atoms/enrollment-status-cell";
import Spinner from "@components/shared/atoms/Spinner";

import DeleteStroke from "@assets/icons/huge/delete-stroke.svg?react";
import EditStroke from "@assets/icons/huge/edit-stroke.svg?react";
import DateCell from "../atoms/date-cell";

interface EnrollmentsTableProps {
    enrollments: EnrollmentListItem[];
    loading?: boolean;
    onEdit?: (enrollment: EnrollmentListItem) => void;
    onDelete?: (enrollment: EnrollmentListItem) => void;
}

const INITIAL_COLUMNS: TableProps<EnrollmentListItem>["columns"] = [
    {
        title: "ID",
        dataIndex: "eid",
        key: "eid",
        width: 80,
        render: (eid: string) => (
            <Tooltip title="Ver detalles de la matrícula">
                <Link
                    to={`/lms/enrollments/${eid}`}
                    className="font-semibold text-blue-600 flex items-center gap-1"
                >
                    <Hash size={14} strokeWidth={2.5} className="text-gray-500" />
                    {eid.slice(-6)}
                </Link>
            </Tooltip>
        ),
    },
    {
        title: "USUARIO",
        dataIndex: "user",
        key: "user",
        width: 250,
        render: (user: EnrollmentListItem["user"]) => (
            <EnrollmentUserCell user={user} />
        ),
    },
    {
        title: "PRODUCTO",
        dataIndex: "offering",
        key: "offering",
        width: 200,
        render: (
            offering: EnrollmentListItem["offering"],
            record: EnrollmentListItem,
        ) => (
            <EnrollmentOfferingCell offering={offering} orderItem={record.orderItem} />
        ),
    },
    {
        title: "ESTADO",
        key: "status",
        width: 150,
        render: (_, record: EnrollmentListItem) => (
            <EnrollmentStatusCell
                isActive={record.isActive}
                certificateIssued={record.certificateIssued}
            />
        ),
    },
    {
        title: "FECHAS",
        dataIndex: "createdAt",
        key: "dates",
        width: 150,
        render: (_, record: EnrollmentListItem) => (
            <div className="space-y-2">
                <DateCell date={record.createdAt} label="Creado" />
                <DateCell date={record.updatedAt} label="Actualizado" />
            </div>
        ),
    },
];
export default function EnrollmentsTable({
    enrollments,
    loading = false,
    onEdit,
    onDelete,
}: EnrollmentsTableProps) {
    const handleRowAction = useCallback(
        (key: string, record: EnrollmentListItem) => {
            if (key === "edit") {
                onEdit?.(record);
            }
            if (key === "delete") {
                onDelete?.(record);
            }
        },
        [onEdit, onDelete],
    );

    const columns = useMemo(() => {
        const actionColumns: TableProps<EnrollmentListItem>["columns"] = [
            {
                title: <Scissors />,
                key: "actions",
                width: 80,
                render: (record: EnrollmentListItem) => (
                    <Dropdown
                        trigger={["click"]}
                        menu={{
                            items: [
                                {
                                    key: "edit",
                                    label: (
                                        <div className="flex items-center gap-2 text-blue-600">
                                            <EditStroke className="w-5 h-5" /> Editar
                                        </div>
                                    ),
                                },
                                {
                                    key: "delete",
                                    label: (
                                        <div className="flex items-center gap-2 text-red-600">
                                            <DeleteStroke className="w-5 h-5" />{" "}
                                            Eliminar
                                        </div>
                                    ),
                                },
                            ],
                            onClick: ({ key }) => {
                                handleRowAction(key, record);
                            },
                        }}
                        placement="bottomRight"
                    >
                        <Button
                            icon={<MoreVertical className="w-5 h-5" />}
                            type="text"
                            size="small"
                        />
                    </Dropdown>
                ),
            },
        ];

        const baseColumns = INITIAL_COLUMNS || [];
        const actions = actionColumns || [];
        return [...baseColumns, ...actions];
    }, [handleRowAction]);

    return (
        <ConfigProvider
            theme={{
                components: {
                    Table: {
                        headerBg: "#FBFCFD",
                        borderColor: "#fff",
                        headerSplitColor: "#fafafa",
                        headerBorderRadius: 8,
                        rowHoverBg: "#F6FAFD",
                        rowSelectedBg: "#F6FAFD",
                        rowSelectedHoverBg: "#F6FAFD",
                        footerBg: "#F1F1F1",
                    },
                },
            }}
        >
            <Table
                className="rounded-lg shadow-sm"
                footer={() => ""}
                pagination={false}
                columns={columns}
                locale={{
                    emptyText: <>{loading ? <Spinner /> : <Empty />}</>,
                }}
                dataSource={enrollments}
                loading={loading}
                rowKey="eid"
                scroll={{ x: "max-content" }}
            />
        </ConfigProvider>
    );
}
