@tailwind base;
@tailwind components;
@tailwind utilities;


@font-face {
    font-family: 'DM Sans';
    font-style: normal;
    font-weight: 100;
    src: url('assets/fonts/DMSans-VariableFont.ttf') format('truetype');
}

@font-face {
    font-family: 'DM Sans';
    font-style: normal;
    font-weight: 200;
    src: url('assets/fonts/DMSans-VariableFont.ttf') format('truetype');
}

@font-face {
    font-family: 'DM Sans';
    font-style: normal;
    font-weight: 300;
    src: url('assets/fonts/DMSans-VariableFont.ttf') format('truetype');
}

@font-face {
    font-family: 'DM Sans';
    font-style: normal;
    font-weight: 400;
    src: url('assets/fonts/DMSans-VariableFont.ttf') format('truetype');
}

@font-face {
    font-family: 'DM Sans';
    font-style: normal;
    font-weight: 500;
    src: url('assets/fonts/DMSans-VariableFont.ttf') format('truetype');
}

@font-face {
    font-family: 'DM Sans';
    font-style: normal;
    font-weight: 600;
    src: url('assets/fonts/DMSans-VariableFont.ttf') format('truetype');
}

@font-face {
    font-family: 'DM Sans';
    font-style: normal;
    font-weight: 700;
    src: url('assets/fonts/DMSans-VariableFont.ttf') format('truetype');
}

@font-face {
    font-family: 'DM Sans';
    font-style: normal;
    font-weight: 800;
    src: url('assets/fonts/DMSans-VariableFont.ttf') format('truetype');
}

@font-face {
    font-family: 'DM Sans';
    font-style: normal;
    font-weight: 900;
    src: url('assets/fonts/DMSans-VariableFont.ttf') format('truetype');
}


* {
    font-family: 'DM Sans', sans-serif;
}
body {
    padding: 0;
    margin: 0;
}

html {
  width: min-content;
}

@media (min-width: 768px) {
  html {
    width: auto;
  }
}