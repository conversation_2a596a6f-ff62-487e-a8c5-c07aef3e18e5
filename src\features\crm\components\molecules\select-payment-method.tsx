import { Select, SelectProps, <PERSON><PERSON>, Divider, Form, Input, Modal } from "antd";
import { Plus } from "lucide-react";
import { useState } from "react";
import {
    usePaymentMethods,
    useCreatePaymentMethod,
} from "../../hooks/use-payment-method";
import { CreatePaymentMethod, PaymentMethod } from "../../types/payment-method";

interface SelectPaymentMethodProps extends Omit<SelectProps, "options"> {
    value?: string;
    onChange?: (value: string) => void;
}

export default function SelectPaymentMethod({
    value,
    onChange,
    ...restProps
}: SelectPaymentMethodProps) {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [form] = Form.useForm();

    const { paymentMethods, isLoading } = usePaymentMethods();
    const { mutate: createPaymentMethodMutation, isPending: isCreating } =
        useCreatePaymentMethod({
            onSuccess: () => {
                setIsModalOpen(false);
                form.resetFields();
            },
        });

    const paymentMethodOptions: SelectProps["options"] =
        paymentMethods?.map((method: PaymentMethod) => ({
            value: method.pmid,
            label: method.name,
        })) || [];

    const handleChange = (selectedValue: string) => {
        if (onChange) {
            onChange(selectedValue);
        }
    };

    const handleCreatePaymentMethod = async (values: CreatePaymentMethod) => {
        createPaymentMethodMutation(values);
    };

    return (
        <>
            <Select
                {...restProps}
                value={value}
                onChange={handleChange}
                options={paymentMethodOptions}
                loading={isLoading}
                optionFilterProp="label"
                allowClear
                showSearch
                dropdownRender={(menu) => (
                    <>
                        {menu}
                        <Divider className="my-1" />
                        <div className="flex justify-between items-center px-2">
                            <p className="text-sm text-gray-700 font-medium">
                                ¿No encuentras el método de pago?
                            </p>
                            <Button
                                size="small"
                                type="primary"
                                icon={<Plus size={12} />}
                                onClick={() => setIsModalOpen(true)}
                            >
                                Agregar
                            </Button>
                        </div>
                    </>
                )}
            />

            <Modal
                title="Agregar Método de Pago"
                open={isModalOpen}
                onCancel={() => {
                    setIsModalOpen(false);
                    form.resetFields();
                }}
                footer={null}
                confirmLoading={isCreating}
            >
                <Form
                    form={form}
                    onFinish={handleCreatePaymentMethod}
                    layout="vertical"
                >
                    <Form.Item
                        name="name"
                        label="Nombre del método de pago"
                        rules={[
                            {
                                required: true,
                                message:
                                    "Por favor ingrese el nombre del método de pago",
                            },
                        ]}
                    >
                        <Input
                            placeholder="Ingrese el nombre del método de pago"
                            disabled={isCreating}
                        />
                    </Form.Item>
                    <Form.Item name="description" label="Descripción">
                        <Input.TextArea
                            placeholder="Ingrese una descripción (opcional)"
                            disabled={isCreating}
                        />
                    </Form.Item>
                    <Form.Item className="mb-0 text-right">
                        <Button
                            type="default"
                            onClick={() => {
                                setIsModalOpen(false);
                                form.resetFields();
                            }}
                            className="mr-2"
                            disabled={isCreating}
                        >
                            Cancelar
                        </Button>
                        <Button type="primary" htmlType="submit" loading={isCreating}>
                            Guardar
                        </Button>
                    </Form.Item>
                </Form>
            </Modal>
        </>
    );
}
