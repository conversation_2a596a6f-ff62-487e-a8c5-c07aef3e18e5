import { ConfigProvider, Table, Typography } from "antd";
import type { TableProps } from "antd";
import { useMemo } from "react";
import { EventScheduleEnrollment } from "../../types/event-schedule-enrollment";
import { formatDateTime } from "@lib/helpers";
import EnrollmentContactCell from "../atoms/enrollment-contact-cell";
import EnrollmentStatusCell from "../atoms/enrollment-status-cell";
import EnrollmentAcademicCell from "../atoms/enrollment-academic-cell";
import EnrollmentInterestsCell from "../atoms/enrollment-interests-cell";
import { Hash } from "lucide-react";

const { Text } = Typography;

const ENROLLMENT_COLUMNS: TableProps<EventScheduleEnrollment>["columns"] = [
    {
        title: "ID",
        dataIndex: "id",
        key: "id",
        width: 80,
        render: (id: number) => (
            <div className="flex items-center gap-1">
                <Hash size={14} strokeWidth={2.5} className="text-gray-500" />
                <Text className="font-semibold text-sm">#{id}</Text>
            </div>
        ),
    },
    {
        title: "INFORMACIÓN DE CONTACTO",
        key: "contact",
        width: 250,
        render: (_, record: EventScheduleEnrollment) => (
            <EnrollmentContactCell
                fullName={record.fullName}
                email={record.email}
                phoneNumber={record.phoneNumber}
                uid={record.user?.uid}
            />
        ),
    },
    {
        title: "INFORMACIÓN ACADÉMICA",
        key: "academic",
        width: 200,
        render: (_, record: EventScheduleEnrollment) => (
            <EnrollmentAcademicCell
                university={record.university}
                major={record.major}
                term={record.term}
                occupation={record.occupation}
            />
        ),
    },
    {
        title: "INTERESES & CANAL",
        key: "interests",
        width: 200,
        render: (_, record: EventScheduleEnrollment) => (
            <EnrollmentInterestsCell
                interests={record.interests}
                diffusionChannel={record.diffusionChannel}
                partnership={record.partnership}
            />
        ),
    },
    {
        title: "ESTADO",
        key: "status",
        width: 150,
        render: (_, record: EventScheduleEnrollment) => (
            <EnrollmentStatusCell
                hasContact={!!record.user}
                needsConciliation={record.needsConciliation}
                alreadyLead={record.alreadyLead}
            />
        ),
    },
    {
        title: "FECHA DE INSCRIPCIÓN",
        dataIndex: "createdAt",
        key: "createdAt",
        width: 180,
        render: (createdAt: string) => {
            const formattedDate = formatDateTime(createdAt);
            return <Text className="text-sm">{formattedDate}</Text>;
        },
    },
    {
        title: "ÚLTIMA ACTUALIZACIÓN",
        dataIndex: "updatedAt",
        key: "updatedAt",
        width: 180,
        render: (updatedAt: string) => {
            const formattedDate = formatDateTime(updatedAt);
            return <Text className="text-sm">{formattedDate}</Text>;
        },
    },
];

type EventScheduleEnrollmentsTableProps = {
    enrollments: EventScheduleEnrollment[];
    loading?: boolean;
};

export default function EventScheduleEnrollmentsTable({
    enrollments,
    loading = false,
}: EventScheduleEnrollmentsTableProps) {
    const columns = useMemo(() => ENROLLMENT_COLUMNS, []);

    return (
        <ConfigProvider
            theme={{
                components: {
                    Table: {
                        headerBg: "#FBFCFD",
                        borderColor: "#fff",
                        headerSplitColor: "#fafafa",
                        headerBorderRadius: 8,
                        rowHoverBg: "#F6FAFD",
                        rowSelectedBg: "#F6FAFD",
                        rowSelectedHoverBg: "#F6FAFD",
                        footerBg: "#F1F1F1",
                    },
                },
            }}
        >
            <Table
                className="rounded-lg shadow-sm"
                footer={() => ""}
                pagination={false}
                columns={columns}
                dataSource={enrollments}
                loading={loading}
                rowKey={(record) => record.id}
                scroll={{ x: 1200 }}
                locale={{
                    emptyText: (
                        <div className="text-center py-8">
                            <Text type="secondary">
                                No hay inscripciones para mostrar
                            </Text>
                        </div>
                    ),
                }}
            />
        </ConfigProvider>
    );
}
