import { Route, Routes } from "react-router-dom";
import LmsOutlet from "./outlet";

import DashboardPage from "./pages/dashboard";
import EnrollmentsListPage from "./pages/enrollments/list";
import EnrollmentDetailPage from "./pages/enrollments/detail";
import ProgramsListPage from "./pages/programs/list";
import ProgramDetailPage from "./pages/programs/detail";

export default function LmsRoutes() {
    return (
        <Routes>
            <Route element={<LmsOutlet />}>
                {/** LMS Dashboard Route */}
                <Route path="" element={<DashboardPage />} />

                {/** Enrollments Routes */}
                <Route path="enrollments" element={<EnrollmentsListPage />} />
                <Route path="enrollments/:eid" element={<EnrollmentDetailPage />} />

                {/* Program Routes */}
                <Route path="programs" element={<ProgramsListPage />} />
                <Route path="programs/:oid" element={<ProgramDetailPage />} />

                {/** Not Found Route */}
                <Route path="*" element={null} />
            </Route>
        </Routes>
    );
}
