import { useMutation, UseMutationOptions, useQuery } from "@tanstack/react-query";

import {
    getOrderDashboardSummary,
    getOrderDashboardAgents,
    getOrderDashboardAnalytics,
    getOrderDashboardHistorical,
    getOrderDashboardProducts,
    getOrderDashboardRecentOrders,
    invalidateOrderDashboardCache,
    getOrderDashboardLeadSources,
} from "../services/portals/dashboard/order";
import type { DashboardOrderQueryParams } from "../types/dashboard/orders";
import type { Dayjs } from "dayjs";

// Hook para el summary (más importante, se carga primero)
export const useDashboardOrdersSummary = (query: DashboardOrderQueryParams = {}) => {
    const { data, isLoading, isError, isFetching } = useQuery({
        queryKey: ["dashboard-orders-summary", query],
        queryFn: () => getOrderDashboardSummary(query ?? {}),
        refetchOnWindowFocus: false,
    });

    return {
        isLoading: isLoading || isFetching,
        isError,
        data,
    };
};

// Hook para agentes de ventas
export const useDashboardOrdersAgents = (query: DashboardOrderQueryParams = {}) => {
    const { data, isLoading, isError, isFetching } = useQuery({
        queryKey: ["dashboard-orders-agents", query],
        queryFn: () => getOrderDashboardAgents(query ?? {}),
        refetchOnWindowFocus: false,
    });

    return {
        isLoading: isLoading || isFetching,
        isError,
        data,
    };
};

// Hook para analytics
export const useDashboardOrdersAnalytics = (query: DashboardOrderQueryParams = {}) => {
    const { data, isLoading, isError, isFetching } = useQuery({
        queryKey: ["dashboard-orders-analytics", query],
        queryFn: () => getOrderDashboardAnalytics(query ?? {}),
        refetchOnWindowFocus: false,
    });

    return {
        isLoading: isLoading || isFetching,
        isError,
        data,
    };
};

// Hook para análisis de fuentes de lead
export const useDashboardOrdersLeadSources = (
    query: DashboardOrderQueryParams = {},
) => {
    const { data, isLoading, isError, isFetching } = useQuery({
        queryKey: ["dashboard-orders-lead-sources", query],
        queryFn: () => getOrderDashboardLeadSources(query ?? {}),
        refetchOnWindowFocus: false,
    });

    return {
        isLoading: isLoading || isFetching,
        isError,
        data,
    };
};

// Hook para datos históricos
export const useDashboardOrdersHistorical = (query: DashboardOrderQueryParams = {}) => {
    const { data, isLoading, isError, isFetching } = useQuery({
        queryKey: ["dashboard-orders-historical", query],
        queryFn: () => getOrderDashboardHistorical(query ?? {}),
        refetchOnWindowFocus: false,
    });

    return {
        isLoading: isLoading || isFetching,
        isError,
        data,
    };
};

// Hook para productos
export const useDashboardOrdersProducts = (query: DashboardOrderQueryParams = {}) => {
    const { data, isLoading, isError, isFetching } = useQuery({
        queryKey: ["dashboard-orders-products", query],
        queryFn: () => getOrderDashboardProducts(query ?? {}),
        refetchOnWindowFocus: false,
    });

    return {
        isLoading: isLoading || isFetching,
        isError,
        data,
    };
};

// Hook para órdenes recientes
export const useDashboardOrdersRecentOrders = (
    query: DashboardOrderQueryParams = {},
) => {
    const { data, isLoading, isError, isFetching } = useQuery({
        queryKey: ["dashboard-orders-recent", query],
        queryFn: () => getOrderDashboardRecentOrders(query ?? {}),
        refetchOnWindowFocus: false,
    });

    return {
        isLoading: isLoading || isFetching,
        isError,
        data,
    };
};

export const useDashboardOrdersInvalidateCache = (options?: UseMutationOptions) => {
    const mutation = useMutation({
        mutationFn: () => invalidateOrderDashboardCache(),
        ...options,
    });

    return mutation;
};

// Función utilitaria para crear parámetros de query reutilizables
export const createDashboardOrdersQueryParams = (
    searchParams: URLSearchParams,
    defaultDateRange: [Dayjs, Dayjs],
): DashboardOrderQueryParams => {
    return {
        createdAtBefore:
            searchParams.get("createdAtBefore") ||
            defaultDateRange[1].format("YYYY-MM-DD"),
        createdAtAfter:
            searchParams.get("createdAtAfter") ||
            defaultDateRange[0].format("YYYY-MM-DD"),
        stages: searchParams.get("stages") || undefined,
        products: searchParams.get("products") || undefined,
        salesAgent: searchParams.get("salesAgent") || undefined,
        forceRefresh: searchParams.get("forceRefresh")
            ? searchParams.get("forceRefresh") === "true"
            : undefined,
        benefits: searchParams.get("benefits") || undefined,
    };
};
