import CrmLayout from "@/features/crm/layout";
import { use<PERSON>ara<PERSON>, <PERSON> } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { retrieveMajor } from "@/features/crm/services/portals/major";
import { Breadcrumb, Card, Descriptions, Typography } from "antd";

const { Title } = Typography;

export default function MajorDetailPage() {
    const { mid } = useParams();
    const id = mid as string;

    const { data: major, isLoading } = useQuery({
        queryKey: ["major", id],
        queryFn: () => retrieveMajor(id),
        enabled: !!id,
    });

    return (
        <CrmLayout>
            <div className="space-y-4">
                <Breadcrumb
                    items={[
                        { title: <Link to="/crm/majors">Carreras</Link> },
                        { title: major?.name || "Detalle" },
                    ]}
                />
                <Title level={3}>Carrera universitaria</Title>
                <Card loading={isLoading}>
                    {major && (
                        <Descriptions bordered column={1} size="middle">
                            <Descriptions.Item label="ID">
                                {major.mid}
                            </Descriptions.Item>
                            <Descriptions.Item label="Nombre">
                                {major.name}
                            </Descriptions.Item>
                        </Descriptions>
                    )}
                </Card>
            </div>
        </CrmLayout>
    );
}
