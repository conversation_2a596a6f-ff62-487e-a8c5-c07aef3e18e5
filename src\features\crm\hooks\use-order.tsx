import { useMutation, useQuery } from "@tanstack/react-query";
import {
    createOrder,
    listOrders,
    getOrdersByContact,
    retrieveOrder,
    ListOrdersQueryParams,
    deleteOrder,
    sendClassroomInvitations,
} from "../services/portals/order";
import { type AxiosError } from "axios";
import { CreateOrder, Order } from "../types/order";
import queryClient from "@lib/queryClient";

export const useOrders = (queryParams?: ListOrdersQueryParams) => {
    const { data, isLoading, isError } = useQuery({
        queryKey: ["orders", queryParams],
        queryFn: () => listOrders(queryParams),
    });

    const { count, results: orders } = data || {
        count: 0,
        results: [],
    };

    return {
        isLoading,
        isError,
        orders,
        count,
    };
};

export const useOrdersByContact = (contactId: string) => {
    const { data, isLoading, isError } = useQuery({
        queryKey: ["orders", "contact", contactId],
        queryFn: () => getOrdersByContact(contactId),
        enabled: !!contactId,
    });

    const { count, results: orders } = data || {
        count: 0,
        results: [],
    };

    return {
        isLoading,
        isError,
        orders,
        count,
    };
};

export const useOrder = (oid: string) => {
    const {
        data: order,
        isLoading,
        isError,
    } = useQuery({
        queryKey: ["order", oid],
        queryFn: () => retrieveOrder(oid),
        enabled: !!oid,
        refetchOnWindowFocus: false,
        retry: false,
    });

    return {
        isLoading,
        isError,
        order,
    };
};

type UseCreateOrderProps = {
    onCreateOrderSuccess?: (order: Order) => void;
    onCreateOrderError?: (error: AxiosError) => void;
};

export const useCreateOrder = ({
    onCreateOrderSuccess,
    onCreateOrderError,
}: UseCreateOrderProps = {}) => {
    return useMutation<Order, AxiosError, CreateOrder>({
        mutationFn: (newOrder: CreateOrder) => createOrder(newOrder),

        onSuccess: (data) => {
            onCreateOrderSuccess?.(data);
            queryClient.invalidateQueries({ queryKey: ["orders"] });
        },
        onError: (error) => {
            console.error("Error creating order:", error);
            onCreateOrderError?.(error);
        },
    });
};

type useDeleteOrderProps = {
    onDeleteOrderSuccess?: () => void;
    onDeleteOrderError?: () => void;
};

export const useDeleteOrder = ({
    onDeleteOrderSuccess,
    onDeleteOrderError,
}: useDeleteOrderProps = {}) => {
    return useMutation<unknown, AxiosError, { oid: string }>({
        mutationFn: async ({ oid }) => {
            await deleteOrder(oid);
        },

        onSuccess: (_, variables) => {
            queryClient.invalidateQueries({ queryKey: ["orders"] });
            queryClient.invalidateQueries({ queryKey: ["order", variables.oid] });
            onDeleteOrderSuccess?.();
        },
        onError: () => {
            onDeleteOrderError?.();
        },
    });
};

type UseSendClassroomInvitationsProps = {
    onSendInvitationsSuccess?: () => void;
    onSendInvitationsError?: (error: AxiosError) => void;
};

export const useSendClassroomInvitations = ({
    onSendInvitationsSuccess,
    onSendInvitationsError,
}: UseSendClassroomInvitationsProps = {}) => {
    return useMutation<void, AxiosError, { oid: string }>({
        mutationFn: async ({ oid }) => {
            await sendClassroomInvitations(oid);
        },
        onSuccess: (_, variables) => {
            onSendInvitationsSuccess?.();
            queryClient.invalidateQueries({ queryKey: ["order", variables.oid] });
        },
        onError: (error) => {
            onSendInvitationsError?.(error);
        },
    });
};
