import { z } from "zod";
import { DEFAULT_DATE_FORMAT } from "@lib/dayjs-helpers";
import { getOptionsFromEnum } from "@lib/filters";
import type { FilterPanelItem } from "@myTypes/filter";
import { InstructorStatusEnum } from "@myTypes/instructor";

/** Query filter schema for API */
export const instructorQueryFilterSchema = z
    .object({
        search: z.string(),
        status: z.string(),
        // Only for filters of type "rangepicker"
        createdAtBefore: z.string(),
        createdAtAfter: z.string(),
    })
    .partial();

export const instructorPanelFilterSchema = z
    .object({
        status: z.nativeEnum(InstructorStatusEnum),
        createdAt: z.array(z.any().optional(), z.any().optional()), // [after, before]
    })
    .partial();

/** Filter schea type for filter panel form */
export type instructorQueryFilterSchema = z.infer<typeof instructorQueryFilterSchema>;
export type InstructorPanelFilterSchema = z.infer<typeof instructorPanelFilterSchema>;

export const filters: FilterPanelItem<InstructorPanelFilterSchema>[] = [
    {
        name: "status",
        label: "Estado",
        type: "select",
        selectprops: {
            options: getOptionsFromEnum(InstructorStatusEnum),
        },
    },
    {
        name: "createdAt",
        label: "Fecha de creación",
        type: "rangepicker",
        datepickerprops: {
            format: DEFAULT_DATE_FORMAT,
            className: "w-full",
        },
    },
];
