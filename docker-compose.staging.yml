services:
  erp-app:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - VITE_API_BASE_URL=${VITE_API_BASE_URL}
        - VITE_BUCKET_URL=${VITE_BUCKET_URL}
    ports:
      - "3001:80"
    restart: always
    volumes:
      - static_content:/usr/share/nginx/html
    networks:
      - ceu-staging-network

volumes:
  static_content:

networks:
  ceu-staging-network:
    external: true