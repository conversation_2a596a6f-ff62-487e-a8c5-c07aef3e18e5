/* Blog Preview Styles */

/*
    Variables
*/
:root {
    --heading-color: #1483ff;
    --paragraph-color: #374151;
}

/* Main container */
.blog-preview {
    @apply leading-none w-full max-w-6xl mx-auto bg-white-full rounded-lg overflow-hidden shadow-lg;
}

/* Header with blue gradient background */
.blog-preview-header {
    @apply rounded-lg bg-gradient-to-r from-blue-full to-[#40AAFF] text-white-full space-y-5 p-12;
}

.blog-preview-date {
    @apply text-sm font-medium mb-4 capitalize;
}

.blog-preview-title {
    @apply text-3xl md:text-5xl font-semibold;
}

/* Content container */
.blog-preview-container {
    @apply p-6 md:p-8;
}

/* Table of contents */

.blog-preview-toc-section {
    @apply grid md:grid-cols-4;
}

.blog-preview-toc-aside {
    @apply h-fit w-full min-w-[100vw] md:min-w-0  md:col-span-1 sticky top-0;
}

.blog-preview-toc-container {
    @apply mx-4 my-4 w-full z-10;
}

.blog-preview-toc-title {
    @apply text-lg font-semibold mb-2 text-gray-800;
}

.blog-toc-list {
    @apply list-none pl-0;
}

.blog-toc-item {
    @apply cursor-pointer py-2 px-8 w-full border-l-[2px] hover:border-[#1483ff] hover:bg-blue-100 hover:font-medium hover:text-[#1483ff];
    color: var(--paragraph-color);
}

.blog-toc-level-1 {
    @apply pl-2 text-sm font-medium;
}

.blog-toc-level-2 {
    @apply pl-4 text-sm;
}

.blog-toc-level-3 {
    @apply pl-8 text-xs;
}

/* Summary section */
.blog-preview-summary {
    @apply relative py-6 text-right italic border-t-[1px] border-b-[1px] text-[#848484];
}

.blog-preview-summary-text {
    @apply text-sm;
}

/* Headings */
.blog-content-h1 {
    font-size: clamp(
        2rem,
        5vw,
        3rem
    ); /* Mínimo 32px, preferido 5% del ancho de la pantalla, máximo 48px */
    @apply font-bold mt-8 mb-4;
    color: var(--heading-color);
}

.blog-content-h2 {
    font-size: clamp(
        1.5rem,
        4vw,
        2rem
    ); /* Mínimo 24px, preferido 4% del ancho de la pantalla, máximo 32px */
    @apply font-semibold mt-6 mb-3;
    color: var(--heading-color);
}

.blog-content-h3 {
    font-size: clamp(
        1.25rem,
        3vw,
        1.5rem
    ); /* Mínimo 20px, preferido 3% del ancho de la pantalla, máximo 24px */
    @apply font-medium mt-4 mb-2;
    color: var(--heading-color);
}

/* Paragraphs */
.blog-content-paragraph {
    @apply leading-relaxed my-4;
    color: var(--paragraph-color);
}

/* Lists */
.blog-content-ul,
.blog-content-ol {
    @apply pl-6 space-y-1;
    color: var(--paragraph-color);
}

.blog-content-ul {
    @apply list-disc;
}

.blog-content-ol {
    @apply list-decimal;
}

.blog-content-li {
    @apply leading-relaxed;
}

/* Blockquotes */
.blog-content-blockquote {
    @apply leading-relaxed bg-blue-50 my-4 p-6 rounded-lg border-l-4 border-[#1483ff];
    color: var(--paragraph-color);
}

/* Images */
.blog-content-figure {
    @apply my-6 mx-auto;
}

.blog-content-image {
    @apply rounded-md shadow-md max-w-full mx-auto;
}

.blog-content-caption {
    @apply text-sm text-center text-gray-500 mt-2 italic;
}

/* Divider */
.blog-content-divider {
    @apply my-8 border-t border-gray-200;
}

/* Inline formatting */
.blog-content-code {
    @apply bg-gray-100 text-red-600 px-1 py-0.5 rounded text-sm font-mono;
}

.blog-content-link {
    @apply text-blue-600 hover:text-blue-800 underline;
}
