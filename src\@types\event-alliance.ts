export interface EventAlliance {
    rowNumber: string;
    iDEventoAlianza: string;
    iDEventoHorario: string;
    iDAlianza: string;
    googleFormLinkView: string;
    googleFormLinkEdit: string;
    googleFormId: string;
    googleSheetLink: string;
    deleted: string;
    label: string;
    alianza: string;
    brochureEnlace: string;
    textoCorreo: string;
    evento: string;
    tipoEvento: string;
    textoPresentaciónForm: string;
    textoFinalForm: string;
    día: string;
    horario: string;
    iDEvento: string;
    horaInicio: string;
    relatedInscripciónAEventos: string;
    googleMeetLink: string;
    iDCurso: string;
    estado: string;
}

export interface EventAllianceRegistration {
    id: string;
    whatsApp: string;
    name: string;
}
