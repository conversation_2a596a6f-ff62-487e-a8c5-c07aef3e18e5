import React from "react";
import { Card, Empty } from "antd";
import {
    Responsive<PERSON>ontaine<PERSON>,
    <PERSON><PERSON>hart,
    Bar,
    XAxis,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON>,
    CartesianGrid,
} from "recharts";
import { Radio } from "lucide-react";
import type { DiffusionChannel } from "@/features/crm/types/dashboard/events";

interface DiffusionChannelsChartProps {
    data?: DiffusionChannel[];
    isLoading?: boolean;
}

interface CustomTooltipProps {
    active?: boolean;
    payload?: Array<{
        color: string;
        name: string;
        value: string | number;
        dataKey: string;
    }>;
    label?: string;
}

const COLORS = {
    total: "#1890ff",
    hasContact: "#52c41a",
    needsConciliation: "#faad14",
    alreadyLead: "#722ed1",
};

const DiffusionChannelsChart: React.FC<DiffusionChannelsChartProps> = ({
    data = [],
    isLoading = false,
}) => {
    const CustomTooltip = ({ active, payload, label }: CustomTooltipProps) => {
        if (active && payload && payload.length) {
            return (
                <div
                    style={{
                        backgroundColor: "white",
                        padding: "12px",
                        border: "1px solid #d9d9d9",
                        borderRadius: "8px",
                        boxShadow:
                            "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
                        minWidth: "200px",
                        zIndex: 1000,
                    }}
                >
                    <p
                        style={{
                            fontWeight: 600,
                            color: "#262626",
                            marginBottom: "8px",
                            fontSize: "14px",
                        }}
                    >
                        {`Canal: ${label}`}
                    </p>
                    <div style={{ marginBottom: "8px" }}>
                        {payload.map((entry, index) => (
                            <p
                                key={index}
                                style={{ fontSize: "12px", marginBottom: "2px" }}
                            >
                                <span style={{ color: entry.color, fontWeight: 500 }}>
                                    {getMetricLabel(entry.dataKey)}:
                                </span>
                                <span
                                    style={{
                                        fontWeight: 500,
                                        color: "#262626",
                                        marginLeft: "4px",
                                    }}
                                >
                                    {entry.value.toLocaleString()}
                                </span>
                            </p>
                        ))}
                    </div>
                </div>
            );
        }
        return null;
    };

    const getMetricLabel = (dataKey: string) => {
        const labels: Record<string, string> = {
            total: "Total",
            hasContact: "Con Contacto",
            needsConciliation: "Necesitan Conciliación",
            alreadyLead: "Ya son Leads",
        };
        return labels[dataKey] || dataKey;
    };

    return (
        <Card
            title={
                <div className="flex items-center gap-2">
                    <Radio size={20} className="text-blue-500" />
                    <span>Canales de Difusión</span>
                </div>
            }
            className="shadow-md h-full"
        >
            <div className="h-80">
                {data.length && !isLoading ? (
                    <ResponsiveContainer width="100%" height="100%">
                        <BarChart
                            data={data}
                            margin={{
                                top: 20,
                                right: 30,
                                left: 20,
                                bottom: 5,
                            }}
                            maxBarSize={60}
                        >
                            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                            <XAxis
                                dataKey="channel"
                                tick={{ fontSize: 12 }}
                                stroke="#666"
                                angle={-45}
                                textAnchor="end"
                                height={80}
                            />
                            <YAxis tick={{ fontSize: 12 }} stroke="#666" />
                            <Tooltip content={<CustomTooltip />} />
                            <Legend formatter={(value) => getMetricLabel(value)} />
                            <Bar
                                dataKey="total"
                                fill={COLORS.total}
                                name="total"
                                radius={[2, 2, 0, 0]}
                            />
                            <Bar
                                dataKey="hasContact"
                                fill={COLORS.hasContact}
                                name="hasContact"
                                radius={[2, 2, 0, 0]}
                            />
                            <Bar
                                dataKey="needsConciliation"
                                fill={COLORS.needsConciliation}
                                name="needsConciliation"
                                radius={[2, 2, 0, 0]}
                            />
                            <Bar
                                dataKey="alreadyLead"
                                fill={COLORS.alreadyLead}
                                name="alreadyLead"
                                radius={[2, 2, 0, 0]}
                            />
                        </BarChart>
                    </ResponsiveContainer>
                ) : (
                    <div className="h-full flex items-center justify-center">
                        <Empty description="No hay datos disponibles" />
                    </div>
                )}
            </div>
        </Card>
    );
};

export default DiffusionChannelsChart;
