import { useContext } from "react";
import ResponsivePreviewContext from "./context";

export type ViewportSize = "desktop" | "tablet" | "mobile";

export interface ViewportConfig {
    name: string;
    width: number;
    height: number;
    breakpoint: string;
}

export const VIEWPORT_CONFIGS: Record<ViewportSize, ViewportConfig> = {
    desktop: {
        name: "Escritorio",
        width: 1200,
        height: 800,
        breakpoint: "lg",
    },
    tablet: {
        name: "Tablet",
        width: 768,
        height: 1024,
        breakpoint: "md",
    },
    mobile: {
        name: "<PERSON><PERSON><PERSON>",
        width: 375,
        height: 667,
        breakpoint: "sm",
    },
};

export function useResponsivePreviewContext() {
    const context = useContext(ResponsivePreviewContext);
    if (context === undefined) {
        throw new Error(
            "useResponsivePreview must be used within a ResponsivePreviewProvider",
        );
    }
    return context;
}
