import { useMemo } from "react";
import { useAuthStore } from "@store/authStore";
import { PermissionKey, PermissionAction } from "@/core/config/permissions-map";
import type { MenuProps } from "antd";
import {
    hasPermission,
    canView,
    canAdd,
    canChange,
    canDelete,
    hasAnyPermission,
    hasAllPermissions,
} from "@lib/permissions";

/**
 * Hook genérico para verificar un permiso con acción específica
 * @param permission - Clave del permiso a verificar
 * @param action - Acción a verificar (view, add, change, delete)
 * @returns boolean indicando si el usuario tiene el permiso
 */
export const useHasPermission = (
    permission: PermissionKey,
    action: PermissionAction = "view",
): boolean => {
    const { user, isAuthenticated, isHydrated } = useAuthStore((state) => state);

    return useMemo(() => {
        if (!isAuthenticated || !isHydrated) {
            return false;
        }

        const userPermissions = user?.permissions || [];
        return hasPermission(userPermissions, permission, action);
    }, [user, permission, action, isAuthenticated, isHydrated]);
};

/**
 * Hook específico para verificar permisos de vista (más común)
 * @param permission - Clave del permiso a verificar
 * @returns boolean indicando si el usuario puede ver el recurso
 */
export const useCanView = (permission: PermissionKey): boolean => {
    const { user, isAuthenticated, isHydrated } = useAuthStore((state) => state);

    return useMemo(() => {
        if (!isAuthenticated || !isHydrated) {
            return false;
        }

        const userPermissions = user?.permissions || [];
        return canView(userPermissions, permission);
    }, [user, permission, isAuthenticated, isHydrated]);
};

/**
 * Hook específico para verificar permisos de creación
 * @param permission - Clave del permiso a verificar
 * @returns boolean indicando si el usuario puede crear el recurso
 */
export const useCanAdd = (permission: PermissionKey): boolean => {
    const { user, isAuthenticated, isHydrated } = useAuthStore((state) => state);

    return useMemo(() => {
        if (!isAuthenticated || !isHydrated) {
            return false;
        }

        const userPermissions = user?.permissions || [];
        return canAdd(userPermissions, permission);
    }, [user, permission, isAuthenticated, isHydrated]);
};

/**
 * Hook específico para verificar permisos de edición
 * @param permission - Clave del permiso a verificar
 * @returns boolean indicando si el usuario puede editar el recurso
 */
export const useCanChange = (permission: PermissionKey): boolean => {
    const { user, isAuthenticated, isHydrated } = useAuthStore((state) => state);

    return useMemo(() => {
        if (!isAuthenticated || !isHydrated) {
            return false;
        }

        const userPermissions = user?.permissions || [];
        return canChange(userPermissions, permission);
    }, [user, permission, isAuthenticated, isHydrated]);
};

/**
 * Hook específico para verificar permisos de eliminación
 * @param permission - Clave del permiso a verificar
 * @returns boolean indicando si el usuario puede eliminar el recurso
 */
export const useCanDelete = (permission: PermissionKey): boolean => {
    const { user, isAuthenticated, isHydrated } = useAuthStore((state) => state);

    return useMemo(() => {
        if (!isAuthenticated || !isHydrated) {
            return false;
        }

        const userPermissions = user?.permissions || [];
        return canDelete(userPermissions, permission);
    }, [user, permission, isAuthenticated, isHydrated]);
};

/**
 * Hook para verificar múltiples permisos con acción específica
 * @param permissions - Array de permisos a verificar
 * @param action - Acción a verificar para todos los permisos
 * @returns Objeto con el estado de cada permiso
 */
export const useHasPermissions = (
    permissions: PermissionKey[],
    action: PermissionAction = "view",
): Record<PermissionKey, boolean> => {
    const { user, isAuthenticated, isHydrated } = useAuthStore((state) => state);

    return useMemo(() => {
        if (!isAuthenticated || !isHydrated) {
            return {} as Record<PermissionKey, boolean>;
        }

        const userPermissions = user?.permissions || [];

        return permissions.reduce(
            (acc, permission) => {
                acc[permission] = hasPermission(userPermissions, permission, action);
                return acc;
            },
            {} as Record<PermissionKey, boolean>,
        );
    }, [user, permissions, action, isAuthenticated, isHydrated]);
};

/**
 * Hook para verificar si tiene alguno de los permisos especificados
 * @param permission - Permiso a verificar
 * @param actions - Array de acciones a verificar
 * @returns boolean indicando si tiene al menos una de las acciones
 */
export const useHasAnyPermission = (
    permission: PermissionKey,
    actions: PermissionAction[] = ["view"],
): boolean => {
    const { user, isAuthenticated, isHydrated } = useAuthStore((state) => state);

    return useMemo(() => {
        if (!isAuthenticated || !isHydrated) {
            return false;
        }

        const userPermissions = user?.permissions || [];
        return hasAnyPermission(userPermissions, permission, actions);
    }, [user, permission, actions, isAuthenticated, isHydrated]);
};

/**
 * Hook para verificar si tiene todos los permisos especificados
 * @param permission - Permiso a verificar
 * @param actions - Array de acciones que debe tener todas
 * @returns boolean indicando si tiene todas las acciones
 */
export const useHasAllPermissions = (
    permission: PermissionKey,
    actions: PermissionAction[] = ["view"],
): boolean => {
    const { user, isAuthenticated, isHydrated } = useAuthStore((state) => state);

    return useMemo(() => {
        if (!isAuthenticated || !isHydrated) {
            return false;
        }

        const userPermissions = user?.permissions || [];
        return hasAllPermissions(userPermissions, permission, actions);
    }, [user, permission, actions, isAuthenticated, isHydrated]);
};

/**
 * Hook para obtener un objeto con todos los permisos CRUD de un recurso
 * @param permission - Permiso a verificar
 * @returns Objeto con can{View|Add|Change|Delete}
 */
export const useCrudPermissions = (permission: PermissionKey) => {
    const { user, isAuthenticated, isHydrated } = useAuthStore((state) => state);

    return useMemo(() => {
        if (!isAuthenticated || !isHydrated) {
            return {
                canView: false,
                canAdd: false,
                canChange: false,
                canDelete: false,
            };
        }

        const userPermissions = user?.permissions || [];

        return {
            canView: canView(userPermissions, permission),
            canAdd: canAdd(userPermissions, permission),
            canChange: canChange(userPermissions, permission),
            canDelete: canDelete(userPermissions, permission),
        };
    }, [user, permission, isAuthenticated, isHydrated]);
};

// Handle sidebar menu items with permissions

type MenuItem = Required<MenuProps>["items"][number];

export interface MenuItemWithPermission {
    key?: React.Key | null;
    label?: React.ReactNode;
    title?: string;
    icon?: React.ReactNode;
    danger?: boolean;
    disabled?: boolean;
    style?: React.CSSProperties;
    className?: string;
    type?: "group" | "divider" | "item";
    requiredPermission?: PermissionKey;
    children?: MenuItemWithPermission[];
}

/**
 * Hook para filtrar elementos de menú basándose en permisos de vista del usuario
 * Solo muestra elementos que el usuario puede ver (action: "view")
 * @param menuItems - Array de elementos de menú con permisos opcionales
 * @returns Array de elementos de menú filtrados según los permisos de vista del usuario
 */
export const useMenuViewFilter = (menuItems: MenuItemWithPermission[]): MenuItem[] => {
    const { user } = useAuthStore((state) => state);

    return useMemo(() => {
        const userPermissions = user?.permissions || [];
        const filterMenuItems = (items: MenuItemWithPermission[]): MenuItem[] => {
            return items
                .filter((item) => {
                    // Si no tiene permiso requerido, siempre se muestra
                    if (!item.requiredPermission) {
                        return true;
                    }

                    // Verificar si el usuario tiene el permiso requerido
                    return canView(userPermissions, item.requiredPermission);
                })
                .map((item) => {
                    // Si el item tiene children, filtrarlos recursivamente
                    if (item.children && item.children.length > 0) {
                        const filteredChildren = filterMenuItems(item.children);

                        // Si es un grupo y no tiene children después del filtrado, no lo mostramos
                        if (item.type === "group" && filteredChildren.length === 0) {
                            return null;
                        }

                        return {
                            ...item,
                            children: filteredChildren,
                        } as MenuItem;
                    }

                    // Remover la propiedad requiredPermission del resultado final
                    delete item.requiredPermission;

                    return item as MenuItem;
                })
                .filter(Boolean) as MenuItem[];
        };

        return filterMenuItems(menuItems);
    }, [menuItems, user]);
};
