import { useQuery } from "@tanstack/react-query";
import { listPartnerships } from "../services/portals/partnership";

export const usePartnerships = () => {
    const { data, isLoading, isError } = useQuery({
        queryKey: ["partnerships"],
        queryFn: () => listPartnerships(),
        refetchOnWindowFocus: false,
    });

    const { count, results: partnerships } = data || {
        count: 0,
        results: [],
    };

    return {
        isLoading,
        isError,
        partnerships,
        count,
    };
};
