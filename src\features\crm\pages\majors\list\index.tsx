import CrmLayout from "@/features/crm/layout";
import WelcomeBar from "@components/shared/molecules/WelcomeBar";
import { Badge, Input, Modal, Pagination, Typography, Form, Button, App } from "antd";
import { useState } from "react";
import { useSearchParams } from "react-router-dom";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
    deleteMajor,
    getMajors,
    ListMajorsQueryParams,
} from "@/features/crm/services/portals/major";
import MajorsTable from "@/features/crm/components/organisms/majors-table";
import { Plus } from "lucide-react";
import type { Major } from "@/features/crm/types/major";
import CreateMajorForm from "@/features/crm/components/molecules/create-major-form";
import EditMajorForm from "@/features/crm/components/molecules/edit-major-form";

const { Search } = Input;
const { Text } = Typography;

const DEFAULT_PAGE = 1;
const DEFAULT_PAGE_SIZE = 10;

export default function MajorsListPage() {
    const [searchParams, setSearchParams] = useSearchParams();
    const [isCreateOpen, setIsCreateOpen] = useState(false);
    const [isEditOpen, setIsEditOpen] = useState(false);
    const [editingMajor, setEditingMajor] = useState<Major | null>(null);
    const [form] = Form.useForm();
    const [editForm] = Form.useForm();
    const queryClient = useQueryClient();
    const { notification } = App.useApp();

    const page = Number(searchParams.get("page")) || DEFAULT_PAGE;
    const pageSize = Number(searchParams.get("pageSize")) || DEFAULT_PAGE_SIZE;
    const search = searchParams.get("search") || "";

    const queryParams: ListMajorsQueryParams = {
        page,
        pageSize,
        ordering: "name",
        ...(search ? { search } : {}),
    };

    const { data, isLoading } = useQuery({
        queryKey: ["majors", queryParams],
        queryFn: async () => getMajors(queryParams),
        // Evitar refetch al volver al foco (convención del proyecto)
        refetchOnWindowFocus: false,
    });

    const { count = 0, results: majors = [] } = data || {};

    const handleSetPage = (p: number, ps: number) => {
        setSearchParams((prev: URLSearchParams) => {
            prev.set("page", String(p));
            prev.set("pageSize", String(ps));
            return prev;
        });
    };

    const onSearch = (value: string) => {
        setSearchParams((prev: URLSearchParams) => {
            if (value) prev.set("search", value);
            else prev.delete("search");
            prev.set("page", "1");
            return prev;
        });
    };

    const handleEdit = (major: Major) => {
        setEditingMajor(major);
        editForm.setFieldsValue({ name: major.name });
        setIsEditOpen(true);
    };

    const deleteMutation = useMutation({
        mutationFn: (mid: string) => deleteMajor(mid),
        onSuccess: () => {
            notification.success({ message: "Carrera eliminada" });
            queryClient.invalidateQueries({ queryKey: ["majors"] });
        },
        onError: () => {
            notification.error({ message: "No se pudo eliminar la carrera" });
        },
    });

    const handleDelete = (major: Major) => {
        Modal.confirm({
            title: "Eliminar carrera",
            content: `¿Seguro que deseas eliminar "${major.name}"? Esta acción no se puede deshacer`,
            okText: "Eliminar",
            okButtonProps: { danger: true },
            cancelText: "Cancelar",
            onOk: () => deleteMutation.mutate(major.mid),
        });
    };

    return (
        <CrmLayout>
            <div className="w-full h-full space-y-5">
                <div className="flex justify-between items-center">
                    <WelcomeBar helperText="Gestiona aquí las Carreras Universitarias (Majors)" />
                    <div className="flex gap-3">
                        <Button
                            type="primary"
                            size="large"
                            icon={<Plus />}
                            onClick={() => setIsCreateOpen(true)}
                        >
                            Agregar
                        </Button>
                    </div>
                </div>
                <Modal
                    centered
                    open={isCreateOpen}
                    onCancel={() => {
                        setIsCreateOpen(false);
                        form.resetFields();
                    }}
                    footer={false}
                    title={"Agregar Carrera Universitaria"}
                >
                    <CreateMajorForm
                        form={form}
                        onClose={() => {
                            setIsCreateOpen(false);
                            form.resetFields();
                        }}
                        onCreated={() =>
                            queryClient.invalidateQueries({ queryKey: ["majors"] })
                        }
                        notify={(type, message, description) =>
                            notification[type]({ message, description })
                        }
                    />
                </Modal>

                <Modal
                    centered
                    open={isEditOpen}
                    onCancel={() => {
                        setIsEditOpen(false);
                        setEditingMajor(null);
                        editForm.resetFields();
                    }}
                    footer={false}
                    title={"Editar Carrera Universitaria"}
                >
                    <EditMajorForm
                        form={editForm}
                        major={editingMajor}
                        onClose={() => {
                            setIsEditOpen(false);
                            setEditingMajor(null);
                            editForm.resetFields();
                        }}
                        onUpdated={() =>
                            queryClient.invalidateQueries({ queryKey: ["majors"] })
                        }
                        notify={(type, message, description) =>
                            notification[type]({ message, description })
                        }
                    />
                </Modal>

                <div className="p-5 bg-white-full rounded-lg space-y-5">
                    <div className="flex flex-col lg:flex-row justify-between items-center gap-3">
                        <Text className="text-black-medium text-2xl font-semibold">
                            Carreras Universitarias <Badge count={count} color="blue" />
                        </Text>
                        <Search
                            size="large"
                            placeholder="Buscar por nombre"
                            defaultValue={search}
                            key={search}
                            onSearch={onSearch}
                            enterButton
                            allowClear
                            className="max-w-screen-sm"
                        />
                    </div>
                </div>

                <MajorsTable
                    data={majors}
                    loading={isLoading}
                    onEdit={handleEdit}
                    onDelete={handleDelete}
                />

                <div className="flex justify-between items-center p-4 bg-white-full rounded-lg shadow-sm">
                    <Text type="secondary">
                        {majors.length} de {count} carreras
                    </Text>
                    <Pagination
                        current={page}
                        pageSize={pageSize}
                        total={count}
                        onChange={handleSetPage}
                        showSizeChanger
                    />
                </div>
            </div>
        </CrmLayout>
    );
}
