import { App } from "antd";
import type { AxiosError } from "axios";
import { extractErrorMessages } from "@lib/error-helpers";
import { ErroNotificationConfig, openErrorNotification } from "@lib/notification";
import {
    HTTP_400_BAD_REQUEST,
    HTTP_403_FORBIDDEN,
    HTTP_404_NOT_FOUND,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_429_TOO_MANY_REQUESTS,
    HTTP_500_INTERNAL_SERVER_ERROR,
} from "@lib/constants/httpCodes";

interface ApiErrorConfig {
    /**
     * Título del error que se mostrará en la notificación
     */
    title: string;
    /**
     * Mensaje genérico para errores 500 (por defecto: "Ha ocurrido un error inesperado")
     */
    genericMessage?: string;
    /**
     * Mensaje genérico para errores 403 (por defecto: "No tienes permisos para realizar esta acción")
     */
    forbiddenMessage?: string;
    /**
     * Mensaje genérico para errores 404 (por defecto: "El recurso solicitado no fue encontrado")
     */
    notFoundMessage?: string;
    /**
     * Mensaje genérico para errores de red (por defecto: "Error de conexión. Verifica tu conexión a internet")
     */
    networkErrorMessage?: string;
    /**
     * Si se debe mostrar automáticamente la notificación (por defecto: true)
     */
    autoShow?: boolean;
}

interface UseApiErrorReturn {
    /**
     * Función para manejar errores de API
     */
    handleError: (error: AxiosError, config?: Partial<ApiErrorConfig>) => string[];
    /**
     * Función para mostrar manualmente una notificación de error
     */
    showError: (title: string, messages: string | string[]) => void;
}

/**
 * Hook para manejar errores de API de manera consistente
 *
 * @param defaultConfig - Configuración por defecto para el manejo de errores
 * @returns Objeto con funciones para manejar y mostrar errores
 *
 * @example
 * ```tsx
 * const { handleError } = useApiError({
 *   title: "Error al crear usuario",
 *   genericMessage: "No se pudo crear el usuario"
 * });
 *
 * const mutation = useMutation({
 *   mutationFn: createUser,
 *   onError: (error: AxiosError) => {
 *     handleError(error);
 *   }
 * });
 * ```
 */
export const useApiError = (defaultConfig?: ApiErrorConfig): UseApiErrorReturn => {
    const { notification } = App.useApp();

    const getErrorMessage = (error: AxiosError, config: ApiErrorConfig): string[] => {
        const status = error.response?.status;

        // Errores de red
        if (error.code === "ERR_NETWORK") {
            return [
                config.networkErrorMessage ||
                    "Error de conexión. Verifica tu conexión a internet",
            ];
        }

        const extractedMessages: string[] = extractErrorMessages(
            error.response?.data || {},
        ).filter((msg) => typeof msg === "string");

        switch (status) {
            case HTTP_500_INTERNAL_SERVER_ERROR:
                return [config.genericMessage || "Ha ocurrido un error inesperado"];

            case HTTP_403_FORBIDDEN:
                return [
                    config.forbiddenMessage ||
                        "No tienes permisos para realizar esta acción",
                ];

            case HTTP_404_NOT_FOUND:
                return [
                    config.notFoundMessage || "El recurso solicitado no fue encontrado",
                ];

            case HTTP_400_BAD_REQUEST:
            case HTTP_422_UNPROCESSABLE_ENTITY: {
                // Para errores de validación, extraer mensajes del backend
                return extractedMessages.length > 0
                    ? extractedMessages
                    : [config.genericMessage || "Ha ocurrido un error inesperado"];
            }

            case HTTP_429_TOO_MANY_REQUESTS: {
                return extractedMessages.length > 0
                    ? extractedMessages
                    : [
                          config.genericMessage ||
                              "Has realizado demasiadas solicitudes. Por favor, inténtalo de nuevo más tarde.",
                      ];
            }

            default: {
                // Para otros códigos de estado, intentar extraer mensajes
                return extractedMessages.length > 0
                    ? extractedMessages
                    : [config.genericMessage || "Ha ocurrido un error inesperado"];
            }
        }
    };

    // Configuración de la tarjeta de notificación (ej: mensaje detalle, color según HTTP status code, etc.)
    const getNotificationConfig = (error: AxiosError): ErroNotificationConfig => {
        const status = error.response?.status;

        switch (status) {
            case HTTP_429_TOO_MANY_REQUESTS: {
                return {
                    messageDescription: "Has realizado demasiadas solicitudes",
                };
            }

            default: {
                return {};
            }
        }
    };

    const handleError = (
        error: AxiosError,
        overrideConfig?: Partial<ApiErrorConfig>,
    ): string[] => {
        const config: ApiErrorConfig = {
            title: "Error",
            autoShow: true,
            ...defaultConfig,
            ...overrideConfig,
        };

        const errorMessages = getErrorMessage(error, config);
        const notificationConfig = getNotificationConfig(error);

        // Mostrar notificación automáticamente si está habilitado
        if (config.autoShow) {
            openErrorNotification(config.title, errorMessages, notification, {
                ...notificationConfig,
            });
        }

        return errorMessages;
    };

    const showError = (title: string, messages: string | string[]): void => {
        openErrorNotification(title, messages, notification);
    };

    return {
        handleError,
        showError,
    };
};
