import { UploadFile } from "antd";

export enum InstructorStatusEnum {
    Published = "Published",
    Draft = "Draft",
}

export type InstructorProfilePhoto = {
    /**
     * Unique UUID identifier for the instructor profile photo
     * @example 123e4567-e89b-12d3-a456-************
     * @format uuid
     */
    fid: string;

    /** File name of the instructor profile photo
     */
    name: string;

    /** URL of the instructor profile photo
     */
    url: string;
};

export type Instructor = {
    /**
     * Unique identifier for the instructor
     */
    iid: string;

    /**
     * Unique key identifier for the instructor based on the iid
     */
    key: string;

    /**
     * Full name of the instructor
     */
    fullName: string;
    /**
     * Optional biography of the instructor
     */
    biography?: string;

    /**
     * Optional title of the instructor (e.g., Professor, Dr.)
     */
    title?: string;

    /**
     * Optional information to highlight (e.g., achievements)
     */
    highlightedInfo?: string;

    /**
     * Optional Facebook profile URL
     */
    facebookUrl?: string;

    /**
     * Optional LinkedIn profile URL
     */
    linkedinUrl?: string;

    /**
     * Optional Instagram profile URL
     */
    instagramUrl?: string;

    /**
     * Optional profile photo URL
     */
    profilePhoto?: InstructorProfilePhoto | null;

    /**
     * Current status of the instructor (Published or Draft)
     */
    status: InstructorStatusEnum;

    /**
     * Order for the instructor
     */
    order: number;
};

export type RetrieveInstructor = Instructor;

export type CreateInstructor = Partial<
    Omit<Instructor, "iid" | "key" | "profilePhoto">
> & {
    profilePhotoFile: [UploadFile];
};

export type UpdateInstructor = Instructor & {
    profilePhotoFile?: [UploadFile];
};
