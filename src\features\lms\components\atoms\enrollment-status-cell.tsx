import { Tag, Tooltip } from "antd";
import { CheckCircle, XCircle, Award } from "lucide-react";

interface EnrollmentStatusCellProps {
    isActive: boolean;
    certificateIssued: boolean;
}

export default function EnrollmentStatusCell({
    isActive,
    certificateIssued,
}: EnrollmentStatusCellProps) {
    return (
        <div className="space-y-2">
            <div>
                <Tooltip title={isActive ? "Matrícula activa" : "Matrícula inactiva"}>
                    <Tag
                        className="flex items-center gap-1 w-fit"
                        color={isActive ? "green" : "red"}
                        icon={
                            isActive ? <CheckCircle size={14} /> : <XCircle size={14} />
                        }
                    >
                        {isActive ? "Activo" : "Inactivo"}
                    </Tag>
                </Tooltip>
            </div>
            <div>
                <Tooltip
                    title={
                        certificateIssued
                            ? "Certificado emitido"
                            : "Certificado pendiente"
                    }
                >
                    <Tag
                        className="flex items-center gap-1 w-fit"
                        color={certificateIssued ? "blue" : "orange"}
                        icon={<Award size={14} />}
                    >
                        {certificateIssued ? "Certificado" : "Pendiente"}
                    </Tag>
                </Tooltip>
            </div>
        </div>
    );
}
