import BlogPreview from "@components/cms/organisms/BlogPreview";
import Spinner from "@components/shared/atoms/Spinner";
import { retrieveBlogPost } from "@services/portals/cms/blogs/post";
import { useQuery } from "@tanstack/react-query";
import { Button } from "antd";
import { useNavigate, useParams } from "react-router-dom";
import ArrowDown from "@assets/icons/huge/arrow-down.svg?react";
import Reload from "@assets/icons/huge/reload.svg?react";
import NotFoundPage from "../NotFoundPage";

export default function BlogPreviewPage() {
    const { bid } = useParams<{ bid: string }>();
    const navigate = useNavigate();
    const {
        data: blog,
        isLoading,
        refetch,
    } = useQuery({
        queryKey: ["blog", bid],
        queryFn: () => retrieveBlogPost(bid),
        enabled: !!bid,
        refetchOnWindowFocus: false,
    });

    if (isLoading)
        return (
            <div className="grid place-content-center w-screen h-screen">
                <Spinner />
            </div>
        );

    if (!blog) return <NotFoundPage />;

    return (
        <>
            <main className="relative pt-24">
                <div className="fixed top-0 left-0 w-full p-4 flex justify-center bg-white-full z-[100]">
                    {/* preview toolbar, reload, atrás */}
                    <nav className="flex items-center gap-2">
                        <Button onClick={() => navigate(`/cms/blog/${bid}/edit`)}>
                            <ArrowDown className="rotate-90" />
                            <span>Seguir editando</span>
                        </Button>
                        <Button onClick={() => refetch()}>
                            <Reload />
                            <span>Recargar</span>
                        </Button>
                    </nav>
                </div>
                <BlogPreview blog={blog} />
            </main>
        </>
    );
}
