import { useEffect, useState } from "react";
import { Link, useParams } from "react-router-dom";
import {
    <PERSON>readcrumb,
    Button,
    Form,
    Image,
    Input,
    Select,
    Tag,
    Typography,
    Upload,
    message,
    notification,
} from "antd";
import ImgCrop from "antd-img-crop";
import { useMutation, useQuery } from "@tanstack/react-query";
import type { UploadFile, GetProp, UploadProps } from "antd";

const { Text } = Typography;
const { Dragger } = Upload;
const { Option } = Select;

import AdminLayout from "@layouts/admin/AdminLayout";
import Spinner from "@components/shared/atoms/Spinner";

import Save from "@assets/icons/general/save-stroke.svg?react";

import CloudUpload from "@assets/shapes/cloud-upload.svg?react";

import {
    retrieveTestimonial,
    updateTestimonial,
} from "@services/portals/cms/testimonial";
import { openErrorNotification } from "@lib/notification";
import { PartialUpdateTestimonial, TestimonialStatusEnum } from "@myTypes/testimonial";
import { getBase64 } from "@lib/helpers";

type FileType = Parameters<GetProp<UploadProps, "beforeUpload">>[0];

type DraggerProps = {
    customRequest: UploadProps["customRequest"];
    onRemove?: UploadProps["onRemove"];
    onPreview?: UploadProps["onPreview"];
    fileList?: UploadProps<FileType>["fileList"];
};

export default function TestimoniaEditPage() {
    const [messsageApi, messageHolder] = message.useMessage();
    const [notificationApi, notificationHolder] = notification.useNotification();
    const { tid } = useParams<{ tid: string }>();
    const [form] = Form.useForm<PartialUpdateTestimonial>();

    const { isLoading, isError, data, refetch } = useQuery({
        queryKey: ["testimonial", tid],
        queryFn: () => retrieveTestimonial(tid),
        enabled: tid !== undefined,
        refetchOnWindowFocus: false,
    });

    const updateMutation = useMutation({
        mutationFn: (data: PartialUpdateTestimonial) => {
            return updateTestimonial(tid as string, data);
        },
        onSuccess: () => {
            messsageApi.success("Testimonio actualizado correctamente");
            refetch();
        },
        onError: (error: Error) => {
            openErrorNotification(
                "Error al actualizar el testimonio",
                error.message,
                notificationApi,
            );
        },
    });

    const [fileList, setFileList] = useState<UploadFile<FileType>[]>([]);
    const [previewOpen, setPreviewOpen] = useState(false);

    useEffect(() => {
        if (data && data.authorPhoto) {
            const currentFile = {
                uid: data?.authorPhoto.fid,
                name: data?.authorPhoto.name,
                status: "done",
                url: data?.authorPhoto.url,
            };
            setFileList([currentFile as UploadFile<FileType>]);
        }
    }, [data, form]);

    const handleSave = () => {
        form.submit();
    };

    const handleFormFinish = (values: PartialUpdateTestimonial) => {
        if (data?.authorPhoto && fileList.length === 0) {
            values.deleteAuthorPhoto = true;
        }
        updateMutation.mutate(values);
    };

    const draggerProps: DraggerProps = {
        customRequest: async (options) => {
            const { file, onSuccess, onError } = options;
            try {
                if (typeof onSuccess === "function") {
                    const base64file = await getBase64(file as FileType);

                    const uploadFile: UploadFile<FileType> =
                        file as UploadFile<FileType>;
                    uploadFile.url = base64file;

                    form.setFieldsValue({
                        authorPhotoFile: [uploadFile],
                    });
                    setFileList([uploadFile]);
                    onSuccess(uploadFile);
                }
            } catch (error) {
                typeof onError === "function" && onError(error as Error);
            }
        },
        onRemove: () => {
            form.setFieldsValue({
                authorPhotoFile: undefined,
            });
            setFileList([]);
        },
        onPreview: async () => {
            setPreviewOpen(true);
        },
        fileList,
    };

    return (
        <>
            {messageHolder}
            {notificationHolder}
            <AdminLayout>
                <div className="max-w-7xl w-full h-full space-y-5">
                    <div className="flex justify-between items-center">
                        <div>
                            <h1 className="text-2xl text-black-full">
                                Bienvenido,{" "}
                                <span className="text-blue-full font-semibold">
                                    Gerardo
                                </span>
                            </h1>
                            <Text className="text-sm text-gray-500 font-medium">
                                Gestiona aquí los Testimonios para la Website
                            </Text>
                        </div>
                        <div className="flex gap-3">
                            <Button
                                type="primary"
                                size="large"
                                style={{ fontSize: 16 }}
                                icon={<Save />}
                                disabled={isError}
                                onClick={handleSave}
                            >
                                Guardar
                            </Button>
                        </div>
                    </div>
                    {isLoading ? (
                        <Spinner />
                    ) : (
                        <>
                            <div className="p-5 bg-white-full rounded-lg space-y-5 shadow-sm">
                                <div className="flex items-center">
                                    <Breadcrumb
                                        separator=">"
                                        items={[
                                            {
                                                title: (
                                                    <Link
                                                        to="/admin/testimonial"
                                                        className="text-base"
                                                    >
                                                        Testimonios
                                                    </Link>
                                                ),
                                            },
                                            {
                                                title: (
                                                    <Link
                                                        to={`/admin/testimonial/${tid}`}
                                                        className="text-base"
                                                    >
                                                        {data?.authorName !== undefined
                                                            ? data.authorName
                                                            : "Testimonio"}
                                                    </Link>
                                                ),
                                            },
                                            {
                                                title: (
                                                    <Text className="text-base">
                                                        Editar
                                                    </Text>
                                                ),
                                            },
                                        ]}
                                    />
                                </div>
                            </div>
                            <div className="space-y-5">
                                <Form
                                    name="testimonialEdit"
                                    layout="vertical"
                                    form={form}
                                    initialValues={data}
                                    onFinish={handleFormFinish}
                                >
                                    <div className="grid grid-cols-1 lg:grid-cols-6 gap-y-6 lg:gap-6">
                                        <div className="bg-white-full col-span-4 p-5 rounded-lg shadow-sm">
                                            <p className="text-gray-400 font-semibold text-sm">
                                                INFORMACIÓN PERSONAL
                                            </p>
                                            <Form.Item<PartialUpdateTestimonial>
                                                name="authorName"
                                                label={
                                                    <span className="font-semibold text-base">
                                                        Nombre del Autor
                                                    </span>
                                                }
                                                rules={[
                                                    {
                                                        required: true,
                                                        message:
                                                            "Por favor, ingrese el nombre del autor",
                                                    },
                                                ]}
                                            >
                                                <Input
                                                    placeholder="Nombre del Autor"
                                                    className="py-2"
                                                />
                                            </Form.Item>
                                            <Form.Item<PartialUpdateTestimonial>
                                                name="authorTitle"
                                                label={
                                                    <span className="font-semibold text-base">
                                                        Título o Rol
                                                    </span>
                                                }
                                            >
                                                <Input
                                                    placeholder="Título o Rol"
                                                    className="py-2"
                                                />
                                            </Form.Item>
                                            <Form.Item<PartialUpdateTestimonial>
                                                name="content"
                                                label={
                                                    <span className="font-semibold text-base">
                                                        Contenido
                                                    </span>
                                                }
                                                rules={[
                                                    {
                                                        required: true,
                                                        message:
                                                            "Por favor, ingrese el contenido del testimonio",
                                                    },
                                                ]}
                                            >
                                                <Input.TextArea
                                                    placeholder="Contenido"
                                                    className="py-2"
                                                    autoSize={{
                                                        minRows: 7,
                                                        maxRows: 7,
                                                    }}
                                                />
                                            </Form.Item>
                                        </div>
                                        <div className="col-span-2 space-y-6">
                                            <div className="bg-white-full p-5 rounded-lg shadow-sm">
                                                <p className="text-gray-400 font-semibold text-sm">
                                                    CONTENIDO MULTIMEDIA
                                                </p>
                                                <Form.Item<PartialUpdateTestimonial>
                                                    name="authorPhotoFile"
                                                    label={
                                                        <span className="font-semibold text-base">
                                                            Avatar del Autor
                                                        </span>
                                                    }
                                                    valuePropName="listFile"
                                                    getValueFromEvent={(e) => {
                                                        return e?.fileList;
                                                    }}
                                                >
                                                    <ImgCrop>
                                                        <Dragger
                                                            {...draggerProps}
                                                            listType="picture"
                                                            maxCount={1}
                                                            multiple={false}
                                                        >
                                                            <div className="flex flex-col justify-center items-center">
                                                                <CloudUpload />
                                                                <Text className="font-medium text-black-full">
                                                                    Arrastre una imagen
                                                                    de perfil o haga
                                                                    click aquí
                                                                </Text>
                                                                <Text className="text-xs text-black-medium">
                                                                    Solo una imagen
                                                                    (Máx. 2MB)
                                                                </Text>
                                                            </div>
                                                        </Dragger>
                                                    </ImgCrop>
                                                </Form.Item>
                                                <Image
                                                    wrapperStyle={{ display: "none" }}
                                                    preview={{
                                                        visible: previewOpen,
                                                        onVisibleChange: setPreviewOpen,
                                                    }}
                                                    src={fileList[0]?.url}
                                                />
                                            </div>
                                            <div className="bg-white-full p-5 rounded-lg shadow-sm">
                                                <p className="text-gray-400 font-semibold text-sm">
                                                    VISIBILIDAD DEL CONTENIDO
                                                </p>
                                                <Form.Item<PartialUpdateTestimonial>
                                                    name="status"
                                                    label={
                                                        <span className="font-semibold text-base">
                                                            Estado del Testimonio
                                                        </span>
                                                    }
                                                    rules={[
                                                        {
                                                            required: true,
                                                            message:
                                                                "Por favor, seleccione un estado",
                                                        },
                                                    ]}
                                                >
                                                    <Select>
                                                        <Option
                                                            value={
                                                                TestimonialStatusEnum.PUBLISHED
                                                            }
                                                        >
                                                            <Tag
                                                                color="green"
                                                                className="rounded-full px-3"
                                                                bordered={false}
                                                            >
                                                                Publicado
                                                            </Tag>
                                                        </Option>
                                                        <Option
                                                            value={
                                                                TestimonialStatusEnum.DRAFT
                                                            }
                                                        >
                                                            <Tag
                                                                color="volcano"
                                                                className="rounded-full px-3"
                                                                bordered={false}
                                                            >
                                                                Borrador
                                                            </Tag>
                                                        </Option>
                                                    </Select>
                                                </Form.Item>
                                                <Form.Item<PartialUpdateTestimonial>
                                                    name="order"
                                                    label={
                                                        <span className="font-semibold text-base">
                                                            Orden
                                                        </span>
                                                    }
                                                >
                                                    <Input placeholder="Ej. 2" />
                                                </Form.Item>
                                            </div>
                                        </div>
                                    </div>
                                </Form>
                            </div>
                        </>
                    )}
                </div>
            </AdminLayout>
        </>
    );
}
