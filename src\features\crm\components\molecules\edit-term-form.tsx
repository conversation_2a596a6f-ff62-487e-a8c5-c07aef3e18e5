import { Form, Input, Button } from "antd";
import type { FormInstance } from "antd";
import { useMutation } from "@tanstack/react-query";
import { updateTerm } from "@/features/crm/services/portals/term";

type ErrorData = { name?: unknown; detail?: unknown };
type APIError = { response?: { data?: ErrorData } };

export type EditTermFormProps = {
    form: FormInstance;
    tid: string;
    initialName: string;
    onClose: () => void;
    onUpdated: () => void;
    notify: (type: "success" | "error", message: string, description?: string) => void;
};

export default function EditTermForm({
    form,
    tid,
    initialName,
    onClose,
    onUpdated,
    notify,
}: EditTermFormProps) {
    const { mutate, isPending } = useMutation({
        mutationFn: (name: string) => updateTerm(tid, { name }),
        onSuccess: () => {
            notify(
                "success",
                "Ciclo actualizado",
                "Los cambios se guardaron correctamente",
            );
            onUpdated();
            onClose();
        },
        onError: (err: unknown) => {
            const res = (err as APIError).response?.data ?? {};
            const nameVal = res.name;
            const fieldMsg = Array.isArray(nameVal) ? nameVal[0] : nameVal;
            const detail = res.detail;
            if (fieldMsg) {
                form.setFields([{ name: "name", errors: [String(fieldMsg)] }]);
            }
            const friendly =
                fieldMsg ||
                detail ||
                "No se pudo actualizar el ciclo. Verifica el nombre e inténtalo nuevamente.";
            notify("error", "Error al actualizar", friendly);
        },
    });

    return (
        <Form
            form={form}
            layout="vertical"
            initialValues={{ name: initialName }}
            onFinish={(v: { name: string }) => mutate(v.name)}
        >
            <Form.Item
                name="name"
                label="Nombre del ciclo"
                rules={[{ required: true, message: "Ingrese el nombre" }]}
            >
                <Input placeholder="Ej: Ciclo 1" disabled={isPending} />
            </Form.Item>
            <div className="flex justify-end gap-2">
                <Button onClick={onClose} disabled={isPending}>
                    Cancelar
                </Button>
                <Button type="primary" htmlType="submit" loading={isPending}>
                    Guardar
                </Button>
            </div>
        </Form>
    );
}
