import { Select, SelectProps } from "antd";
import { useOrdersByContact } from "../../hooks/use-order";
import { Link } from "react-router-dom";
import { ExternalLink } from "lucide-react";

interface SelectOrderByContactProps extends Omit<SelectProps, "options"> {
    value?: string;
    onChange?: (value: string) => void;
    contactId?: string;
}

export default function SelectOrderByContact({
    value,
    onChange,
    contactId,
    ...restProps
}: SelectOrderByContactProps) {
    const { orders } = useOrdersByContact(contactId || "");

    const orderOptions: SelectProps["options"] = orders?.map((order) => ({
        value: order.oid,
        label: (
            <div className="flex justify-between items-center">
                <span>{order.owner.fullName}</span>
                <span className="text-xs text-gray-600">#{order.oid?.slice(-6)}</span>
                <Link to={`/crm/orders/${order.oid}`} title="View Order">
                    <ExternalLink size={14} />
                </Link>
            </div>
        ),
        data: {
            ...order,
        },
    }));

    return (
        <Select
            {...restProps}
            value={value}
            onChange={onChange}
            options={orderOptions}
        />
    );
}
