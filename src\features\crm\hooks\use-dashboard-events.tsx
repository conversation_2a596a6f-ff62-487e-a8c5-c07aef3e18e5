import { useMutation, UseMutationOptions, useQuery } from "@tanstack/react-query";

import {
    getEventDashboardSummary,
    getEventDashboardAnalytics,
    getEventDashboardSegmentation,
    invalidateEventDashboardCache,
    getEventDashboardLaunched,
    getEventDashboardHistorical,
} from "../services/portals/dashboard/event";
import type { DashboardEventQueryParams } from "../types/dashboard/events";
import type { Dayjs } from "dayjs";

// Hook for the summary (most important, loads first)
export const useDashboardEventsSummary = (query: DashboardEventQueryParams = {}) => {
    const { data, isLoading, isError, isFetching } = useQuery({
        queryKey: ["dashboard-events-summary", query],
        queryFn: () => getEventDashboardSummary(query ?? {}),
        refetchOnWindowFocus: false,
    });

    return {
        isLoading: isLoading || isFetching,
        isError,
        data,
    };
};

// Hook for analytics
export const useDashboardEventsAnalytics = (query: DashboardEventQueryParams = {}) => {
    const { data, isLoading, isError, isFetching } = useQuery({
        queryKey: ["dashboard-events-analytics", query],
        queryFn: () => getEventDashboardAnalytics(query ?? {}),
        refetchOnWindowFocus: false,
    });

    return {
        isLoading: isLoading || isFetching,
        isError,
        data,
    };
};

// Hook for segmentation
export const useDashboardEventsSegmentation = (
    query: DashboardEventQueryParams = {},
) => {
    const { data, isLoading, isError, isFetching } = useQuery({
        queryKey: ["dashboard-events-segmentation", query],
        queryFn: () => getEventDashboardSegmentation(query ?? {}),
        refetchOnWindowFocus: false,
    });

    return {
        isLoading: isLoading || isFetching,
        isError,
        data,
    };
};

// Hook for launched events
export const useDashboardLaunchedEvents = (query: DashboardEventQueryParams = {}) => {
    const { data, isLoading, isError, isFetching } = useQuery({
        queryKey: ["dashboard-events-launched", query],
        queryFn: () => getEventDashboardLaunched(query ?? {}),
        refetchOnWindowFocus: false,
    });

    return {
        isLoading: isLoading || isFetching,
        isError,
        data,
    };
};

// Hook for historical events
export const useDashboardEventsHistorical = (query: DashboardEventQueryParams = {}) => {
    const { data, isLoading, isError, isFetching } = useQuery({
        queryKey: ["dashboard-events-historical", query],
        queryFn: () => getEventDashboardHistorical(query ?? {}),
        refetchOnWindowFocus: false,
    });

    return {
        isLoading: isLoading || isFetching,
        isError,
        data,
    };
};

export const useDashboardEventsInvalidateCache = (options?: UseMutationOptions) => {
    const mutation = useMutation({
        mutationFn: () => invalidateEventDashboardCache(),
        ...options,
    });

    return mutation;
};

// Utility function to create reusable query parameters
export const createDashboardEventsQueryParams = (
    searchParams: URLSearchParams,
    defaultDateRange?: [Dayjs, Dayjs],
): DashboardEventQueryParams => {
    return {
        startDate:
            searchParams.get("startDate") ||
            defaultDateRange?.[0]?.format("YYYY-MM-DD") ||
            undefined,
        endDate:
            searchParams.get("endDate") ||
            defaultDateRange?.[1]?.format("YYYY-MM-DD") ||
            undefined,
        stage: searchParams.get("stage") || undefined,
        eventType: searchParams.get("eventType") || undefined,
        modality: searchParams.get("modality") || undefined,
        programs: searchParams.get("programs") || undefined,
        events: searchParams.get("events") || undefined,
    };
};
