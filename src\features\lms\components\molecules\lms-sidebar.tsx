import { Link, useLocation } from "react-router-dom";
import { Menu } from "antd";
import type { MenuProps } from "antd";

import Dashboard from "@assets/icons/huge/dashboard.svg?react";
import ContactBook from "@assets/icons/huge/contact-book.svg?react";
import Program from "@assets/icons/huge/program.svg?react";
import { type MenuItemWithPermission, useMenuViewFilter } from "@hooks/use-permission";

const menuItemsWithPermissions: MenuItemWithPermission[] = [
    {
        key: "general",
        label: <span className="text-black-medium font-semibold text-xs">GENERAL</span>,
        type: "group",
        children: [
            {
                key: "dashboard",
                label: <Link to="/lms">Dashboard</Link>,
                icon: <Dashboard />,
            },
        ],
    },
    {
        type: "divider",
    },
    {
        key: "enrollments-group",
        label: (
            <span className="text-black-medium font-semibold text-xs">MATRÍCULAS</span>
        ),
        type: "group",
        children: [
            {
                key: "enrollments",
                label: (
                    <Link to="/lms/enrollments/" className="font-medium text-sm">
                        Matrículas
                    </Link>
                ),
                icon: <ContactBook />,
                requiredPermission: "lms.enrollment",
            },
            {
                key: "programs",
                label: (
                    <Link to="/lms/programs/" className="font-medium text-sm">
                        Programas
                    </Link>
                ),
                icon: <Program />,
            },
        ],
    },
];

const PATHS: Record<string, string[]> = {
    "/lms": ["dashboard"],
    "/lms/enrollments": ["enrollments"],
    "/lms/programs": ["programs"],
};

export default function LmsSidebar() {
    const location = useLocation();

    const filteredItems = useMenuViewFilter(menuItemsWithPermissions);

    const selectedKeys =
        location.pathname === "/lms"
            ? ["dashboard"]
            : Object.entries(PATHS).find(([path]) => {
                  if (path === "/lms") return false;
                  return location.pathname.includes(path);
              })?.[1] || [];

    const onClick: MenuProps["onClick"] = (e) => {
        console.info("click ", e);
    };

    return (
        <Menu
            onClick={onClick}
            style={{ width: 256, border: 0 }}
            defaultSelectedKeys={selectedKeys}
            defaultOpenKeys={[]}
            mode="inline"
            items={filteredItems}
            className="fixed w-64"
        />
    );
}
