import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "antd";
import { OrderPartialUpdate, OrderStage, RetrieveOrder } from "../../types/order";
import { EditIcon } from "lucide-react";
import { useState } from "react";
import dayjs from "dayjs";
import type { Dayjs } from "dayjs";
import { useMutation } from "@tanstack/react-query";
import { partialUpdateOrder } from "../../services/portals/order";
import queryClient from "@lib/queryClient";
import type { AxiosError } from "axios";
import { useApiError } from "@hooks/use-api-error";

type EditStageDatePopoverProps = {
    stage: OrderStage;
    order: RetrieveOrder;
};

// Orden jerárquico de los stages
const STAGE_HIERARCHY = [
    OrderStage.PROSPECT,
    OrderStage.INTERESTED,
    OrderStage.TO_PAY,
    OrderStage.SOLD,
    OrderStage.LOST,
];

// Función para validar que las fechas respeten la jerarquía
const validateStageDate = (
    newDate: Dayjs,
    stage: OrderStage,
    stagesDates: RetrieveOrder["stagesDates"],
): { isValid: boolean; errorMessage?: string } => {
    const currentStageIndex = STAGE_HIERARCHY.indexOf(stage);

    // Si el stage no está en la jerarquía principal, no validamos
    if (currentStageIndex === -1) {
        return { isValid: true };
    }

    // Validar stages anteriores (no pueden ser posteriores a la nueva fecha)
    for (let i = 0; i < currentStageIndex; i++) {
        const previousStage = STAGE_HIERARCHY[i];
        const previousStageDate = stagesDates.find(
            (s) => s.stage === previousStage,
        )?.date;

        if (previousStageDate) {
            const previousDate = dayjs(previousStageDate);
            if (previousDate.isAfter(newDate)) {
                return {
                    isValid: false,
                    errorMessage: `La fecha no puede ser anterior a la fecha de ${getStageLabel(previousStage)} (${previousDate.format("DD/MM/YYYY HH:mm")})`,
                };
            }
        }
    }

    // Validar stages posteriores (no pueden ser anteriores a la nueva fecha)
    for (let i = currentStageIndex + 1; i < STAGE_HIERARCHY.length; i++) {
        const nextStage = STAGE_HIERARCHY[i];
        const nextStageDate = stagesDates.find((s) => s.stage === nextStage)?.date;

        if (nextStageDate) {
            const nextDate = dayjs(nextStageDate);
            if (nextDate.isBefore(newDate)) {
                return {
                    isValid: false,
                    errorMessage: `La fecha no puede ser posterior a la fecha de ${getStageLabel(nextStage)} (${nextDate.format("DD/MM/YYYY HH:mm")})`,
                };
            }
        }
    }

    return { isValid: true };
};

// Función auxiliar para obtener el label del stage
const getStageLabel = (stage: OrderStage): string => {
    const labels = {
        [OrderStage.PROSPECT]: "Prospecto",
        [OrderStage.INTERESTED]: "Interesado",
        [OrderStage.TO_PAY]: "Por pagar",
        [OrderStage.SOLD]: "Vendido",
        [OrderStage.LOST]: "Perdido",
    };
    return labels[stage];
};

const EditStageDatePopover = ({ stage, order }: EditStageDatePopoverProps) => {
    const [open, setOpen] = useState(false);
    const [validationError, setValidationError] = useState<string | null>(null);
    const { message, notification } = App.useApp();

    const [stageDate, setStageDate] = useState<Dayjs | null>(
        order?.stagesDates.find((s) => s.stage === stage)?.date
            ? dayjs(order?.stagesDates.find((s) => s.stage === stage)?.date)
            : null,
    );

    const { handleError: handleUpdateError } = useApiError({
        title: "Error al actualizar la fecha de etapa",
        genericMessage: "No se pudo actualizar la fecha de etapa",
    });

    const { mutate: updateStageUpdateMutate, isPending } = useMutation({
        mutationFn: (payload: OrderPartialUpdate) =>
            partialUpdateOrder(order.oid, payload),
        onSuccess: () => {
            message.success({
                content: "Fecha de etapa actualizada",
                duration: 2,
            });
            queryClient.invalidateQueries({
                queryKey: ["order", order.oid],
            });
            setOpen(false);
        },
        onError: (error: AxiosError) => {
            handleUpdateError(error);
        },
    });

    const handleOpenChange = (newOpen: boolean) => {
        setOpen(newOpen);
        // Limpiar error de validación al cerrar el popover
        if (!newOpen) {
            setValidationError(null);
        }
    };

    const handleDateChange = (date: Dayjs | null) => {
        setStageDate(date);

        // Validar en tiempo real si hay una fecha seleccionada
        if (date) {
            const validation = validateStageDate(date, stage, order.stagesDates);
            if (!validation.isValid) {
                setValidationError(
                    validation.errorMessage || "Las fechas no son válidas",
                );
            } else {
                setValidationError(null);
            }
        } else {
            setValidationError(null);
        }
    };

    const handleUpdateStageDate = () => {
        if (stageDate) {
            // Validar la fecha antes de actualizar
            const validation = validateStageDate(stageDate, stage, order.stagesDates);

            if (!validation.isValid) {
                setValidationError(
                    validation.errorMessage || "Las fechas no son válidas",
                );
                notification.error({
                    message: "Error de validación",
                    description: validation.errorMessage || "Las fechas no son válidas",
                    duration: 4,
                });
                return;
            }

            // Limpiar error de validación si la fecha es válida
            setValidationError(null);

            let payload: Omit<OrderPartialUpdate, "owner"> = {};

            if (stage === OrderStage.PROSPECT) {
                payload = {
                    prospectAt: stageDate.toISOString(),
                };
            } else if (stage === OrderStage.INTERESTED) {
                payload = {
                    interestedAt: stageDate.toISOString(),
                };
            } else if (stage === OrderStage.TO_PAY) {
                payload = {
                    toPayAt: stageDate.toISOString(),
                };
            } else if (stage === OrderStage.SOLD) {
                payload = {
                    soldAt: stageDate.toISOString(),
                };
            } else if (stage === OrderStage.LOST) {
                payload = {
                    lostAt: stageDate.toISOString(),
                };
            }
            return updateStageUpdateMutate({
                ...payload,
                owner: order.owner.uid,
            });
        }
        setOpen(false);
    };
    return (
        <Popover
            content={
                <div className="max-w-[200px]">
                    <div className="space-y-2">
                        <DatePicker
                            showTime
                            value={stageDate}
                            onChange={handleDateChange}
                            format="YYYY-MM-DD HH:mm:ss"
                            status={validationError ? "error" : undefined}
                        />
                        {validationError && (
                            <div className="text-red-500 text-xs">
                                {validationError}
                            </div>
                        )}
                    </div>
                    <Divider className="my-1" />
                    <div className="flex">
                        <Button
                            block
                            type="text"
                            onClick={() => setOpen(false)}
                            disabled={isPending}
                        >
                            Cancelar
                        </Button>
                        <Button
                            block
                            type="primary"
                            onClick={handleUpdateStageDate}
                            loading={isPending}
                            disabled={!!validationError}
                        >
                            Actualizar
                        </Button>
                    </div>
                </div>
            }
            trigger="click"
            open={open}
            onOpenChange={handleOpenChange}
        >
            <Tooltip title="Editar fecha de etapa">
                <EditIcon className="text-blue-full hover:cursor-pointer" size={16} />
            </Tooltip>
        </Popover>
    );
};

export default EditStageDatePopover;
