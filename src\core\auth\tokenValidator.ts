import { checkToken, logout as logoutService } from "@services/portals/shared/auth";
import type { AuthState } from "@store/authStore";

interface TokenValidationResponse {
    key: string;
    user: {
        uid: string;
        username: string;
        email: string;
        first_name: string;
        last_name: string;
        groups: string[];
    };
}

/**
 * Validates if current state matches server response
 * Returns true if data matches, false if there are discrepancies
 */
const validateStateConsistency = (
    currentState: AuthState,
    serverResponse: TokenValidationResponse,
): boolean => {
    return (
        serverResponse.key === currentState.key &&
        serverResponse.user.uid === currentState.user?.uid &&
        serverResponse.user.username === currentState.user?.username &&
        JSON.stringify(serverResponse.user.groups) ===
            JSON.stringify(currentState.user?.groups)
    );
};

/**
 * Validates token with server and returns action to take
 */
export const validateTokenWithServer = async (
    currentState: AuthState,
): Promise<{
    action: "keep" | "logout";
}> => {
    // If not authenticated or no token, skip validation
    if (!currentState.isAuthenticated || !currentState.key) {
        return { action: "keep" };
    }

    try {
        const response = await checkToken();

        // Check if data is consistent
        if (validateStateConsistency(currentState, response)) {
            // Data matches, keep current state
            return { action: "keep" };
        } else {
            // Data mismatch detected - SECURITY RISK
            // Force logout to prevent localStorage manipulation
            console.warn(
                "Token validation failed: Data mismatch detected. Forcing logout for security.",
            );
            return { action: "logout" };
        }
    } catch (error) {
        console.error("Token validation failed:", error);
        // Token is invalid, force logout
        return { action: "logout" };
    }
};

/**
 * Performs logout operation
 */
export const performLogout = async (): Promise<void> => {
    try {
        await logoutService();
    } catch (error) {
        console.error("Error during logout service call:", error);
    }
};
