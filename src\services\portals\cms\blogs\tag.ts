import { PaginatedResponse, PaginationParams } from "@myTypes/base";
import { BlogTag, CreateBlogTagBody, UpdateBlogTagBody } from "@myTypes/blog";
import { portalsApi } from "@services/portals";

export const DEFAULT_PAGE = 1;
export const DEFAULT_PAGE_SIZE = 10;

export type ListBlogTagsQuery = PaginationParams & {
    search?: string;
    sortBy?: string;
    order?: "asc" | "desc";
};

/**
 * List blog tags with pagination and filtering
 */
export const listBlogTags = async (
    params: ListBlogTagsQuery = {},
): Promise<PaginatedResponse<BlogTag>> => {
    const response = await portalsApi.get("cms/blogs/tags", {
        params: {
            page: params.page || DEFAULT_PAGE,
            pageSize: params.pageSize || DEFAULT_PAGE_SIZE,
            ...params,
        },
    });
    return response.data;
};

export const createBlogTag = async (data: CreateBlogTagBody): Promise<BlogTag> => {
    const response = await portalsApi.post("cms/blogs/tags", data);
    return response.data;
};

/**
 * Retrieve a blog tag by ID
 */
export const retrieveBlogTag = async (btid: string): Promise<BlogTag> => {
    if (!btid) {
        throw new Error("No blog tag ID provided");
    }
    const response = await portalsApi.get(`cms/blogs/tags/${btid}`);
    return response.data;
};

export const updateBlogTag = async (
    btid: string,
    data: UpdateBlogTagBody,
): Promise<BlogTag> => {
    if (!btid) {
        throw new Error("No blog tag ID provided");
    }
    const response = await portalsApi.patch(`cms/blogs/tags/${btid}`, data);
    return response.data;
};

/**
 * Delete a blog tag
 */
export const deleteBlogTag = async (btid: string): Promise<void> => {
    if (!btid) {
        throw new Error("No blog tag ID provided");
    }
    await portalsApi.delete(`cms/blogs/tags/${btid}`);
};
