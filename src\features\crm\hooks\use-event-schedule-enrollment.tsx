import { useQuery } from "@tanstack/react-query";
import { ListEventScheduleEnrollmentsQueryParams } from "@/features/crm/types/event-schedule-enrollment";
import { listEventScheduleEnrollments } from "@/features/crm/services/portals/event-schedule-enrollment";

type UseEventScheduleEnrollmentsProps = {
    esid: string;
    queryParams?: ListEventScheduleEnrollmentsQueryParams;
    enabled?: boolean;
};

export const useEventScheduleEnrollments = ({
    esid,
    queryParams,
    enabled = true,
}: UseEventScheduleEnrollmentsProps) => {
    const { data, isLoading, isError } = useQuery({
        queryKey: ["event-schedule-enrollments", esid, queryParams],
        queryFn: () => listEventScheduleEnrollments(esid, queryParams),
        enabled: enabled && !!esid,
        refetchOnWindowFocus: false,
    });

    const {
        count,
        results: enrollments,
        next,
        previous,
    } = data || {
        count: 0,
        results: [],
        next: null,
        previous: null,
    };

    return {
        isLoading,
        isError,
        enrollments,
        count,
        next,
        previous,
    };
};
