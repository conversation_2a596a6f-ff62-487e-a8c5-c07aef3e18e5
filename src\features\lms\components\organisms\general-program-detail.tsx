import { useState } from "react";
import { Card, Form, Button, Typography, Row, Col, Tag, Tooltip } from "antd";
import { Calendar, Clock, Users, BookOpen, Edit3, FileText } from "lucide-react";
import dayjs from "dayjs";

import { Program } from "@/features/lms/types/program";
import {
    OfferingModality,
    OfferingModalityLabel,
    OfferingType,
    OfferingTypeLabel,
    OfferingFormat,
    OfferingStage,
    OfferingStageLabel,
} from "@myTypes/offering";
import ProgramEnrollmentsDetail from "./program-enrollments-detail";
import GeneralProgramEdit from "./general-program-edit";

const { Title, Text } = Typography;

interface GeneralProgramEditProps {
    oid: string;
    data: Program;
    onRefetch: () => void;
}

interface ProgramFormValues {
    name: string;
    longName?: string;
    codeName?: string;
    description?: string;
    duration?: string;
    frequency?: string;
    hours?: number;
    schedule?: string;
    modality: OfferingModality;
    type: OfferingType;
    stage: OfferingStage;
    format: OfferingFormat;
    rangeDate: [dayjs.Dayjs, dayjs.Dayjs];
}

export default function GeneralProgramDetail({
    oid,
    data,
    onRefetch,
}: GeneralProgramEditProps) {
    const [form] = Form.useForm<ProgramFormValues>();
    const [isEditing, setIsEditing] = useState(false);

    const handleEdit = () => {
        setIsEditing(true);
        form.setFieldsValue({
            name: data.name,
            longName: data.longName,
            codeName: data.codeName,
            description: data.description,
            duration: data.duration,
            frequency: data.frequency,
            hours: data.hours,
            schedule: data.schedule,
            modality: data.modality,
            type: data.type,
            stage: data.stage,
            format: data.format,
            rangeDate: [dayjs(data.startDate), dayjs(data.endDate)],
        });
    };

    const handleCancel = () => {
        setIsEditing(false);
        form.resetFields();
    };

    const onUpdateSuccess = () => {
        setIsEditing(false);
        onRefetch();
    };

    const getStatusColor = () => {
        const stage = data.stage;
        if (stage === OfferingStage.ENROLLMENT) return "blue";
        if (stage === OfferingStage.ENROLLMENT_CLOSED) return "purple";
        return "default";
    };

    if (!isEditing) {
        return (
            <div className="space-y-6">
                {/* Hero Banner Card - Similar to Google Classroom */}
                <Card className="overflow-hidden">
                    {/* Banner/Thumbnail Section */}
                    <div className="relative  bg-gradient-to-br flex items-end brightness-20">
                        <div className="absolute inset-0 bg-black bg-opacity-30"></div>
                        <div className="relative z-10 p-6 text-white-full w-full">
                            <div className="flex justify-between items-end">
                                <div className="flex-1">
                                    <div className="flex items-center gap-4 mb-2">
                                        <div>
                                            <Title level={2}>
                                                {data.longName || data.name}
                                            </Title>
                                            {data.codeName && (
                                                <Text className="text-lg text-gray-600">
                                                    {data.codeName}
                                                </Text>
                                            )}
                                        </div>
                                    </div>
                                </div>
                                <Tooltip title={"Editar información del programa"}>
                                    <Button
                                        type="primary"
                                        onClick={handleEdit}
                                        icon={<Edit3 size={16} />}
                                        shape="circle"
                                    />
                                </Tooltip>
                            </div>
                        </div>
                    </div>

                    {/* Key Information Bar */}
                    <div className="bg-white-full p-4 border-b">
                        <Row gutter={[24, 16]} align="middle">
                            <Col xs={24} sm={8} md={6}>
                                <div className="text-center">
                                    <div className="flex items-center justify-center gap-2 mb-1">
                                        <Calendar size={16} className="text-blue-500" />
                                        <Text strong className="text-gray-600 text-sm">
                                            INICIO
                                        </Text>
                                    </div>
                                    <Text className="text-lg font-semibold">
                                        {dayjs(data.startDate).format("DD")}
                                    </Text>
                                    <br />
                                    <Text className="text-sm text-gray-500">
                                        {dayjs(data.startDate).format("MMM YYYY")}
                                    </Text>
                                </div>
                            </Col>
                            <Col xs={24} sm={8} md={6}>
                                <div className="text-center">
                                    <div className="flex items-center justify-center gap-2 mb-1">
                                        <Clock size={16} className="text-green-500" />
                                        <Text strong className="text-gray-600 text-sm">
                                            FRECUENCIA
                                        </Text>
                                    </div>
                                    <Text className="text-base font-medium">
                                        {data.frequency || "No definida"}
                                    </Text>
                                </div>
                            </Col>
                            <Col xs={24} sm={8} md={6}>
                                <div className="text-center">
                                    <div className="flex items-center justify-center gap-2 mb-1">
                                        <Clock size={16} className="text-orange-500" />
                                        <Text strong className="text-gray-600 text-sm">
                                            HORARIO
                                        </Text>
                                    </div>
                                    <Text className="text-base font-medium">
                                        {data.schedule || "No definido"}
                                    </Text>
                                </div>
                            </Col>
                            <Col xs={24} sm={24} md={6}>
                                <div className="text-center">
                                    <div className="flex items-center justify-center gap-2 mb-2">
                                        <Users size={16} className="text-purple-500" />
                                        <Text strong className="text-gray-600 text-sm">
                                            ESTADO
                                        </Text>
                                    </div>
                                    <Tag
                                        color={getStatusColor()}
                                        className="px-3 py-1 rounded-full font-medium"
                                    >
                                        {OfferingStageLabel[data.stage]}
                                    </Tag>
                                </div>
                            </Col>
                        </Row>
                    </div>
                </Card>

                {/* Details Cards */}
                <Row gutter={[16, 16]}>
                    {/* Program Information */}
                    <Col xs={24} md={8} className="space-y-4">
                        <Card
                            title={
                                <div className="flex items-center gap-2">
                                    <BookOpen size={18} className="text-blue-full" />
                                    <span>Información del Programa</span>
                                </div>
                            }
                            className="h-fit border-none"
                        >
                            <div className="space-y-4">
                                <div className="flex justify-between items-center">
                                    <Text strong className="text-gray-600">
                                        Modalidad:
                                    </Text>
                                    <Tag color="blue">
                                        {OfferingModalityLabel[data.modality]}
                                    </Tag>
                                </div>
                                <div className="flex justify-between items-center">
                                    <Text strong className="text-gray-600">
                                        Tipo:
                                    </Text>
                                    <Tag color="cyan">
                                        {OfferingTypeLabel[data.type]}
                                    </Tag>
                                </div>
                                {data.duration && (
                                    <div className="flex justify-between items-center">
                                        <Text strong className="text-gray-600">
                                            Duración:
                                        </Text>
                                        <Text className="font-medium">
                                            {data.duration}
                                        </Text>
                                    </div>
                                )}
                                {data.hours && (
                                    <div className="flex justify-between items-center">
                                        <Text strong className="text-gray-600">
                                            Horas Académicas:
                                        </Text>
                                        <Text className="font-medium">
                                            {data.hours}h
                                        </Text>
                                    </div>
                                )}
                            </div>
                        </Card>
                        {/* Description Card */}
                        {data.description && (
                            <Card
                                title={
                                    <div className="flex items-center gap-2">
                                        <FileText
                                            size={18}
                                            className="text-blue-full"
                                        />
                                        <span>Descripción del Programa</span>
                                    </div>
                                }
                                className="h-fit border-none"
                            >
                                <div className="text-base leading-relaxed whitespace-pre-wrap text-gray-700">
                                    {data.description}
                                </div>
                            </Card>
                        )}
                    </Col>
                    <Col xs={24} md={16} className="bg-white-full p-5 rounded-lg">
                        <ProgramEnrollmentsDetail oid={oid} />
                    </Col>
                </Row>
            </div>
        );
    }

    return (
        <Card
            className="bg-transparent border-none"
            styles={{
                body: {
                    padding: 0,
                },
            }}
        >
            <div className="mb-6">
                <Title level={4} className="mb-2">
                    Editar Información General
                </Title>
                <Text type="secondary">Modifica los datos básicos del programa</Text>
            </div>

            <GeneralProgramEdit
                oid={oid}
                data={data}
                onCancel={handleCancel}
                onUpdateSuccess={onUpdateSuccess}
            />
        </Card>
    );
}
