import { Form, InputNumber, Typography, Switch, App } from "antd";
import { useState, forwardRef, useImperativeHandle } from "react";
import SelectOfferings from "../molecules/select-offerings";
import { useCreateOrderItem } from "../../hooks/use-order-item";
import type { CreateOrderItem, Order } from "../../types/order";

const { Text } = Typography;

type AddOrderItemFormProps = {
    order: Order;
    onSuccess?: () => void;
};

export type AddOrderItemFormRef = {
    submit: () => void;
};

const AddOrderItemForm = forwardRef<AddOrderItemFormRef, AddOrderItemFormProps>(
    ({ order, onSuccess }, ref) => {
        const { message } = App.useApp();
        const [form] = Form.useForm<CreateOrderItem>();
        const [useCustomPrice, setUseCustomPrice] = useState(false);

        const { mutate: createOrderItem } = useCreateOrderItem({
            onCreateOrderSuccess: () => {
                form.resetFields();
                message.success("Producto agregado exitosamente");
                onSuccess?.();
            },
        });

        const handleFinish = async (values: CreateOrderItem) => {
            createOrderItem({
                order: order.oid,
                offering: values.offering,
                customAmount: useCustomPrice ? values.customAmount : null,
            });
        };

        // Exposer el método submit al componente padre
        useImperativeHandle(ref, () => ({
            submit: () => {
                form.submit();
            },
        }));

        return (
            <Form
                form={form}
                layout="vertical"
                initialValues={{
                    offering: "",
                    quantity: 1,
                }}
                onFinish={handleFinish}
            >
                <Form.Item<CreateOrderItem>
                    label="Nombre del producto"
                    name="offering"
                    rules={[
                        { required: true, message: "Por favor selecciona un producto" },
                    ]}
                >
                    <SelectOfferings />
                </Form.Item>
                <div className="grid grid-cols-2">
                    <div className="flex py-2 gap-2 items-center">
                        <Text type="secondary">¿Usar precio personalizado?</Text>
                        <Switch
                            size="small"
                            checked={useCustomPrice}
                            onChange={setUseCustomPrice}
                        />
                    </div>
                    {useCustomPrice && (
                        <Form.Item<CreateOrderItem>
                            className="w-full"
                            rules={[
                                {
                                    required: true,
                                    message: "Por favor ingresa un precio",
                                },
                            ]}
                            label="Precio personalizado"
                            name="customAmount"
                        >
                            <InputNumber
                                className="w-full"
                                min={0}
                                prefix={order.isInternational ? "$" : "S/"}
                            />
                        </Form.Item>
                    )}
                </div>
            </Form>
        );
    },
);

export default AddOrderItemForm;
