import { Form, Input, Button } from "antd";
import type { FormInstance } from "antd";
import { useMutation } from "@tanstack/react-query";
import { createMajor } from "@/features/crm/services/portals/major";

type ErrorData = { name?: unknown; detail?: unknown };
type APIError = { response?: { data?: ErrorData } };

export type CreateMajorFormProps = {
    form: FormInstance;
    onClose: () => void;
    onCreated: () => void;
    notify: (type: "success" | "error", message: string, description?: string) => void;
};

export default function CreateMajorForm({
    form,
    onClose,
    onCreated,
    notify,
}: CreateMajorFormProps) {
    const { mutate, isPending } = useMutation({
        mutationFn: createMajor,
        onSuccess: () => {
            notify(
                "success",
                "Carrera creada",
                "La carrera ha sido creada exitosamente",
            );
            onCreated();
            onClose();
        },
        onError: (err: unknown) => {
            const res = (err as APIError).response?.data ?? {};
            const nameVal = res.name;
            const fieldMsg = Array.isArray(nameVal) ? nameVal[0] : nameVal;
            const detail = res.detail;
            if (fieldMsg) {
                form.setFields([{ name: "name", errors: [String(fieldMsg)] }]);
            }
            const friendly =
                fieldMsg ||
                detail ||
                "No se pudo crear la carrera. Verifica el nombre e inténtalo nuevamente.";
            notify("error", "Error al crear la carrera", friendly);
        },
    });

    return (
        <Form
            form={form}
            layout="vertical"
            onFinish={(v: { name: string }) => mutate(v.name)}
        >
            <Form.Item
                name="name"
                label="Nombre de la Carrera"
                rules={[{ required: true, message: "Ingrese el nombre" }]}
            >
                <Input placeholder="Ej. Ingeniería de Sistemas" disabled={isPending} />
            </Form.Item>
            <div className="flex justify-end gap-2">
                <Button onClick={onClose} disabled={isPending}>
                    Cancelar
                </Button>
                <Button type="primary" htmlType="submit" loading={isPending}>
                    Guardar
                </Button>
            </div>
        </Form>
    );
}
