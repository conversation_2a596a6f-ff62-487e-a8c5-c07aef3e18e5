import { Select, SelectProps } from "antd";
import { useOrders } from "../../hooks/use-order";
import { Link } from "react-router-dom";
import { ExternalLink } from "lucide-react";
import { ListOrdersQueryParams } from "../../services/portals/order";
import { useDebounce } from "@hooks/use-debounce";
import { useState } from "react";
import type { OrderSummary } from "../../types/activity";

interface SelectOrderProps extends Omit<SelectProps, "options"> {
    value?: string; // Add value prop for controlled component
    onChange?: (value: string) => void; // Add onChange prop
    selectedOrder?: OrderSummary;
}
export default function SelectOrder({
    value,
    onChange,
    selectedOrder,
    ...restProps
}: SelectOrderProps) {
    const [query, setQuery] = useState<Omit<
        ListOrdersQueryParams,
        "page" | "pageSize"
    > | null>(null);

    const debouncedQuery = useDebounce(query, 1000);

    const { orders, isLoading } = useOrders(debouncedQuery || undefined);

    const orderOptions: SelectProps["options"] = orders?.map((order) => ({
        value: order.oid,
        label: (
            <div className="flex justify-between items-center">
                <span>{order.owner.fullName}</span>
                <span className="text-xs text-gray-600">#{order.oid?.slice(-6)}</span>
                <Link to={`/crm/orders/${order.oid}`} title="View Contact">
                    <ExternalLink size={14} />
                </Link>
            </div>
        ),
        data: {
            ...order,
        },
    }));

    if (value && selectedOrder) {
        const isValueInOptions = orderOptions.some((option) => option.value === value);
        if (!isValueInOptions) {
            orderOptions.unshift({
                value: value,
                label: (
                    <div className="flex items-center gap-2">
                        <span>{selectedOrder.owner}</span>
                        <span className="text-xs text-gray-600">
                            #{selectedOrder.oid?.slice(-6)}
                        </span>
                        <Link
                            to={`/crm/orders/${selectedOrder.oid}`}
                            title="View Contact"
                        >
                            <ExternalLink size={14} />
                        </Link>
                    </div>
                ),
                data: {
                    ...selectedOrder,
                },
            });
        }
    }

    const handleSearch = (value: string) => {
        setQuery({ search: value });
    };

    const handleClear = () => {
        setQuery(null);
    };

    return (
        <>
            <Select
                {...restProps}
                value={value}
                onChange={onChange}
                onSearch={handleSearch}
                onClear={handleClear}
                options={orderOptions}
                loading={isLoading}
                filterOption={false}
                showSearch
            />
        </>
    );
}
