import { Link, useNavigate, useParams } from "react-router-dom";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Button, Image, Modal, Tag, Typography } from "antd";
import AdminLayout from "@layouts/admin/AdminLayout";

const { Text } = Typography;

import Edit from "@assets/icons/general/edit-stroke.svg?react";
import Trash from "@assets/icons/huge/trash-white.svg?react";
import { useMutation, useQuery } from "@tanstack/react-query";
import { deleteInstructor, retrieveInstructor } from "@services/portals/cms/instructor";
import { useEffect } from "react";
import { AxiosError } from "axios";
import Spinner from "@components/shared/atoms/Spinner";
import { InstructorStatusEnum } from "@myTypes/instructor";
import LinkField from "@components/shared/atoms/LinkField";

export default function InstructorDetailPage() {
    const navigate = useNavigate();
    const { iid } = useParams();

    const [modal, contextHolder] = Modal.useModal();

    const {
        isLoading,
        isError,
        data,
        error: fetchError,
    } = useQuery({
        queryKey: ["instructor", iid],
        queryFn: () => retrieveInstructor(iid),
        enabled: iid !== undefined,
    });

    const deleteMutation = useMutation({
        mutationFn: () => deleteInstructor(iid as string),
        onSuccess: () => {
            navigate("/admin/instructor", { replace: true });
        },
        onError: (error: AxiosError) => {
            console.error(error);
        },
    });

    useEffect(() => {
        if (isError) {
            const error = fetchError as AxiosError;
            if (error.response?.status === 404) {
                navigate("/admin/instructor");
            }
        }
    }, [isError, fetchError, navigate]);

    const handleOnEdit = () => {
        navigate(`/admin/instructor/${iid}/edit`);
    };

    const handleOnDelete = () => {
        modal.confirm({
            title: "¿Estás seguro de eliminar el presente registro?",
            content: "Esta acción no se puede deshacer",
            okText: "Eliminar",
            okButtonProps: { danger: true },
            cancelText: "Cancelar",
            onOk: () => deleteMutation.mutate(),
        });
    };

    return (
        <>
            {contextHolder}
            <AdminLayout>
                <div className="w-full h-full space-y-5 max-w-7xl">
                    <div className="flex justify-between items-center">
                        <div>
                            <h1 className="text-2xl text-black-full">
                                Bienvenido,{" "}
                                <span className="text-blue-full font-semibold">
                                    Gerardo
                                </span>
                            </h1>
                            <Text className="text-sm text-gray-500 font-medium">
                                Gestiona aquí los Testimonios para la Website
                            </Text>
                        </div>
                        <div className="flex gap-3">
                            <Button
                                type="primary"
                                size="large"
                                style={{ fontSize: 16 }}
                                icon={<Edit />}
                                disabled={isError}
                                onClick={handleOnEdit}
                            >
                                Editar
                            </Button>
                            <Button
                                type="primary"
                                size="large"
                                style={{ fontSize: 16 }}
                                icon={<Trash />}
                                danger
                                disabled={isError}
                                onClick={handleOnDelete}
                            >
                                Eliminar
                            </Button>
                        </div>
                    </div>
                    {isLoading ? (
                        <Spinner />
                    ) : (
                        <>
                            <div className="p-5 bg-white-full rounded-lg space-y-5">
                                <div className="flex items-center">
                                    <Breadcrumb
                                        separator=">"
                                        items={[
                                            {
                                                title: (
                                                    <Link
                                                        to="/admin/instructor"
                                                        className="text-base"
                                                    >
                                                        Instructores
                                                    </Link>
                                                ),
                                            },
                                            {
                                                title: (
                                                    <Link
                                                        to={`/admin/instructor/${iid}`}
                                                        className="text-base text-blue-medium"
                                                    >
                                                        {data?.fullName !== undefined
                                                            ? data.fullName
                                                            : "Testimonio"}
                                                    </Link>
                                                ),
                                            },
                                            {
                                                title: (
                                                    <Text className="text-base">
                                                        Detalle
                                                    </Text>
                                                ),
                                            },
                                        ]}
                                    />
                                </div>
                            </div>
                            <div className="p-5 bg-white-full rounded-lg lg:flex lg:justify-between space-y-5">
                                <div className="space-y-2">
                                    <p className="text-gray-400 font-semibold text-sm">
                                        INFORMACIÓN PERSONAL
                                    </p>
                                    <div className="space-y-4">
                                        <div>
                                            <p className="text-sm text-black-medium font-semibold">
                                                Nombres
                                            </p>
                                            <p className="text-black-full">
                                                {data?.fullName}
                                            </p>
                                        </div>
                                        <div>
                                            <p className="text-sm text-black-medium font-semibold">
                                                Título o rol
                                            </p>
                                            <p className="text-black-full">
                                                {data?.title}
                                            </p>
                                        </div>
                                        <div>
                                            <p className="text-sm text-black-medium font-semibold">
                                                Biografía
                                            </p>
                                            <p className="text-black-full">
                                                {data?.biography}
                                            </p>
                                        </div>
                                        <div>
                                            <p className="text-sm text-black-medium font-semibold">
                                                Información destacada
                                            </p>
                                            <p className="text-black-full">
                                                {data?.highlightedInfo}
                                            </p>
                                        </div>
                                        <div>
                                            <p className="text-sm text-black-medium font-semibold">
                                                Estado
                                            </p>
                                            <Tag
                                                className="rounded-full px-3"
                                                bordered={false}
                                                color={
                                                    data?.status === "Published"
                                                        ? "green"
                                                        : "red"
                                                }
                                            >
                                                {data?.status ===
                                                InstructorStatusEnum.Published
                                                    ? "Publicado"
                                                    : "Borrador"}
                                            </Tag>
                                        </div>
                                        <div>
                                            <p className="text-sm text-black-medium font-semibold">
                                                Orden
                                            </p>
                                            <p className="text-black-full">
                                                {data?.order}
                                            </p>
                                        </div>
                                        {data?.facebookUrl && (
                                            <LinkField
                                                url={data?.facebookUrl}
                                                label="Facebook"
                                            />
                                        )}
                                        {data?.linkedinUrl && (
                                            <LinkField
                                                url={data?.linkedinUrl}
                                                label="LinkedIn"
                                            />
                                        )}
                                        {data?.instagramUrl && (
                                            <LinkField
                                                url={data?.instagramUrl}
                                                label="Instagram"
                                            />
                                        )}
                                    </div>
                                </div>
                                <div className="space-y-2 lg:text-end lg:pl-4 lg:border-l-2 border-gray-100">
                                    <p className="text-gray-400 font-semibold text-sm">
                                        CONTENIDO MULTIMEDIA
                                    </p>
                                    <div>
                                        <p className="text-sm text-black-medium font-semibold">
                                            Foto de perfil
                                        </p>
                                        <Image
                                            src={data?.profilePhoto?.url}
                                            alt={data?.profilePhoto?.name}
                                        />
                                    </div>
                                </div>
                            </div>
                        </>
                    )}
                </div>
            </AdminLayout>
        </>
    );
}
