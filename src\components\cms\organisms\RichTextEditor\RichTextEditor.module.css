/* RichTextEditor.module.css */

/* Variables para mantener consistencia con el preview */
:root {
    --heading-color: #1483ff;
    --paragraph-color: #374151;
}

.editorContainer {
    width: 100%;
    background-color: #ffffff;
    border-radius: 0.5rem;
    border: 1px solid #eaeaea;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.editor {
    padding: 1rem 1rem;
    color: var(--paragraph-color);
}

/* Toolbar styling */
:global(.yoopta-toolbar) {
    @apply bg-white-full rounded-md shadow-sm;
    border: 1px solid #eaeaea;
}

:global(.yoopta-toolbar-button) {
    @apply text-black-medium hover:bg-blue-low hover:text-blue-full transition-colors;
    border-radius: 4px;
    margin: 2px;
}

:global(.yoopta-toolbar-button-active) {
    @apply bg-blue-low text-blue-full;
}

/* Action menu styling (slash commands) */
:global(.yoopta-action-menu) {
    @apply bg-white-full rounded-lg shadow-md;
    border: 1px solid #eaeaea;
}

:global(.yoopta-action-menu-item) {
    @apply hover:bg-blue-low rounded-md transition-colors;
    padding: 8px 12px;
}

:global(.yoopta-action-menu-item-active) {
    @apply bg-blue-low text-blue-full;
}

/* Content styling */
:global(.yoopta-editor h1) {
    font-size: clamp(2rem, 5vw, 3rem);
    @apply font-bold mt-8 mb-4;
    color: var(--heading-color);
}

:global(.yoopta-editor h2) {
    font-size: clamp(1.5rem, 4vw, 2rem);
    @apply font-semibold mt-6 mb-3;
    color: var(--heading-color);
}

:global(.yoopta-editor h3) {
    font-size: clamp(1.25rem, 3vw, 1.5rem);
    @apply font-medium mt-4 mb-2;
    color: var(--heading-color);
}

:global(.yoopta-editor p) {
    @apply text-base my-4 leading-tight;
    color: var(--paragraph-color);
}

:global(.yoopta-editor blockquote) {
    @apply leading-relaxed bg-blue-50 my-4 p-6 rounded-lg border-l-4;
    border-left-color: #1483ff;
    color: var(--paragraph-color);
}

:global(.yoopta-editor ul),
:global(.yoopta-editor ol) {
    @apply pl-6 space-y-1 my-4;
    color: var(--paragraph-color);
}

:global(.yoopta-editor ul) {
    @apply list-disc;
}

:global(.yoopta-editor ol) {
    @apply list-decimal;
}

:global(.yoopta-editor li) {
    @apply leading-relaxed;
}

:global(.yoopta-editor a) {
    @apply text-blue-600 hover:text-blue-800 underline;
}

:global(.yoopta-editor code) {
    @apply bg-gray-100 text-red-600 px-1 py-0.5 rounded text-sm font-mono;
}

:global(.yoopta-editor img) {
    @apply rounded-md shadow-md max-w-full mx-auto my-6;
}

:global(.yoopta-editor hr) {
    @apply my-8 border-t border-gray-200;
}

/* Link tool styling */
:global(.yoopta-link-tool) {
    @apply bg-white-full rounded-md shadow-sm p-2;
    border: 1px solid #eaeaea;
}

:global(.yoopta-link-tool-input) {
    @apply border border-gray-200 rounded-md px-2 py-1 focus:outline-none focus:ring-1 focus:ring-blue-medium;
}

:global(.yoopta-link-tool-button) {
    @apply bg-blue-medium text-white-full rounded-md px-3 py-1 hover:bg-blue-full transition-colors;
}

/* Placeholder styling */
:global(.yoopta-editor-placeholder) {
    @apply text-gray-400 opacity-70 italic;
    font-family: inherit;
}
