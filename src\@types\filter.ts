import type { UseQueryResult } from "@tanstack/react-query";
import type { DatePickerProps, FormItemProps, SelectProps } from "antd";
import type { RangePickerProps } from "antd/es/date-picker";

type FilterTypeEnum = {
    date: "date";
    rangepicker: "rangepicker";
    checkbox: "checkbox";
    select: "select";
    multiSelect: "multi-select";
};

// Select filters
export type SelectFilter = {
    type: FilterTypeEnum["select"];

    /**
     * @template {"custom"} options
     * Custom: use a useQuery hook to fetch options from backend
     *
     * @template {"tags" | "multiple"} mode
     * Use mode to enable multiselect
     */
    selectprops: Omit<SelectProps, "options"> &
        (
            | {
                  options: Exclude<SelectProps["options"], "custom">;
                  optionsQuery?: never;
              }
            | {
                  options: "custom";
                  /**
                   * @example
                   * export const useInstructores = () =>
                      useQuery({
                          queryKey: ["instructors"],
                          queryFn: async () =>
                              getInstructors(), // Ideal not to be paginated
                          select: (data) =>  // map data to options
                              data.results.map((instructor) => ({
                                  label: instructor.fullName,
                                  value: instructor.iid,
                              })),
                      });

                      optionsQuery: useInstructores,
                  */
                  optionsQuery: () => UseQueryResult<SelectProps["options"]>;
              }
        );
};

// Dates
export type DateFilter = {
    type: FilterTypeEnum["date"];
    value?: string;
    datepickerprops?: DatePickerProps;
};

export type RangeDateFilter = {
    type: FilterTypeEnum["rangepicker"];
    value?: [string, string];
    datepickerprops?: RangePickerProps;
};

// Boolean (checkbox)
export type ChekcboxFilter = {
    type: FilterTypeEnum["checkbox"];
    value?: boolean;
};

export type FilterPanelItem<T> = FormItemProps<T> &
    (SelectFilter | DateFilter | ChekcboxFilter | RangeDateFilter);
