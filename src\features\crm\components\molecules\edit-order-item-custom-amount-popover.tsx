import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Tooltip } from "antd";
import { OrderItem } from "../../types/order";
import { EditIcon } from "lucide-react";
import { useState } from "react";
import { useUpdateOrderItem } from "../../hooks/use-order-item";

type EditOrderItemCustomAmountPopoverProps = {
    orderItem: OrderItem;
    orderId: string;
    isInternational?: boolean;
};

const EditOrderItemCustomAmountPopover = ({
    orderItem,
    orderId,
    isInternational = false,
}: EditOrderItemCustomAmountPopoverProps) => {
    const [open, setOpen] = useState(false);
    const { message, notification } = App.useApp();

    const [customAmount, setCustomAmount] = useState<number | null>(
        orderItem.customAmount || null,
    );

    const { mutate: updateOrderItemMutate, isPending } = useUpdateOrderItem({
        onUpdateOrderItemSuccess: () => {
            message.success({
                content: "Precio personalizado actualizado",
                duration: 2,
            });
            setOpen(false);
        },
        onUpdateOrderItemError: () => {
            notification.error({
                message: "Error al actualizar el precio personalizado",
                description:
                    "Ha ocurrido un error al intentar actualizar el precio personalizado",
                duration: 2,
            });
        },
    });

    const handleOpenChange = (newOpen: boolean) => {
        setOpen(newOpen);
        if (newOpen) {
            // Reset to current value when opening
            setCustomAmount(orderItem.customAmount || null);
        }
    };

    const handleUpdateCustomAmount = () => {
        if (customAmount !== null && customAmount >= 0) {
            updateOrderItemMutate({
                oid: orderId,
                orderItem: {
                    oiid: orderItem.oiid,
                    customAmount: customAmount,
                },
            });
        } else {
            setOpen(false);
        }
    };

    const handleClearCustomAmount = () => {
        updateOrderItemMutate({
            oid: orderId,
            orderItem: {
                oiid: orderItem.oiid,
                customAmount: null,
            },
        });
    };

    return (
        <Popover
            content={
                <>
                    <div className="space-y-3">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Precio personalizado
                            </label>
                            <InputNumber
                                value={customAmount}
                                onChange={(value) => setCustomAmount(value)}
                                min={0}
                                precision={2}
                                prefix={isInternational ? "$ " : "S/ "}
                                placeholder="Ingrese el precio personalizado"
                                className="w-full"
                                size="large"
                            />
                        </div>

                        {orderItem.customAmount && (
                            <Button
                                type="text"
                                danger
                                size="small"
                                onClick={handleClearCustomAmount}
                                loading={isPending}
                                block
                            >
                                Eliminar precio personalizado
                            </Button>
                        )}
                    </div>

                    <Divider className="my-3" />

                    <div className="flex gap-2">
                        <Button
                            block
                            onClick={() => setOpen(false)}
                            disabled={isPending}
                        >
                            Cancelar
                        </Button>
                        <Button
                            block
                            type="primary"
                            onClick={handleUpdateCustomAmount}
                            loading={isPending}
                            disabled={customAmount === null || customAmount < 0}
                        >
                            Actualizar
                        </Button>
                    </div>
                </>
            }
            trigger="click"
            open={open}
            onOpenChange={handleOpenChange}
            title="Editar precio personalizado"
            placement="topRight"
        >
            <Tooltip title="Editar precio personalizado">
                <EditIcon
                    className="text-blue-500 hover:text-blue-600 hover:cursor-pointer transition-colors"
                    size={16}
                />
            </Tooltip>
        </Popover>
    );
};

export default EditOrderItemCustomAmountPopover;
