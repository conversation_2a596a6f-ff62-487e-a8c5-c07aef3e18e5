import React, { createContext } from "react";
import { VIEWPORT_CONFIGS, ViewportConfig, ViewportSize } from "./config";

interface ResponsivePreviewContextType {
    currentViewport: ViewportSize;
    config: ViewportConfig;
    isViewport: (viewport: ViewportSize) => boolean;
    isMobile: boolean;
    isTablet: boolean;
    isDesktop: boolean;
}

const ResponsivePreviewContext = createContext<
    ResponsivePreviewContextType | undefined
>(undefined);

interface ResponsivePreviewProviderProps {
    children: React.ReactNode;
    currentViewport: ViewportSize;
}

export function ResponsivePreviewProvider({
    children,
    currentViewport,
}: ResponsivePreviewProviderProps) {
    const config = VIEWPORT_CONFIGS[currentViewport];

    const isViewport = (viewport: ViewportSize) => currentViewport === viewport;
    const isMobile = currentViewport === "mobile";
    const isTablet = currentViewport === "tablet";
    const isDesktop = currentViewport === "desktop";

    const value: ResponsivePreviewContextType = {
        currentViewport,
        config,
        isViewport,
        isMobile,
        isTablet,
        isDesktop,
    };

    return (
        <ResponsivePreviewContext.Provider value={value}>
            {children}
        </ResponsivePreviewContext.Provider>
    );
}

export default ResponsivePreviewContext;
