import { memo, useMemo } from "react";
import {
    Avatar,
    Button,
    Col,
    Dropdown,
    List,
    MenuProps,
    Row,
    Typography,
    Tag,
    Tooltip,
    Alert,
    App,
} from "antd";
import { useProgramEnrollments } from "../../hooks/use-program";
import StatCard from "@/features/crm/components/atoms/stat-card";
import {
    MoreVerticalIcon,
    Users2,
    AlertTriangle,
    CheckCircle,
    XCircle,
    Clock,
    RefreshCw,
} from "lucide-react";
import { ProgramEnrollment } from "../../types/program";
import { useNavigate } from "react-router-dom";
import { OrderItemInvitationStatus } from "@/features/crm/types/order";
import { useCreateEnrollment } from "@/features/crm/hooks/use-order-item";

const { Title } = Typography;

interface ProgramEnrollmentsDetailProps {
    oid: string;
}

// Helper function to render conciliation badge
const renderConciliationBadge = (needsConciliation: boolean) => {
    if (!needsConciliation) return null;

    return (
        <Tooltip title="El estudiante se encuentra registrado en la plataforma Classroom, pero no tiene una matrícula creada">
            <Tag
                color="warning"
                icon={<AlertTriangle size={14} />}
                className="flex items-center gap-1 w-fit"
            >
                Necesita conciliación
            </Tag>
        </Tooltip>
    );
};

// Helper function to render classroom invitation status badge
const renderClassroomStatusBadge = (
    extInvitationStatus: string,
    acceptedInvitation: boolean | null,
) => {
    const status = extInvitationStatus as OrderItemInvitationStatus;

    if (status === OrderItemInvitationStatus.SENT && acceptedInvitation === true) {
        return (
            <Tooltip title="El estudiante ha aceptado la invitación y está inscrito en Classroom">
                <Tag
                    color="success"
                    icon={<CheckCircle size={14} />}
                    className="flex items-center gap-1 w-fit"
                >
                    Inscrito en Classroom
                </Tag>
            </Tooltip>
        );
    }

    if (status === OrderItemInvitationStatus.SENT && acceptedInvitation === false) {
        return (
            <Tooltip title="La invitación ha sido enviada pero el estudiante aún no la ha aceptado">
                <Tag
                    color="processing"
                    icon={<Clock size={14} />}
                    className="flex items-center gap-1 w-fit"
                >
                    Pendiente de aceptación
                </Tag>
            </Tooltip>
        );
    }

    if (status === OrderItemInvitationStatus.ERROR) {
        return (
            <Tooltip title="Hubo un error al enviar la invitación a Classroom">
                <Tag
                    color="error"
                    icon={<XCircle size={14} />}
                    className="flex items-center gap-1 w-fit"
                >
                    Error al enviar invitación
                </Tag>
            </Tooltip>
        );
    }

    return null;
};

// Helper function to build conditional menu items
const buildMenuItems = (enrollment: ProgramEnrollment): MenuProps["items"] => {
    const items: MenuProps["items"] = [];

    // Always show "Ver matrícula" if the enrollment has a key
    if (enrollment.key) {
        items.push({
            key: "viewEnrollment",
            label: (
                <div className="flex items-center gap-2 text-blue-full">
                    Ver matrícula
                </div>
            ),
        });
    } else {
        // Si tiene usuario y order item, es posible crear una matrícula
        if (enrollment.user && enrollment.orderItem) {
            items.push({
                key: "createErollment",
                label: (
                    <div className="flex items-center gap-2 text-blue-full">
                        Crear matrícula
                    </div>
                ),
            });
        }
    }

    // Only show "Ver orden" if orderItem.order exists
    if (enrollment.orderItem?.order) {
        items.push({
            key: "viewOrder",
            label: (
                <div className="flex items-center gap-2 text-blue-full">Ver orden</div>
            ),
        });
    }

    // Always show delete option
    items.push({
        key: "delete",
        disabled: true, // TODO: Validate and implement delete functionality
        label: (
            <div className="flex items-center gap-2 text-state-red-full">
                Eliminar de la clase
            </div>
        ),
    });

    return items;
};

// Memoized list item component for better performance
const EnrollmentListItem = memo(
    ({
        item,
        onRowAction,
    }: {
        item: ProgramEnrollment;
        onRowAction: (key: string, record: ProgramEnrollment) => void;
    }) => {
        const menuItems = useMemo(() => buildMenuItems(item), [item]);

        return (
            <List.Item>
                <List.Item.Meta
                    avatar={
                        <Avatar>
                            {item.user?.fullName?.[0] ?? item.fullName?.[0] ?? "S"}
                        </Avatar>
                    }
                    title={
                        <div className="flex flex-col gap-2">
                            <span>
                                {item.user?.fullName || item.fullName || "Sin nombre"}
                            </span>
                        </div>
                    }
                    description={
                        <div className="space-y-2">
                            <div>{item.user?.email ?? ""}</div>
                            <div className="flex flex-wrap gap-2">
                                {renderConciliationBadge(item.needsConciliation)}
                                {item.orderItem &&
                                    renderClassroomStatusBadge(
                                        item.orderItem.extInvitationStatus,
                                        item.acceptedInvitation,
                                    )}
                            </div>
                        </div>
                    }
                />
                <div>
                    <Dropdown
                        trigger={["click"]}
                        menu={{
                            items: menuItems,
                            onClick: ({ key }) => {
                                onRowAction(key, item);
                            },
                        }}
                    >
                        <Button icon={<MoreVerticalIcon />} type="text" size="small" />
                    </Dropdown>
                </div>
            </List.Item>
        );
    },
);

EnrollmentListItem.displayName = "EnrollmentListItem";

const ProgramEnrollmentsDetail = memo(({ oid }: ProgramEnrollmentsDetailProps) => {
    const navigate = useNavigate();
    const { data, isError, isLoading, refetch } = useProgramEnrollments(oid);
    const { message } = App.useApp();

    const { mutate: createEnrollment } = useCreateEnrollment({
        onCreateEnrollmentSuccess: () => {
            message.success("Matrícula creada exitosamente");
            refetch();
        },
    });

    const handleRowAction = (key: string, record: ProgramEnrollment) => {
        if (key === "viewEnrollment") {
            navigate(`/lms/enrollments/${record.key}`);
        }

        if (key === "viewOrder") {
            if (!record.orderItem?.order) return;
            navigate(`/crm/orders/${record.orderItem.order}`);
        }

        if (key === "createErollment") {
            if (!record.orderItem) return;
            createEnrollment({
                oid: record.orderItem.order,
                orderItemId: record.orderItem.id,
            });
        }

        if (key === "delete") {
            // TODO: Implement delete functionality
        }
    };

    const handleRetry = () => {
        refetch?.();
    };

    // Mostrar error si hay un problema al cargar los datos
    if (isError) {
        return (
            <section>
                <div className="flex justify-center items-center min-h-[400px]">
                    <div className="text-center max-w-md">
                        <Alert
                            message="Error al cargar las inscripciones"
                            description="Hubo un problema al cargar las inscripciones del programa. Por favor, intenta nuevamente."
                            type="error"
                            showIcon
                            action={
                                <Button
                                    size="small"
                                    type="primary"
                                    icon={<RefreshCw size={14} />}
                                    onClick={handleRetry}
                                >
                                    Reintentar
                                </Button>
                            }
                        />
                    </div>
                </div>
            </section>
        );
    }

    return (
        <section>
            {/* Responsive stat cards */}
            <Row gutter={[16, 16]}>
                <Col xs={12} md={8}>
                    <StatCard
                        title="Total de inscripciones"
                        value={data?.enrollmentsCount ?? 0}
                        icon={<Users2 size={24} className="text-blue-500" />}
                        color="#4096ff"
                        loading={!data && isLoading}
                    />
                </Col>
                <Col xs={12} md={8}>
                    <StatCard
                        title="Invitaciones aceptadas"
                        value={
                            data?.acceptedInvitationCount !== null
                                ? data?.acceptedInvitationCount || 0
                                : "-"
                        }
                        icon={<CheckCircle size={24} className="text-green-500" />}
                        color="#52c41a"
                        loading={!data && isLoading}
                    />
                </Col>
                <Col xs={12} md={8}>
                    <StatCard
                        title="Necesitan conciliación"
                        value={
                            data?.needsConciliationCount !== null
                                ? data?.needsConciliationCount || 0
                                : "-"
                        }
                        icon={<AlertTriangle size={24} className="text-orange-500" />}
                        color="#fa8c16"
                        loading={!data && isLoading}
                    />
                </Col>
            </Row>
            {data?.errorMessage && (
                <Alert
                    message={data.errorMessage}
                    type="warning"
                    showIcon
                    className="mt-8"
                    action={
                        <Button size="small" type="link" onClick={handleRetry}>
                            Recargar
                        </Button>
                    }
                />
            )}
            <Row gutter={[16, 16]}>
                <Col xs={24} className="mt-4">
                    <div className="p-4">
                        <Title level={2} color="gray">
                            Estudiantes
                        </Title>
                        <List
                            itemLayout="horizontal"
                            dataSource={data?.enrollments ?? []}
                            loading={!data && isLoading}
                            renderItem={(item) => (
                                <EnrollmentListItem
                                    key={item.key}
                                    item={item}
                                    onRowAction={handleRowAction}
                                />
                            )}
                        />
                    </div>
                </Col>
            </Row>
        </section>
    );
});

ProgramEnrollmentsDetail.displayName = "ProgramEnrollmentsDetail";

export default ProgramEnrollmentsDetail;
