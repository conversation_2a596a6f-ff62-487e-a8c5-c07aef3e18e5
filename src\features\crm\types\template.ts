import { UploadFile } from "antd";
import type { TemplateType } from "./template-type";

export const DEFAULT_PAGE = 1;
export const DEFAULT_PAGE_SIZE = 10;

/**
 *  DRAFT = "DRAFT"
    IN_REVIEW = "IN_REVIEW"
    REJECTED = "REJECTED"
    APPROVED = "APPROVED"
    PAUSED = "PAUSED"
    DISABLED = "DISABLED"

 */
export enum TemplateStatus {
    DRAFT = "DRAFT",
    IN_REVIEW = "IN_REVIEW",
    APPROVED = "APPROVED",
    PAUSED = "PAUSED",
    DISABLED = "DISABLED",
    REJECTED = "REJECTED",
}

export const TemplateStatusLabel: Record<TemplateStatus, string> = {
    [TemplateStatus.DRAFT]: "Borrador",
    [TemplateStatus.IN_REVIEW]: "En revisión",
    [TemplateStatus.APPROVED]: "Aprobado",
    [TemplateStatus.PAUSED]: "Pausado",
    [TemplateStatus.DISABLED]: "Inactivo",
    [TemplateStatus.REJECTED]: "Rechazado",
};
export type TemplateButton = {
    type: "URL";
    text: string;
    url: string;
    urlType: "static" | "dynamic";
    example?: string[];
};
export type TemplateHeaderImage = {
    fid: string;
    name: string;
    url: string;
};

export type Template = {
    tid: string;
    name: string;
    status: TemplateStatus;
    headerImage?: TemplateHeaderImage | null;
    bodyText: string;
    // positionalParamsExample?: string[] | null; Deprecated
    buttons?: TemplateButton[] | null;
    extReference?: string | null;
    createdAt: string | Date;
    updatedAt: string | Date;
};

export type RetrieveTemplate = Template & {
    parsedBodyText: string;
    type: TemplateType;
};

export type CreateTemplateFormValues = Partial<
    Omit<Template, "tid" | "status" | "createdAt" | "updatedAt">
> & {
    type?: string;
    completeInfo?: boolean;
};

export type CreateTemplateBody = Partial<
    Omit<Template, "tid" | "createdAt" | "status" | "updatedAt">
> & {
    type?: string | null;
};

export type PartialUpdateTemplateBody = Partial<
    Omit<Template, "tid" | "key" | "createdAt" | "updatedAt">
> & {
    headerImageFile?: UploadFile[];
    deleteHeaderImage?: boolean;
    type?: string | null;
};

export type ListTemplatesQueryParams = {
    page?: number;
    pageSize?: number;
    search?: string;
    status?: TemplateStatus;
};

export type TestTemplateBody = {
    phoneNumber: string;
};
