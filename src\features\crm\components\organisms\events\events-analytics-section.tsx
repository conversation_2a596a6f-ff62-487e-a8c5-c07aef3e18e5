import React from "react";
import { Row, Col } from "antd";
import EventPieChartWithToggle from "@/features/crm/components/molecules/events/event-pie-chart-with-toggle";
import DiffusionChannelsChart from "@/features/crm/components/molecules/events/diffusion-channels-chart";
import {
    useDashboardEventsAnalytics,
    useDashboardEventsSummary,
} from "@/features/crm/hooks/use-dashboard-events";
import type { DashboardEventQueryParams } from "@/features/crm/types/dashboard/events";

interface EventsAnalyticsSectionProps {
    queryParams: DashboardEventQueryParams;
}

const EventsAnalyticsSection: React.FC<EventsAnalyticsSectionProps> = ({
    queryParams,
}) => {
    const { data: analyticsData, isLoading } = useDashboardEventsAnalytics(queryParams);
    const { data: summaryData, isLoading: summaryLoading } =
        useDashboardEventsSummary(queryParams);

    return (
        <div className="space-y-6">
            {/* Charts Row */}
            <Row gutter={[16, 16]}>
                <Col xs={24} lg={12}>
                    <EventPieChartWithToggle
                        eventByTypeData={analyticsData?.eventByType}
                        eventStatsData={summaryData?.stats}
                        isLoading={isLoading || summaryLoading}
                    />
                </Col>
                <Col xs={24} lg={12}>
                    <DiffusionChannelsChart
                        data={analyticsData?.diffusionChannels}
                        isLoading={isLoading}
                    />
                </Col>
            </Row>
        </div>
    );
};

export default EventsAnalyticsSection;
