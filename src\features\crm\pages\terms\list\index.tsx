import CrmLayout from "@/features/crm/layout";
import WelcomeBar from "@components/shared/molecules/WelcomeBar";
import { Badge, Input, Modal, Pagination, Typography, Form, Button, App } from "antd";
import { useState } from "react";
import { useSearchParams } from "react-router-dom";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
    deleteTerm,
    getTerms,
    ListTermsQueryParams,
} from "@/features/crm/services/portals/term";
import TermsTable from "@/features/crm/components/organisms/terms-table";
import { Plus } from "lucide-react";
import type { Term } from "@/features/crm/types/term";
import CreateTermForm from "@/features/crm/components/molecules/create-term-form";
import EditTermForm from "@/features/crm/components/molecules/edit-term-form";

const { Search } = Input;
const { Text } = Typography;

const DEFAULT_PAGE = 1;
const DEFAULT_PAGE_SIZE = 10;

export default function TermsListPage() {
    const [searchParams, setSearchParams] = useSearchParams();
    const [isCreateOpen, setIsCreateOpen] = useState(false);
    const [isEditOpen, setIsEditOpen] = useState(false);
    const [editingTerm, setEditingTerm] = useState<Term | null>(null);
    const [form] = Form.useForm();
    const [editForm] = Form.useForm();
    const queryClient = useQueryClient();
    const { notification } = App.useApp();

    const page = Number(searchParams.get("page")) || DEFAULT_PAGE;
    const pageSize = Number(searchParams.get("pageSize")) || DEFAULT_PAGE_SIZE;
    const search = searchParams.get("search") || "";

    const queryParams: ListTermsQueryParams = {
        page,
        pageSize,
        ordering: "name",
        ...(search ? { search } : {}),
    };

    const { data, isLoading } = useQuery({
        queryKey: ["terms", queryParams],
        queryFn: async () => getTerms(queryParams),
        refetchOnWindowFocus: false,
    });

    const { count = 0, results: terms = [] } = data || {};

    const handleSetPage = (p: number, ps: number) => {
        setSearchParams((prev: URLSearchParams) => {
            prev.set("page", String(p));
            prev.set("pageSize", String(ps));
            return prev;
        });
    };

    const onSearch = (value: string) => {
        setSearchParams((prev: URLSearchParams) => {
            if (value) prev.set("search", value);
            else prev.delete("search");
            prev.set("page", "1");
            return prev;
        });
    };

    const handleEdit = (term: Term) => {
        setEditingTerm(term);
        editForm.setFieldsValue({ name: term.name });
        setIsEditOpen(true);
    };

    const deleteMutation = useMutation({
        mutationFn: (tid: string) => deleteTerm(tid),
        onSuccess: () => {
            notification.success({ message: "Ciclo eliminado" });
            queryClient.invalidateQueries({ queryKey: ["terms"] });
        },
        onError: () => {
            notification.error({ message: "No se pudo eliminar el ciclo" });
        },
    });

    const handleDelete = (term: Term) => {
        Modal.confirm({
            title: "Eliminar ciclo",
            content: `¿Seguro que deseas eliminar "${term.name}"? Esta acción no se puede deshacer`,
            okText: "Eliminar",
            okButtonProps: { danger: true },
            cancelText: "Cancelar",
            onOk: () => deleteMutation.mutate(term.tid),
        });
    };

    return (
        <CrmLayout>
            <div className="w-full h-full space-y-5">
                <div className="flex justify-between items-center">
                    <WelcomeBar helperText="Gestiona aquí los ciclos académicos (Terms)" />
                    <div className="flex gap-3">
                        <Button
                            type="primary"
                            size="large"
                            icon={<Plus />}
                            onClick={() => setIsCreateOpen(true)}
                        >
                            Agregar
                        </Button>
                    </div>
                </div>
                <Modal
                    centered
                    open={isCreateOpen}
                    onCancel={() => {
                        setIsCreateOpen(false);
                        form.resetFields();
                    }}
                    footer={false}
                    title={"Agregar Ciclo Académico"}
                >
                    <CreateTermForm
                        form={form}
                        onClose={() => {
                            setIsCreateOpen(false);
                            form.resetFields();
                        }}
                        onCreated={() =>
                            queryClient.invalidateQueries({ queryKey: ["terms"] })
                        }
                        notify={(type, message, description) =>
                            notification[type]({ message, description })
                        }
                    />
                </Modal>

                <Modal
                    centered
                    open={isEditOpen}
                    onCancel={() => {
                        setIsEditOpen(false);
                        setEditingTerm(null);
                        editForm.resetFields();
                    }}
                    footer={false}
                    title={"Editar Ciclo Académico"}
                >
                    {editingTerm && (
                        <EditTermForm
                            form={editForm}
                            tid={editingTerm.tid}
                            initialName={editingTerm.name}
                            onClose={() => {
                                setIsEditOpen(false);
                                setEditingTerm(null);
                                editForm.resetFields();
                            }}
                            onUpdated={() =>
                                queryClient.invalidateQueries({ queryKey: ["terms"] })
                            }
                            notify={(type, message, description) =>
                                notification[type]({ message, description })
                            }
                        />
                    )}
                </Modal>

                <div className="p-5 bg-white-full rounded-lg space-y-5">
                    <div className="flex flex-col lg:flex-row justify-between items-center gap-3">
                        <Text className="text-black-medium text-2xl font-semibold">
                            Ciclos Académicos <Badge count={count} color="blue" />
                        </Text>
                        <Search
                            size="large"
                            placeholder="Buscar por nombre"
                            defaultValue={search}
                            key={search}
                            onSearch={onSearch}
                            enterButton
                            allowClear
                            className="max-w-screen-sm"
                        />
                    </div>
                </div>

                <TermsTable
                    data={terms}
                    loading={isLoading}
                    onEdit={handleEdit}
                    onDelete={handleDelete}
                />

                <div className="flex justify-between items-center p-4 bg-white-full rounded-lg shadow-sm">
                    <Text type="secondary">
                        {terms.length} de {count} ciclos
                    </Text>
                    <Pagination
                        current={page}
                        pageSize={pageSize}
                        total={count}
                        onChange={handleSetPage}
                        showSizeChanger
                    />
                </div>
            </div>
        </CrmLayout>
    );
}
