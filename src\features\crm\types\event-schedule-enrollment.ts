import { PaginatedResponse } from "@myTypes/base";

type AuditBaseType = {
    createdAt: string;
    updatedAt: string;
};

export type EventScheduleEnrollmentUser = {
    uid: string;
    firstName: string;
    lastName: string;
    fullName: string;
    email: string;
    phoneNumber: string;
};

export type EventScheduleEnrollmentPartnership = {
    key: string;
    pid: string;
    name: string;
    institution: string;
} & AuditBaseType;

export type EventScheduleEnrollment = {
    id: number;
    user: EventScheduleEnrollmentUser;
    firstName: string;
    lastName: string;
    fullName: string;
    email: string;
    phoneNumber: string;
    occupation: string;
    major: string;
    term: string;
    university: string;
    interests: string[];
    diffusionChannel: string;
    hasContact: boolean;
    needsConciliation: boolean;
    alreadyLead: boolean;
    partnership: EventScheduleEnrollmentPartnership | null;
} & AuditBaseType;

export type ListEventScheduleEnrollmentsQueryParams = {
    page?: number;
    pageSize?: number;
    search?: string;
    hasContact?: boolean;
    needsConciliation?: boolean;
    alreadyLead?: boolean;
    partnership?: string; // Partnership ID (pid)
};

export type EventScheduleEnrollmentsResponse =
    PaginatedResponse<EventScheduleEnrollment>;

export const DEFAULT_PAGE = 1;
export const DEFAULT_PAGE_SIZE = 20;
