import { App, DraggerProps, Typography, Upload, UploadFile, Image } from "antd";
import { CloudUpload } from "lucide-react";
import { RcFile } from "antd/es/upload";
import ImgCrop from "antd-img-crop";
import {
    uploadContactProfilePhoto,
    removeContactProfilePhoto,
} from "../../services/portals/contact";
import { ProfilePhoto } from "../../types/contact";
import { useState } from "react";
const { Dragger } = Upload;
const { Text } = Typography;

interface UploadContactProfilePhotoProps {
    cid: string;
    initialProfilePhoto?: ProfilePhoto;
}

export default function UploadContactProfilePhoto({
    cid,
    initialProfilePhoto,
}: UploadContactProfilePhotoProps) {
    const { notification } = App.useApp();

    const [fileList, setFileList] = useState<UploadFile[]>(
        initialProfilePhoto
            ? [
                  {
                      uid: initialProfilePhoto.fid,
                      name: initialProfilePhoto.name,
                      fileName: initialProfilePhoto.name,
                      url: initialProfilePhoto.url,
                      status: "done",
                      type: initialProfilePhoto.contentType,
                  },
              ]
            : [],
    );
    const [previewOpen, setPreviewOpen] = useState(false);
    const profilePhotoDraggerProps: DraggerProps = {
        customRequest: async (options) => {
            const { file, onSuccess, onError } = options;
            try {
                // Define proper types for file
                const fileToUpload = file as RcFile & Partial<UploadFile>;
                const fileRes = await uploadContactProfilePhoto(cid, fileToUpload);
                if (fileRes && onSuccess) {
                    const updatedFile = { ...fileToUpload, uid: fileRes.fid };
                    onSuccess(fileRes, updatedFile);
                }
            } catch (error) {
                if (onError)
                    onError(
                        new Error(
                            error instanceof Error ? error.message : "Unknown error",
                        ),
                        file,
                    );
            }
        },
        onChange(info) {
            setFileList(info.fileList);
        },
        multiple: false,
        maxCount: 1,
        accept: ".jpg, .jpeg, .png, .webp, .gif, .bmp, .tiff, .svg",
        beforeUpload: (file) => {
            const isLt2M = file.size / 1024 / 1024 < 2;
            if (!isLt2M) {
                notification.error({
                    message: "El archivo debe ser menor a 2MB",
                    duration: 2,
                });
            }
            return isLt2M;
        },
        onPreview: async () => {
            setPreviewOpen(true);
        },
        onRemove: async (file) => {
            const fid = file?.response?.fid || file?.uid;
            const res = await removeContactProfilePhoto(cid, fid);
            if (res) {
                notification.success({
                    message: "Archivo eliminado correctamente",
                    duration: 2,
                });
            } else {
                notification.error({
                    message: "Error al eliminar el archivo",
                    duration: 2,
                });
            }
        },
        showUploadList: {
            showDownloadIcon: true,
            showRemoveIcon: true,
        },
        fileList: fileList,
    };

    return (
        <>
            <ImgCrop aspect={200 / 200}>
                <Dragger
                    {...profilePhotoDraggerProps}
                    listType="picture"
                    maxCount={1}
                    multiple={false}
                    showUploadList={{
                        showPreviewIcon: true,
                        showRemoveIcon: true,
                        showDownloadIcon: true,
                    }}
                >
                    <div className="flex flex-col justify-center items-center">
                        <CloudUpload />
                        <Text className="font-medium text-black-full">
                            Arrastre una imagen de perfil o haga click aquí
                        </Text>
                        <Text className="text-xs text-black-medium">
                            Solo una imagen (Máx. 2MB)
                        </Text>
                    </div>
                </Dragger>
            </ImgCrop>
            <Image
                wrapperStyle={{ display: "none" }}
                preview={{
                    visible: previewOpen,
                    onVisibleChange: setPreviewOpen,
                }}
                src={fileList[0]?.url}
            />
        </>
    );
}
