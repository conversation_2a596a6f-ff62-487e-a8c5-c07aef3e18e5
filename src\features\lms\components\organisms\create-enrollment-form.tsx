import { useState } from "react";
import { <PERSON>, Select, Button, Switch, message } from "antd";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { createEnrollment } from "../../services/portals/enrollment";
import { EnrollmentCreate } from "../../types/enrollment";
import { AxiosError } from "axios";

const { Option } = Select;

interface CreateEnrollmentFormProps {
    onSuccess?: () => void;
    onCancel?: () => void;
}

interface FormValues {
    orderItem: string;
    user: string;
    isActive: boolean;
}

interface ErrorResponse {
    message?: string;
}

export default function CreateEnrollmentForm({
    onSuccess,
    onCancel,
}: CreateEnrollmentFormProps) {
    const [form] = Form.useForm<FormValues>();
    const [loading, setLoading] = useState(false);
    const queryClient = useQueryClient();

    const createMutation = useMutation({
        mutationFn: createEnrollment,
        onSuccess: () => {
            message.success("Matrícula creada exitosamente");
            form.resetFields();
            queryClient.invalidateQueries({ queryKey: ["enrollments"] });
            onSuccess?.();
        },
        onError: (error: AxiosError<ErrorResponse>) => {
            message.error(
                error?.response?.data?.message || "Error al crear la matrícula",
            );
        },
        onSettled: () => {
            setLoading(false);
        },
    });

    const handleSubmit = async (values: FormValues) => {
        setLoading(true);
        const payload: EnrollmentCreate = {
            orderItem: values.orderItem,
            user: values.user,
            isActive: values.isActive,
        };
        createMutation.mutate(payload);
    };

    return (
        <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            initialValues={{
                isActive: true,
            }}
        >
            <Form.Item
                name="user"
                label="Usuario"
                rules={[{ required: true, message: "Por favor selecciona un usuario" }]}
            >
                <Select
                    placeholder="Seleccionar usuario"
                    showSearch
                    filterOption={(input, option) =>
                        option?.label
                            ?.toString()
                            .toLowerCase()
                            .includes(input.toLowerCase()) ?? false
                    }
                >
                    <Option value="user-1">John Doe</Option>
                    <Option value="user-2">Jane Smith</Option>
                </Select>
            </Form.Item>

            <Form.Item
                name="orderItem"
                label="Producto"
                rules={[
                    { required: true, message: "Por favor selecciona un producto" },
                ]}
            >
                <Select
                    placeholder="Seleccionar producto"
                    showSearch
                    filterOption={(input, option) =>
                        option?.label
                            ?.toString()
                            .toLowerCase()
                            .includes(input.toLowerCase()) ?? false
                    }
                >
                    <Option value="order-item-1">Course A</Option>
                    <Option value="order-item-2">Course B</Option>
                </Select>
            </Form.Item>

            <Form.Item name="isActive" label="Activo" valuePropName="checked">
                <Switch />
            </Form.Item>

            <div className="flex justify-end gap-2">
                <Button onClick={onCancel}>Cancelar</Button>
                <Button type="primary" htmlType="submit" loading={loading}>
                    Crear Matrícula
                </Button>
            </div>
        </Form>
    );
}
