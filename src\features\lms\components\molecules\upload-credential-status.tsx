import { useQuery } from "@tanstack/react-query";
import { checkTaskStatus } from "@services/portals/shared/celery";
import { TaskStatus } from "@/core/types/celery";
import { Check<PERSON>ircle, XCircle, Loader, AlertTriangle } from "lucide-react";

export default function UploadCredentialStatus({
    certificateExists,
    taskId,
}: {
    certificateExists: boolean;
    taskId: string | undefined;
}) {
    const { data: taskStatus } = useQuery({
        queryKey: ["taskStatus", taskId],
        queryFn: () => checkTaskStatus(taskId!),
        enabled: !!taskId,
        refetchInterval: (query) => {
            if (
                query.state.data?.status === TaskStatus.SUCCESS ||
                query.state.data?.status === TaskStatus.FAILURE
            ) {
                return false;
            }
            return 500;
        },
        refetchIntervalInBackground: true,
        retry: true,
        staleTime: 0,
    });

    const getStatusDisplay = () => {
        if (taskId && taskStatus) {
            switch (taskStatus?.status) {
                case TaskStatus.SUCCESS:
                    return {
                        icon: <CheckCircle className="w-5 h-5 text-green-500" />,
                        text: taskStatus.details?.message || "Completado exitosamente",
                        color: "text-green-700",
                    };
                case TaskStatus.PROGRESS:
                    return {
                        icon: (
                            <Loader className="w-5 h-5 text-yellow-500 animate-spin" />
                        ),
                        text: taskStatus.details?.message || "Procesando...",
                        color: "text-yellow-700",
                    };
                default:
                    return {
                        icon: <AlertTriangle className="w-5 h-5 text-gray-500" />,
                        text: "Estado desconocido",
                        color: "text-gray-700",
                    };
            }
        }
        if (certificateExists) {
            return {
                icon: <CheckCircle className="w-5 h-5 text-green-500" />,
                text: "Existe",
                color: "text-green-700",
            };
        }

        return {
            icon: <XCircle className="w-5 h-5 text-red-500" />,
            text: "Pendiente",
            color: "text-red-700",
        };
    };

    const { icon, text, color } = getStatusDisplay();

    return (
        <div className={`flex items-center gap-2 ${color}`}>
            {icon}
            <span>{text}</span>
        </div>
    );
}
