import { OrderStage, OrderStageLabels } from "@/features/crm/types/order";
import { Select, SelectProps } from "antd";

interface SelectOrderStageProps extends Omit<SelectProps, "options"> {
    value?: string; // Add value prop for controlled component
    onChange?: (value: string) => void; // Add onChange prop
}
export default function SelectOrderStage({
    value,
    onChange,
    ...restProps
}: SelectOrderStageProps) {
    const offeringStagesOptions: SelectProps["options"] = Object.values(OrderStage).map(
        (stage) => ({
            value: stage,
            label: OrderStageLabels[stage],
        }),
    );
    return (
        <>
            <Select
                {...restProps}
                value={value}
                onChange={onChange}
                options={offeringStagesOptions}
            />
        </>
    );
}
