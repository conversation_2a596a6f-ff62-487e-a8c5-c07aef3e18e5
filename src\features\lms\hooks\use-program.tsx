import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
    getPrograms,
    listProgramEnrollments,
    partialUpdateProgram,
    retrieveProgram,
    retrieveProgramTeamChannel,
} from "../services/portals/program";
import {
    PartialUpdateProgramBody,
    ProgramQueryParams,
    RetrieveProgram,
} from "../types/program";
import type { AxiosError } from "axios";
import { useApiError } from "@hooks/use-api-error";
import { App } from "antd";

interface UseProgramProps {
    queryParams?: ProgramQueryParams;
    enabled?: boolean;
}

export const usePrograms = ({ enabled, queryParams }: UseProgramProps = {}) => {
    const { data, isLoading, isFetching, isError } = useQuery({
        queryKey: ["programs", queryParams],
        queryFn: () => getPrograms(queryParams),
        enabled,
    });

    const { count, results: programs } = data || {
        count: 0,
        results: [],
    };

    return {
        isLoading: isLoading || isFetching,
        isError,
        programs,
        count,
    };
};

export const useProgram = (oid: string | undefined) => {
    const { isLoading, isFetching, data, refetch, isError } = useQuery({
        queryKey: ["program", oid],
        queryFn: () => retrieveProgram(oid as string),
        enabled: !!oid,
        refetchOnWindowFocus: false,
    });

    return {
        program: data,
        isLoading: isLoading || isFetching,
        isError,
        refetch,
    };
};

export const useProgramEnrollments = (oid: string | undefined) => {
    const { isLoading, isFetching, data, refetch, isError } = useQuery({
        queryKey: ["program-enrollments", oid],
        queryFn: () => listProgramEnrollments(oid as string),
        enabled: !!oid,
        refetchOnWindowFocus: false,
    });

    return {
        data,
        isLoading: isLoading || isFetching,
        isError,
        refetch,
    };
};

// Retrieves program team channel by program oid
interface UseProgramTeamChannelProps {
    oid: string | undefined;
    enabled?: boolean;
}

export const useProgramTeamChannel = ({ oid, enabled }: UseProgramTeamChannelProps) => {
    const { isLoading, isFetching, data, refetch, isError } = useQuery({
        queryKey: ["program-team-channel", oid],
        queryFn: () => retrieveProgramTeamChannel(oid as string),
        enabled: enabled ?? !!oid,
        refetchOnWindowFocus: false,
        refetchOnMount: false,
    });

    return {
        programTeamChannel: data,
        isLoading: isLoading || isFetching,
        isError,
        refetch,
    };
};

// Mutation hooks
interface UseMutateProgramProps {
    onSuccess?: () => void;
    onError?: (error: AxiosError) => void;
}

export const useUpdateProgram = ({
    onSuccess,
    onError,
}: UseMutateProgramProps = {}) => {
    const { notification } = App.useApp();
    const { handleError } = useApiError({
        title: "Error al actualizar el programa",
    });
    const queryClient = useQueryClient();

    return useMutation<
        Partial<RetrieveProgram>,
        AxiosError,
        { oid: string; data: PartialUpdateProgramBody }
    >({
        mutationFn: ({ oid, data }) => partialUpdateProgram(oid, data),

        onSuccess: (_, variables) => {
            notification.success({
                message: "Programa Actualizado",
                description: "Los cambios se han guardado correctamente",
            });
            queryClient.invalidateQueries({ queryKey: ["program", variables.oid] });
            onSuccess?.();
        },
        onError: (error: AxiosError) => {
            handleError(error);
            onError?.(error);
        },
    });
};
