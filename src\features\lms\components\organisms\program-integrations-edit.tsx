import {
    <PERSON><PERSON>,
    <PERSON>,
    Typography,
    Switch,
    Input,
    Form,
    Row,
    Col,
    Tag,
    Tooltip,
} from "antd";
import { Globe, Save, MessageCircle, InfoIcon } from "lucide-react";

import {
    PartialUpdateProgramBody,
    RetrieveProgram,
} from "@/features/lms/types/program";
import SelectTeamChannel from "../molecules/select-team-channel";
import { useProgramTeamChannel, useUpdateProgram } from "../../hooks/use-program";

const { Title, Text } = Typography;

interface IntegrationsProgramEditProps {
    data: RetrieveProgram;
}

type UpdateIntegrationsFormValues = PartialUpdateProgramBody & {
    extReferenceEnabled: boolean;
    teamChannelEnabled: boolean;
};

export default function IntegrationsProgramEdit({
    data,
}: IntegrationsProgramEditProps) {
    const [form] = Form.useForm<UpdateIntegrationsFormValues>();
    const {
        programTeamChannel,
        isLoading: isLoadingTeamChannel,
        refetch: refetchTeamChannel,
    } = useProgramTeamChannel({
        oid: data.oid,
        enabled: data.teamChannelId !== null,
    });

    const { mutate: updateProgram, isPending: isUpdatingProgram } = useUpdateProgram({
        onSuccess: () => {
            // si se modifica teamChannelId, invialidar query
            if (data.teamChannelId || programTeamChannel) {
                if (data.teamChannelId !== programTeamChannel?.id) {
                    refetchTeamChannel();
                }
            }
        },
    });

    // Establecer valores iniciales del form
    const initialValues: UpdateIntegrationsFormValues = {
        extReferenceEnabled: data.extReference !== null,
        extReference: data.extReference,
        teamChannelEnabled: data.teamChannelId !== null,
        teamChannelId: data.teamChannelId,
    };

    const handleSaveIntegrations = () => {
        const values = form.getFieldsValue();

        if (!values.extReferenceEnabled) {
            values.extReference = null;
        }

        if (!values.teamChannelEnabled) {
            values.teamChannelId = null;
        }

        console.log("Saving integrations:", values);

        updateProgram({
            oid: data.oid,
            data: {
                teamChannelId: values.teamChannelId,
                extReference: values.extReference,
            },
        });
    };

    const IntegrationCard = ({
        title,
        description,
        icon: Icon,
        enabledFieldName,
        children,
        status = "available",
    }: {
        title: string;
        description: string;
        icon: React.ElementType;
        enabledFieldName: string;
        children?: React.ReactNode;
        status?: "available" | "coming-soon" | "beta";
    }) => (
        <Card className="shadow-sm mb-4">
            <div className="flex items-start justify-between mb-4">
                <div className="flex items-start gap-3">
                    <div className="p-2 bg-blue-50 rounded-lg">
                        <Icon size={20} className="text-blue-600" />
                    </div>
                    <div>
                        <div className="flex items-center gap-2">
                            <Title level={5} className="mb-1">
                                {title}
                            </Title>
                            {status === "beta" && <Tag color="orange">Beta</Tag>}
                            {status === "coming-soon" && (
                                <Tag color="gray">Próximamente</Tag>
                            )}
                        </div>
                        <Text type="secondary" className="text-sm">
                            {description}
                        </Text>
                    </div>
                </div>
                <Form.Item name={enabledFieldName} valuePropName="checked" noStyle>
                    <Switch disabled={status === "coming-soon"} />
                </Form.Item>
            </div>
            <Form.Item
                noStyle
                shouldUpdate={(prevValues, currentValues) =>
                    prevValues[enabledFieldName] !== currentValues[enabledFieldName]
                }
            >
                {({ getFieldValue }) => {
                    const isEnabled = getFieldValue(enabledFieldName);
                    return isEnabled && children ? (
                        <div className="pl-11">{children}</div>
                    ) : null;
                }}
            </Form.Item>
        </Card>
    );

    return (
        <Form form={form} initialValues={initialValues} layout="vertical">
            <div className="space-y-6">
                <Card className="shadow-sm">
                    <div className="flex justify-between items-center mb-6">
                        <div>
                            <Title level={4}>Integraciones</Title>
                            <Text type="secondary">
                                Conecta el programa con plataformas externas y
                                herramientas de aprendizaje
                            </Text>
                        </div>
                        <Button
                            type="primary"
                            icon={<Save size={16} />}
                            onClick={handleSaveIntegrations}
                            className="bg-blue-full  hover:bg-green-600"
                            disabled={isUpdatingProgram}
                            loading={isUpdatingProgram}
                        >
                            Guardar Integraciones
                        </Button>
                    </div>

                    <div className="space-y-4">
                        <IntegrationCard
                            title="Classroom"
                            description="Sincroniza estudiantes con Classroom"
                            icon={Globe}
                            enabledFieldName="extReferenceEnabled"
                        >
                            <Row gutter={16}>
                                <Col xs={24} md={12}>
                                    <Form.Item
                                        name="extReference"
                                        label="Código de Clase"
                                    >
                                        <Input placeholder="classroom-id-123" />
                                    </Form.Item>
                                </Col>
                            </Row>
                            <Tooltip
                                title="Cómo obtener el ID de Classroom"
                                className="flex items-center gap-1 text-gray-500 w-fit"
                            >
                                <InfoIcon size={16} />
                                Cómo obtener el código de clase
                            </Tooltip>
                        </IntegrationCard>

                        <IntegrationCard
                            title="WhatsApp"
                            description="Invita automáticamente a los inscritos al grupo de WhatsApp del programa"
                            icon={MessageCircle}
                            enabledFieldName="teamChannelEnabled"
                            status="available"
                        >
                            <Row gutter={16}>
                                <Col xs={24} md={12}>
                                    <Form.Item name="teamChannelId" label="Grupo">
                                        <SelectTeamChannel
                                            value={data.teamChannelId ?? undefined}
                                            defaultTeamChannel={programTeamChannel}
                                            isLoading={isLoadingTeamChannel}
                                            size="large"
                                            className="min-h-24"
                                        />
                                    </Form.Item>
                                </Col>
                            </Row>
                        </IntegrationCard>
                    </div>
                </Card>
            </div>
        </Form>
    );
}
