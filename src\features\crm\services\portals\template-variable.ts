import { PaginatedResponse } from "@myTypes/base";
import { portalsApi } from "@services/portals";
import type {
    ListTemplateVariableParams,
    RetrieveTemplateVariable,
    TemplateVariable,
} from "../../types/template-variable";

const DEFAULT_PAGE_SIZE = 20;
const DEFAULT_PAGE = 1;

export const listTemplateVariable = async (
    params: ListTemplateVariableParams = {
        page: DEFAULT_PAGE,
        pageSize: DEFAULT_PAGE_SIZE,
    },
): Promise<PaginatedResponse<TemplateVariable>> => {
    const response = await portalsApi.get("crm/template-variables", {
        params,
    });
    return response.data;
};

export const retrieveTemplateVariable = async (
    tvid: string,
): Promise<RetrieveTemplateVariable | null> => {
    const response = await portalsApi.get(`crm/template-variables/${tvid}`);
    return response.data;
};
