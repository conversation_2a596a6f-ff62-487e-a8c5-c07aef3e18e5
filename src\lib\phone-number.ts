import { PhoneNumber } from "antd-phone-input";
import { parsePhoneNumber } from "react-phone-hooks";

export const phoneNumberToString = (phoneNumber: PhoneNumber | string): string => {
    if (typeof phoneNumber === "string") {
        return phoneNumber;
    }

    const { countryCode, areaCode, phoneNumber: phoneNumberString } = phoneNumber;
    // If all the values are empty, raise an error
    if (!countryCode && !areaCode && !phoneNumberString) {
        throw new Error("Invalid phone number");
    }
    const finalPhoneNumber = `+${countryCode || ""}${areaCode || ""}${phoneNumberString || ""}`;

    try {
        parsePhoneNumber(finalPhoneNumber);
    } catch (error) {
        throw new Error("Invalid phone number");
    }

    return finalPhoneNumber;
};
