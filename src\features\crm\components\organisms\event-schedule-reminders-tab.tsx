import { useSearchParams } from "react-router-dom";
import {
    Badge,
    Input,
    Pagination,
    Typography,
    Button,
    Tooltip,
    Space,
    Row,
    Col,
} from "antd";
import { Search, RefreshCw, RotateCcw } from "lucide-react";
import type { RetrieveEventSchedule } from "../../types/event-schedule";
import {
    useEventReminders,
    useEventReminderMetrics,
    useBulkRetryEventReminders,
} from "../../hooks/use-event-reminder";
import {
    DEFAULT_PAGE,
    DEFAULT_PAGE_SIZE,
    ListEventRemindersQueryParams,
} from "../../types/event-reminder";
import EventReminderTable from "./event-reminder-table";
import EventReminderStats from "../molecules/event-reminder-stats";
import { useState } from "react";

const { Text } = Typography;

type FilterStatus = "all" | "sent" | "pending" | "failed";

export default function EventScheduleRemindersTab({
    eventSchedule,
}: {
    eventSchedule: RetrieveEventSchedule;
}) {
    const [searchParams, setSearchParams] = useSearchParams();
    const [filterStatus, setFilterStatus] = useState<FilterStatus>("all");

    // Get params from URL
    const page = Number(searchParams.get("page")) || DEFAULT_PAGE;
    const pageSize = Number(searchParams.get("pageSize")) || DEFAULT_PAGE_SIZE;
    const user = searchParams.get("user") || undefined;

    // Build query params based on filter status
    const getQueryParams = (): ListEventRemindersQueryParams => {
        const baseParams: ListEventRemindersQueryParams = {
            page,
            pageSize,
            user,
            eventSchedule: eventSchedule.esid,
        };

        switch (filterStatus) {
            case "sent":
                return { ...baseParams, hasSentInvitations: true };
            case "pending":
                return { ...baseParams, hasPendingInvitations: true };
            case "failed":
                return { ...baseParams, hasFailedInvitations: true };
            default:
                return baseParams;
        }
    };

    const queryParams = getQueryParams();

    // Fetch reminders and metrics
    const {
        reminders,
        count,
        isLoading,
        isError,
        refetch: refetchReminders,
    } = useEventReminders({
        queryParams,
    });

    const {
        metrics,
        isLoading: metricsLoading,
        refetch: refetchMetrics,
    } = useEventReminderMetrics({
        queryParams: { eventSchedule: eventSchedule.esid },
    });

    const bulkRetryMutation = useBulkRetryEventReminders();

    // Handle search
    const handleSearch = (value: string) => {
        setSearchParams((prev) => {
            if (value.trim()) {
                prev.set("user", value.trim());
            } else {
                prev.delete("user");
            }
            prev.set("page", "1"); // Reset to first page
            return prev;
        });
    };

    // Handle pagination
    const handlePaginationChange = (newPage: number, newPageSize: number) => {
        setSearchParams((prev) => {
            prev.set("page", newPage.toString());
            prev.set("pageSize", newPageSize.toString());
            return prev;
        });
    };

    // Handle filter status change
    const handleFilterStatusChange = (status: FilterStatus) => {
        setFilterStatus(status);
        setSearchParams((prev) => {
            prev.set("page", "1"); // Reset to first page
            return prev;
        });
    };

    // Handle bulk retry
    const handleBulkRetry = () => {
        bulkRetryMutation.mutate();
    };

    // Handle refresh
    const handleRefresh = () => {
        refetchReminders();
        refetchMetrics();
    };

    if (isError) {
        return (
            <div className="flex items-center justify-center h-64">
                <Text type="danger">
                    Error al cargar los recordatorios. Por favor, intenta nuevamente.
                </Text>
            </div>
        );
    }

    return (
        <div className="space-y-4">
            {/* Stats and Invitation Form Section */}
            <Row gutter={[16, 16]}>
                <Col xs={24}>
                    {metrics && !metricsLoading && (
                        <EventReminderStats metrics={metrics} />
                    )}
                </Col>
            </Row>

            {/* Filter Tabs */}
            <div className="bg-white p-4 rounded-lg shadow-sm">
                <div className="space-x-2 [&>button]:rounded-full [&>button]:px-4 [&>button]:py-2">
                    <Button
                        onClick={() => handleFilterStatusChange("all")}
                        type={filterStatus === "all" ? "primary" : "default"}
                    >
                        Todos
                    </Button>
                    <Button
                        onClick={() => handleFilterStatusChange("sent")}
                        type={filterStatus === "sent" ? "primary" : "default"}
                    >
                        Enviados
                    </Button>
                    <Button
                        onClick={() => handleFilterStatusChange("pending")}
                        type={filterStatus === "pending" ? "primary" : "default"}
                    >
                        Pendientes
                    </Button>
                    <Button
                        onClick={() => handleFilterStatusChange("failed")}
                        type={filterStatus === "failed" ? "primary" : "default"}
                    >
                        Fallidos
                    </Button>
                </div>
            </div>

            {/* Controls Section */}
            <div className="bg-white p-4 rounded-lg shadow-sm">
                <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
                    {/* Title and Count */}
                    <div className="flex items-center gap-2">
                        <Text className="text-black-medium text-xl font-semibold">
                            Recordatorios
                        </Text>
                        <Badge count={count} color="blue" size="default" />
                    </div>

                    {/* Search and Actions */}
                    <div className="flex flex-col sm:flex-row gap-3 w-full lg:w-auto">
                        <Input.Search
                            placeholder="Buscar por nombre, email..."
                            onSearch={handleSearch}
                            defaultValue={user}
                            enterButton={<Search size={16} />}
                            allowClear
                            className="w-full sm:w-64"
                            size="large"
                        />

                        <Space>
                            <Tooltip title="Refrescar datos">
                                <Button
                                    icon={<RefreshCw size={16} />}
                                    onClick={handleRefresh}
                                    loading={isLoading}
                                >
                                    Refrescar
                                </Button>
                            </Tooltip>

                            <Tooltip title="Reintentar todas las invitaciones fallidas">
                                <Button
                                    icon={<RotateCcw size={16} />}
                                    onClick={handleBulkRetry}
                                    loading={bulkRetryMutation.isPending}
                                    disabled={!metrics?.totalFailed}
                                    type="primary"
                                >
                                    Reintentar Fallidos
                                </Button>
                            </Tooltip>
                        </Space>
                    </div>
                </div>
            </div>

            {/* Table Section */}
            <div className="bg-white rounded-lg shadow-sm">
                <EventReminderTable
                    eventSchedule={eventSchedule}
                    reminders={reminders}
                    loading={isLoading}
                />
            </div>

            {/* Pagination Section */}
            {count > 0 && (
                <div className="flex justify-between items-center p-4 bg-white rounded-lg shadow-sm">
                    <Text type="secondary">
                        {reminders.length} de {count} recordatorios
                    </Text>
                    <Pagination
                        current={page}
                        pageSize={pageSize}
                        total={count}
                        onChange={handlePaginationChange}
                        showSizeChanger
                        showQuickJumper
                        showTotal={(total, range) =>
                            `${range[0]}-${range[1]} de ${total} recordatorios`
                        }
                    />
                </div>
            )}
        </div>
    );
}
