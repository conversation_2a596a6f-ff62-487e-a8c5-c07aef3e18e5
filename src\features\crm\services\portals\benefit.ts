import { PaginatedResponse } from "@myTypes/base";
import { portalsApi } from "@services/portals";
import { Benefit, CreateBenefit } from "../../types/benefit";

export const getBenefits = async (): Promise<PaginatedResponse<Benefit>> => {
    const response = await portalsApi.get("crm/benefits", {
        params: {
            page: 1,
            pageSize: 100,
        },
    });
    return response.data;
};

export const createBenefit = async (benefit: CreateBenefit): Promise<Benefit> => {
    const response = await portalsApi.post("crm/benefits", benefit);
    return response.data;
};
