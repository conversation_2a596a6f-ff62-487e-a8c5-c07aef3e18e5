import { portalsApi } from "@services/portals";
import {
    EventScheduleEnrollmentsResponse,
    ListEventScheduleEnrollmentsQueryParams,
} from "../../types/event-schedule-enrollment";

export const listEventScheduleEnrollments = async (
    esid: string,
    queryParams: ListEventScheduleEnrollmentsQueryParams = {},
): Promise<EventScheduleEnrollmentsResponse> => {
    const res = await portalsApi.get(`crm/event-schedules/${esid}/enrollments`, {
        params: queryParams,
    });
    return res.data;
};
