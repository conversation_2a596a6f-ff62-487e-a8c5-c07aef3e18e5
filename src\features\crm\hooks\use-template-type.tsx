import { useQuery } from "@tanstack/react-query";
import { listTemplateType } from "../services/portals/template-type";
import { ListTemplateTypeQuery } from "../types/template-type";

export type UseTemplateTypeQuery = ListTemplateTypeQuery;

type UseTemplateTypesProps = {
    page?: number;
    pageSize?: number;
    query?: UseTemplateTypeQuery;
};

export const useTemplateTypes = ({ query, ...rest }: UseTemplateTypesProps = {}) => {
    const { data, isLoading, isError, isFetching, refetch } = useQuery({
        queryKey: ["template-types", query, rest],
        queryFn: () =>
            listTemplateType({
                ...query,
                ...rest,
            }),
        refetchOnWindowFocus: false,
    });

    const { count: COUNT, results: templateTypes } = data || {
        count: 0,
        results: [],
    };

    return {
        refetch,
        isLoading: isLoading || isFetching,
        isError,
        templateTypes,
        COUNT,
    };
};
