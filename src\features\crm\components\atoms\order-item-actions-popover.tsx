import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>confirm } from "antd";
import {
    MoreHorizontal,
    RefreshCw,
    AlertCircle,
    CheckCircle,
    Loader,
    UserPlus,
    ExternalLink,
    Trash2,
} from "lucide-react";
import { useState } from "react";
import type { OrderItem, Order } from "../../types/order";
import { OrderStage } from "../../types/order";
import {
    useSendClassroomInvitation,
    useCreateEnrollment,
} from "../../hooks/use-order-item";
import type { AxiosError } from "axios";
import { useApiError } from "@hooks/use-api-error";
import { Link } from "react-router-dom";

type OrderItemActionsPopoverProps = {
    orderItem: OrderItem;
    order: Pick<Order, "oid" | "stage">;
    onDeleteOrderItem?: (orderItem: OrderItem) => void;
};

export default function OrderItemActionsPopover({
    orderItem,
    order,
    onDeleteOrderItem,
}: OrderItemActionsPopoverProps) {
    const { message } = App.useApp();
    const [open, setOpen] = useState(false);

    const { handleError } = useApiError({
        title: "Error al reenviar la invitación",
        genericMessage: "No se pudo reenviar la invitación al aula virtual",
    });

    const { mutate: sendInvitation, isPending: isSending } = useSendClassroomInvitation(
        {
            onSendInvitationSuccess: () => {
                message.success("Invitación reenviada correctamente");
                setOpen(false);
            },
            onSendInvitationError: (error: AxiosError) => {
                handleError(error);
            },
        },
    );

    const { mutate: createEnrollment, isPending: isCreatingEnrollment } =
        useCreateEnrollment({
            onCreateEnrollmentSuccess: () => {
                message.success("Matrícula creada exitosamente");
                setOpen(false);
            },
            onCreateEnrollmentError: () => {
                // Error is handled by the hook itself
            },
        });

    const handleResendInvitation = () => {
        if (orderItem.offering.oid) {
            sendInvitation({
                orderItemId: orderItem.oiid,
                oid: order.oid,
            });
        }
    };

    const handleCreateEnrollment = () => {
        createEnrollment({
            orderItemId: orderItem.oiid,
            oid: order.oid,
        });
    };

    const handleDeleteOrderItem = () => {
        onDeleteOrderItem?.(orderItem);
        setOpen(false);
    };

    const getInvitationStatusElement = () => {
        if (order.stage !== OrderStage.SOLD) return null;

        const status = orderItem.extInvitationStatus;

        switch (status) {
            case "pending":
                return (
                    <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-yellow-500 animate-pulse" />
                        <span className="text-sm text-gray-600">
                            Invitación pendiente
                        </span>
                    </div>
                );
            case "sending":
                return (
                    <div className="flex items-center gap-2">
                        <Loader size={12} className="text-blue-500 animate-spin" />
                        <span className="text-sm text-gray-600">
                            Enviando invitación...
                        </span>
                    </div>
                );
            case "sent":
                return (
                    <div className="flex items-center gap-2">
                        <CheckCircle size={12} className="text-green-500" />
                        <span className="text-sm text-green-600">
                            Invitación enviada
                        </span>
                    </div>
                );
            default: // error
                return (
                    <div className="space-y-2">
                        <div className="flex items-center gap-2">
                            <AlertCircle size={12} className="text-red-500" />
                            <span className="text-sm text-red-600">
                                Error al enviar invitación
                            </span>
                        </div>
                        <Button
                            size="small"
                            type="primary"
                            icon={<RefreshCw size={12} />}
                            loading={isSending}
                            onClick={handleResendInvitation}
                            block
                        >
                            Reintentar invitación
                        </Button>
                    </div>
                );
        }
    };

    const getEnrollmentElement = () => {
        if (order.stage !== OrderStage.SOLD) return null;

        if (orderItem.hasEnrollment && orderItem.enrollment?.eid) {
            return (
                <div className="space-y-2">
                    <div className="flex items-center gap-2">
                        <CheckCircle size={12} className="text-green-500" />
                        <span className="text-sm text-green-600">Matrícula creada</span>
                    </div>
                    <Link
                        to={`/lms/enrollments/${orderItem.enrollment.eid}`}
                        onClick={() => setOpen(false)}
                    >
                        <Button
                            size="small"
                            type="default"
                            icon={<ExternalLink size={12} />}
                            block
                        >
                            Ver matrícula en LMS
                        </Button>
                    </Link>
                </div>
            );
        } else {
            return (
                <div className="space-y-2">
                    <div className="flex items-center gap-2">
                        <AlertCircle size={12} className="text-orange-500" />
                        <span className="text-sm text-gray-600">Sin matrícula</span>
                    </div>
                    <Button
                        size="small"
                        type="primary"
                        icon={<UserPlus size={12} />}
                        loading={isCreatingEnrollment}
                        onClick={handleCreateEnrollment}
                        block
                    >
                        Crear matrícula
                    </Button>
                </div>
            );
        }
    };

    const content = (
        <div className="w-64 space-y-3">
            {/* Acciones relacionadas con el status de SOLD */}
            {order.stage === OrderStage.SOLD && (
                <>
                    {getInvitationStatusElement()}

                    {getInvitationStatusElement() && getEnrollmentElement() && (
                        <Divider className="my-2" />
                    )}

                    {getEnrollmentElement()}

                    <Divider className="my-2" />
                </>
            )}

            {/* Acción de eliminar - siempre disponible */}
            <div className="space-y-2">
                <Popconfirm
                    title="Quitar producto de la orden"
                    description="¿Estas seguro de quitar este producto?"
                    okText="Si"
                    cancelText="No"
                    onConfirm={handleDeleteOrderItem}
                    placement="left"
                >
                    <Button
                        size="small"
                        type="primary"
                        danger
                        icon={<Trash2 size={12} />}
                        block
                    >
                        Eliminar producto
                    </Button>
                </Popconfirm>
            </div>
        </div>
    );

    return (
        <Popover
            content={content}
            trigger="click"
            open={open}
            onOpenChange={setOpen}
            placement="leftTop"
            title={
                <div className="text-sm font-medium text-gray-700">
                    Acciones del producto
                </div>
            }
        >
            <Button
                type="text"
                icon={<MoreHorizontal size={16} />}
                size="small"
                className="hover:bg-gray-100"
            />
        </Popover>
    );
}
