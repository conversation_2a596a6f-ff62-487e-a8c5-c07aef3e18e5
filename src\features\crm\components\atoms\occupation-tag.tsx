import { Tooltip, Tag } from "antd";
import { Briefcase, GraduationCap, User } from "lucide-react";
import { ContactOcupation, ContactOcupationLabel } from "@/features/crm/types/contact";

type OccupationTagProps = {
    occupation?: string;
    showLabel?: boolean;
};

const OccupationTag = ({ occupation, showLabel = true }: OccupationTagProps) => {
    if (!occupation) return null;

    const getOccupationInfo = () => {
        switch (occupation) {
            case ContactOcupation.STUDENT:
                return {
                    color: "blue",
                    icon: <GraduationCap size={14} className="mr-1" />,
                    tooltipTitle: ContactOcupationLabel[ContactOcupation.STUDENT],
                };
            case ContactOcupation.EMPLOYEE:
                return {
                    color: "green",
                    icon: <Briefcase size={14} className="mr-1" />,
                    tooltipTitle: ContactOcupationLabel[ContactOcupation.EMPLOYEE],
                };
            case ContactOcupation.INDEPENDENT:
                return {
                    color: "purple",
                    icon: <User size={14} className="mr-1" />,
                    tooltipTitle: ContactOcupationLabel[ContactOcupation.INDEPENDENT],
                };
            default:
                return {
                    color: "default",
                    icon: <User size={14} className="mr-1" />,
                    tooltipTitle: "Sin ocupación",
                };
        }
    };

    const { color, icon, tooltipTitle } = getOccupationInfo();

    return (
        <Tooltip title={tooltipTitle}>
            <Tag color={color} className="flex items-center px-2 py-0.5 rounded-full">
                {icon}
                {showLabel && <span>{tooltipTitle}</span>}
            </Tag>
        </Tooltip>
    );
};

export default OccupationTag;
