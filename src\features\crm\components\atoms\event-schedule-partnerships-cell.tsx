import { EventSchedulePartnership } from "../../types/event-schedule";
import { Tooltip, Tag } from "antd";
import { Building2 } from "lucide-react";

export default function EventSchedulePartnershipsCell({
    partnerships,
}: {
    partnerships: EventSchedulePartnership[];
}) {
    return (
        <div className="flex flex-wrap gap-1">
            {partnerships.map((partnership) => (
                <Tooltip
                    key={partnership.pid}
                    title={partnership.institution}
                    placement="top"
                >
                    <Tag className="flex items-center gap-1 py-1 px-2" color="blue">
                        <Building2 size={14} />
                        <span>{partnership.name}</span>
                    </Tag>
                </Tooltip>
            ))}
        </div>
    );
}
