import CrmLayout from "@/features/crm/layout";
import WelcomeBar from "@components/shared/molecules/WelcomeBar";
import { Tabs, TabsProps } from "antd";
import ContactsDashboardTab from "./components/organisms/contacts-tab";
import SalesDashboardTab from "./components/organisms/sales-tab";
import PaymentsDashboardTab from "./components/organisms/payments-tab";
import { useSearchParams } from "react-router-dom";
import type { PermissionKey } from "@/core/config/permissions-map";
import { useMemo } from "react";
import { useHasPermissions } from "@hooks/use-permission";
import EventsDashboardTab from "./components/organisms/events-tab";

// Configuración de tabs con sus permisos requeridos
const TAB_CONFIG = [
    {
        key: "contacts",
        label: "Contactos",
        children: <ContactsDashboardTab />,
        requiredPermission: "crm.dashboard.contacts" as PermissionKey,
    },
    {
        key: "orders",
        label: "Órdenes",
        children: <SalesDashboardTab />,
        requiredPermission: "crm.dashboard.sales" as PermissionKey,
    },
    {
        key: "payments",
        label: "Pagos",
        children: <PaymentsDashboardTab />,
        requiredPermission: "crm.dashboard.payments" as PermissionKey,
    },
    {
        key: "events",
        label: "Eventos",
        children: <EventsDashboardTab />,
        requiredPermission: "crm.dashboard.events" as PermissionKey,
    },
];

export default function CrmDashboardPage() {
    const [searchParams, setSearchParams] = useSearchParams();

    const permissions = useHasPermissions(
        TAB_CONFIG.map((tab) => tab.requiredPermission),
        "view",
    );

    // Filtrar tabs según permisos del usuario usando el hook
    const tabItems: TabsProps["items"] = useMemo(() => {
        return TAB_CONFIG.filter((tab) => permissions[tab.requiredPermission]).map(
            ({ key, label, children }) => ({ key, label, children }),
        );
    }, [permissions]);

    // Determinar el tab activo, usando el primero disponible si el seleccionado no tiene permisos
    const tabKey = useMemo(() => {
        const requestedTab = searchParams.get("tab") || "contacts";
        const hasAccessToRequestedTab = tabItems?.some(
            (tab) => tab?.key === requestedTab,
        );

        return hasAccessToRequestedTab
            ? requestedTab
            : tabItems?.[0]?.key || "contacts";
    }, [searchParams, tabItems]);

    const handleSetCurrentTab = (key: string) => {
        setSearchParams({ tab: key });
    };

    return (
        <CrmLayout>
            <div className="max-w-screen-2xl w-full">
                <WelcomeBar helperText="Bienvenido a reportería de CRM" />
                {tabItems && tabItems.length > 0 ? (
                    <Tabs
                        items={tabItems}
                        activeKey={tabKey}
                        onChange={handleSetCurrentTab}
                    />
                ) : null}
            </div>
        </CrmLayout>
    );
}
