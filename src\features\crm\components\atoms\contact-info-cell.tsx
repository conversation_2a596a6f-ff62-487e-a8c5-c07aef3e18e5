import { <PERSON><PERSON>, Tooltip } from "antd";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, User } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import IsStaffTag from "./is-staff-tag";
import ContactMethods from "./contact-methods";
import { ContactListItem } from "@/features/crm/types/contact";

type ContactInfoCellProps = {
    contact: ContactListItem;
};

const ContactInfoCell = ({ contact }: ContactInfoCellProps) => {
    const {
        uid,
        firstName,
        lastName,
        fullName,
        email,
        phoneNumber,
        profilePhoto,
        isStaff,
        isActive,
    } = contact;

    return (
        <div className="flex items-center gap-3 py-1">
            {/* Avatar with user photo or placeholder */}
            <Avatar
                size={40}
                src={profilePhoto?.url}
                icon={!profilePhoto && <User size={16} />}
                className="flex-shrink-0"
            />

            <div className="flex flex-col">
                {/* Name row with badges */}
                <div className="flex items-center gap-1 mb-1">
                    <Link
                        to={`/crm/contacts/${uid}`}
                        className="font-medium text-blue-full hover:underline"
                    >
                        {fullName ||
                            `${firstName || ""} ${lastName || ""}` ||
                            "Sin nombre"}
                    </Link>

                    {isStaff && <IsStaffTag />}

                    {isActive && (
                        <Tooltip title="Contacto que ya cuenta con cuenta en la plataforma">
                            <BadgeCheck size={16} className="text-green-500" />
                        </Tooltip>
                    )}
                </div>

                {/* Contact info using the enhanced ContactMethods component */}
                <ContactMethods
                    phoneNumbers={phoneNumber}
                    emails={email}
                    showLabels={false}
                    maxVisible={1}
                />
            </div>
        </div>
    );
};

export default ContactInfoCell;
