import { useDeleteContact } from "@hooks/crm/useDeleteContact";
import { ContactListItem } from "@/features/crm/types/contact";
import { Card, Typography, Avatar, Tooltip, Space } from "antd";
import { Phone, Mail, User, Edit, Trash2, MessageCircle } from "lucide-react";
import { useNavigate } from "react-router-dom";

const { Text, Title } = Typography;

type ContactCardProps = {
    info: ContactListItem;
};

const ICON_SIZE = 14;

export default function ContactCard({ info }: ContactCardProps) {
    const navigate = useNavigate();
    const handleCall = () => {
        if (info.phoneNumber) {
            window.location.href = `tel:${info.phoneNumber}`;
        }
    };

    const handleWhatsApp = () => {
        if (info.phoneNumber) {
            const formattedNumber = info.phoneNumber?.replace(/\D/g, "");
            window.open(`https://wa.me/${formattedNumber}`, "_blank");
        }
    };

    const { mutate: deleteMutate } = useDeleteContact();

    const handleEdit = () => {
        navigate(`/crm/contacts/${info.uid}`);
    };

    const handleDelete = () => {
        deleteMutate(info.uid);
    };

    return (
        <Card
            className="mb-2"
            style={{ borderRadius: "8px" }}
            styles={{
                body: {
                    padding: "12px",
                },
            }}
            actions={[
                <div className="flex justify-center" key="call">
                    <Tooltip title="Llamar">
                        <Phone
                            size={ICON_SIZE}
                            color="#52c41a"
                            onClick={handleCall}
                            className="cursor-pointer"
                        />
                    </Tooltip>
                </div>,
                <div className="flex justify-center" key="whatsapp">
                    <Tooltip title="WhatsApp">
                        <MessageCircle
                            size={ICON_SIZE}
                            color="#25D366"
                            onClick={handleWhatsApp}
                            className="cursor-pointer"
                        />
                    </Tooltip>
                </div>,
                <div className="flex justify-center" key="edit">
                    <Tooltip title="Editar">
                        <Edit
                            size={ICON_SIZE}
                            color="#1890ff"
                            onClick={handleEdit}
                            className="cursor-pointer"
                        />
                    </Tooltip>
                </div>,
                <div className="flex justify-center" key="delete">
                    <Tooltip title="Eliminar">
                        <Trash2
                            size={ICON_SIZE}
                            color="#ff4d4f"
                            onClick={handleDelete}
                            className="cursor-pointer"
                        />
                    </Tooltip>
                </div>,
            ]}
        >
            <div className="flex">
                <Avatar
                    size={48}
                    src={info.profilePhoto?.url}
                    icon={!info.profilePhoto && <User size={18} />}
                />
                <div className="ml-3 flex-1">
                    <div className="flex justify-between items-start">
                        <Title level={5} style={{ margin: 0, fontSize: "14px" }}>
                            {info.fullName ? info.fullName : "Sin nombre"}
                        </Title>
                    </div>

                    <Space
                        direction="vertical"
                        size={2}
                        className="mt-1"
                        style={{ width: "100%" }}
                    >
                        <div className="flex items-center">
                            <Phone size={ICON_SIZE} className="mr-1" color="#8C8C8C" />
                            <Text type="secondary" style={{ fontSize: "12px" }}>
                                {info.phoneNumber || "Sin número"}
                            </Text>
                        </div>

                        <div className="flex items-center">
                            <Mail size={ICON_SIZE} className="mr-1" color="#8C8C8C" />
                            <Text type="secondary" style={{ fontSize: "12px" }}>
                                {info.email || "Sin email"}
                            </Text>
                        </div>
                    </Space>
                </div>
            </div>
        </Card>
    );
}
