export type DashboardPaymentQueryParams = Partial<{
    dateAfter: string;
    dateBefore: string;
    typeDate: "created_at" | "payment_date" | "scheduled_payment_date"; // API expects snake_case
    status: "all" | "paid" | "pending";
    currency: "pen" | "usd"; // Only specific currencies, no "all" option in API
    paymentMethod: string;
    offerings: string;
    forceRefresh: boolean;
}>;

export type DashboardPaymentAmountSummary = {
    pen: number;
    usd: number;
    total: number;
};

export type DashboardPaymentRate = {
    percentage: number;
    successful: number;
    total: number;
};

export type DashboardPaymentConversionRate = {
    percentage: number;
    paid: number;
    total: number;
};

export type DashboardPaymentRecoveryRate = {
    percentage: number;
    latePaid: number;
    totalScheduled: number;
};

export type DashboardPaymentSummaryData = {
    totalRegisteredAmount: DashboardPaymentAmountSummary;
    totalDebtPaidAmount: DashboardPaymentAmountSummary;
    totalPendingDebtAmount: DashboardPaymentAmountSummary;
    totalPaidFirstPaymentAmount: DashboardPaymentAmountSummary;
    totalPendingFirstPaymentAmount: DashboardPaymentAmountSummary;
    debtCollectionRate: DashboardPaymentRate;
    averagePaymentDays: number;
    paymentConversionRate: DashboardPaymentConversionRate;
    recoveryRate: DashboardPaymentRecoveryRate;
    currencyExchangeRate: number;
};

// Types for historical payments endpoint
export type PeriodFilter = "daily" | "weekly" | "monthly";

export interface DashboardPaymentHistoricalQueryParams {
    status?: "all" | "paid" | "pending";
    currency?: "pen" | "usd";
    paymentMethod?: string;
    offerings?: string;
    period: PeriodFilter;
}

export type DashboardPaymentHistoricalPeriodData = {
    period: string;
    shortPeriod: string;
    startDate: string;
    endDate: string;
    discrete: DashboardPaymentAmountSummary;
    cumulative: DashboardPaymentAmountSummary;
};

export type DashboardPaymentHistoricalData = {
    periodType: PeriodFilter;
    totalPeriods: number;
    currencyExchangeRate: number;
    data: DashboardPaymentHistoricalPeriodData[];
    filtersApplied: {
        status: string;
        currency: string;
        paymentMethod: string;
        offerings: string;
        period: PeriodFilter;
    };
};

// Types for currency distribution endpoint
export type DashboardPaymentCurrencyDistributionItem = {
    currency: {
        code: string;
        label: string;
    };
    count: number;
    percentage: number;
};

export type DashboardPaymentCurrencyDistributionData = {
    totalPayments: number;
    data: DashboardPaymentCurrencyDistributionItem[];
    filtersApplied: {
        dateAfter: string;
        dateBefore: string;
        typeDate: string;
        status: string;
        currency: string;
        paymentMethod: string;
        offerings: string;
    };
};

// Types for historical payment methods endpoint
export type DashboardPaymentMethodHistoricalItem = {
    paymentMethod: {
        pmid: string;
        name: string;
    };
    discrete: DashboardPaymentAmountSummary;
    cumulative: DashboardPaymentAmountSummary;
};

export type DashboardPaymentMethodHistoricalPeriodData = {
    period: string;
    shortPeriod: string;
    startDate: string;
    endDate: string;
    paymentMethods: DashboardPaymentMethodHistoricalItem[];
};

export type DashboardPaymentMethodHistoricalData = {
    periodType: PeriodFilter;
    totalPeriods: number;
    currencyExchangeRate: number;
    data: DashboardPaymentMethodHistoricalPeriodData[];
    filtersApplied: {
        status: string;
        currency: string;
        paymentMethod: string;
        offerings: string;
        period: PeriodFilter;
    };
};

// Types for payment methods distribution endpoint
export type DashboardPaymentMethodDistributionItem = {
    paymentMethod: {
        pmid: string | null;
        name: string;
    };
    count: number;
    percentage: number;
};

export type DashboardPaymentMethodDistributionData = {
    totalPayments: number;
    data: DashboardPaymentMethodDistributionItem[];
    filtersApplied: {
        dateAfter: string;
        dateBefore: string;
        typeDate: string;
        status: string;
        currency: string;
        paymentMethod: string;
        offerings: string;
    };
};

// Types for last payments endpoint
export type DashboardLastPaymentItem = {
    key: string;
    pid: string;
    order: {
        oid: string;
        stage: string;
        owner: {
            uid: string;
            fullName: string;
            email: string;
            phoneNumber: string;
        };
    };
    paymentMethod: {
        pmid: string;
        name: string;
    } | null;
    isPaid: boolean;
    amount: string;
    currency: "pen" | "usd";
    isFirstPayment: boolean;
    paymentDate: string;
    scheduledPaymentDate: string | null;
    isLost: boolean;
    createdAt: string;
    updatedAt: string;
};

export type DashboardLastPaymentsData = {
    totalReturned: number;
    data: DashboardLastPaymentItem[];
    filtersApplied: {
        dateAfter: string;
        dateBefore: string;
        typeDate: string;
        status: string;
        currency: string;
        paymentMethod: string;
        offerings: string;
    };
};
