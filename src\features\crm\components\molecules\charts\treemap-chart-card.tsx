import { Card, Empty } from "antd";
import { CardProps } from "antd/es/card";
import { ResponsiveContainer, Treemap, Tooltip } from "recharts";

interface TreeMapCardProps extends CardProps {
    data: Array<Record<string, unknown>>;
    valueKey?: string;
    nameKey?: string;
    colors: string[];
    icon?: React.ReactNode;
}

interface CustomTooltipProps {
    active?: boolean;
    payload?: Array<{
        payload: {
            name: string;
            originalValue: number;
        };
    }>;
}

const CustomTooltip = ({ active, payload }: CustomTooltipProps) => {
    if (active && payload && payload.length) {
        const data = payload[0].payload;
        return (
            <div className="bg-white-full p-2 border border-gray-200 shadow rounded">
                <div className="font-medium">{data.name}</div>
                <div>{data.originalValue?.toLocaleString()}</div>{" "}
            </div>
        );
    }
    return null;
};

interface CustomTreemapContent {
    x?: number;
    y?: number;
    width?: number;
    height?: number;
    name?: string;
    value?: number;
    originalValue?: number;
    color?: string;
}

const CustomTreemapContent = (props: CustomTreemapContent) => {
    const {
        x = 0,
        y = 0,
        width = 0,
        height = 0,
        name = "",
        originalValue = 0, // Usar originalValue en lugar de value
        color = "",
    } = props;

    return (
        <g>
            <rect
                x={x}
                y={y}
                width={width}
                height={height}
                style={{ fill: color, stroke: "#fff" }}
            />
            {width > 60 && height > 30 && (
                <>
                    <text
                        x={x + width / 2}
                        y={y + height / 2 - 6}
                        textAnchor="middle"
                        fill="#fff"
                        fontWeight={600}
                        fontSize={14}
                    >
                        {name}
                    </text>
                    <text
                        x={x + width / 2}
                        y={y + height / 2 + 12}
                        textAnchor="middle"
                        fill="#fff"
                        fontSize={12}
                    >
                        {originalValue?.toLocaleString()}{" "}
                        {/* Mostrar valor original formateado */}
                    </text>
                </>
            )}
        </g>
    );
};

const TreeMapCard: React.FC<TreeMapCardProps> = ({
    data,
    valueKey = "value",
    nameKey = "name",
    colors,
    icon,
    ...cardProps
}) => {
    // Solo normalizamos para el tamaño visual, pero preservamos los valores originales
    const processedData = data.map((item, idx) => {
        const rawValue = Number(item[valueKey] ?? 0);
        const normalizedValue = rawValue > 0 ? Math.log10(rawValue + 1) * 100 : 0;

        return {
            name: item[nameKey],
            value: normalizedValue, // Para el cálculo del tamaño del rectángulo
            originalValue: rawValue, // Para mostrar al usuario
            color: colors[idx % colors.length],
        };
    });

    return (
        <Card
            {...cardProps}
            title={
                <div className="flex items-center gap-2 w-full">
                    {icon}
                    <span>{cardProps.title || ""}</span>
                </div>
            }
            className="shadow-md h-full"
            styles={{ body: { height: 350, padding: 8 } }}
        >
            {data.length ? (
                <ResponsiveContainer width="100%" height="100%">
                    <Treemap
                        data={processedData}
                        dataKey="value" // Usa el valor normalizado para el tamaño
                        nameKey={nameKey}
                        stroke="#fff"
                        content={<CustomTreemapContent />}
                        isAnimationActive={false}
                    >
                        <Tooltip content={<CustomTooltip />} />
                    </Treemap>
                </ResponsiveContainer>
            ) : (
                <div className="h-full flex items-center justify-center">
                    <Empty />
                </div>
            )}
        </Card>
    );
};

export default TreeMapCard;
