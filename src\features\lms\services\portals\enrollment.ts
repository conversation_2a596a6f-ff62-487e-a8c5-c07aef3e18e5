import { portalsApi } from "@services/portals";
import {
    EnrollmentRetrieve,
    EnrollmentQueryParams,
    EnrollmentCreate,
    EnrollmentUpdate,
    EnrollmentsResponse,
} from "../../types/enrollment";

export const getEnrollments = async (
    params: EnrollmentQueryParams = {},
): Promise<EnrollmentsResponse> => {
    const response = await portalsApi.get("lms/enrollments", {
        params,
    });
    return response.data;
};

export const retrieveEnrollment = async (eid: string): Promise<EnrollmentRetrieve> => {
    const response = await portalsApi.get(`lms/enrollments/${eid}`);
    return response.data;
};

export const createEnrollment = async (
    data: EnrollmentCreate,
): Promise<EnrollmentRetrieve> => {
    const response = await portalsApi.post("lms/enrollments", data);
    return response.data;
};

export const updateEnrollment = async (
    eid: string,
    data: EnrollmentUpdate,
): Promise<EnrollmentRetrieve> => {
    const response = await portalsApi.patch(`lms/enrollments/${eid}`, data);
    return response.data;
};

export const attachCertificateToEnrollment = async (
    eid: string,
    file: File,
): Promise<{
    taskId: string;
}> => {
    const formData = new FormData();
    formData.append("file", file);

    const response = await portalsApi.post(
        `lms/enrollments/${eid}/attach-certificate`,
        formData,
    );
    return response.data;
};

export const deleteEnrollment = async (eid: string): Promise<void> => {
    await portalsApi.delete(`lms/enrollments/${eid}`);
};

export const bulkDeleteEnrollments = async (eids: string[]): Promise<void> => {
    await portalsApi.delete("lms/enrollments/bulk_delete", {
        data: { eids },
    });
};
