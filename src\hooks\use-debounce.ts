import { useState, useEffect } from "react";

export function useDebounce<T>(value: T, delay?: number) {
    // State and setters for debounced value
    const _delay = delay || 500;
    const [debouncedValue, setDebouncedValue] = useState(value);

    useEffect(
        () => {
            // Set debouncedValue to value (passed in) after the specified delay
            const handler = setTimeout(() => {
                setDebouncedValue(value);
            }, _delay);

            // Return a cleanup function that will be called every time ...
            // ... useEffect is re-called. useEffect will only be re-called ...
            // ... if value changes (see the inputs array below).

            return () => {
                clearTimeout(handler);
            };
        },

        // Only re-call effect if value or delay changes
        [value, _delay],
    );

    return debouncedValue;
}
