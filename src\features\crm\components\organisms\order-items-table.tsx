import { App, Config<PERSON><PERSON><PERSON>, Switch, Table } from "antd";
import type { TableProps } from "antd";
import { useCallback, useMemo, useState } from "react";
import type { Order, OrderItem, OrderItemOffering } from "../../types/order";
import OrderItemPriceCell from "../atoms/order-item-price-cell";
import OrderItemOfferingCell from "../atoms/order-item-offering-cell";
import OrderItemDiscountCell from "../atoms/order-item-discount-cell";
import OrderItemActionsPopover from "../atoms/order-item-actions-popover";
import { useDeleteOrderItem, useUpdateOrderItem } from "../../hooks/use-order-item";

type OrderItemsTableProps = {
    order: Order;
};

export default function OrderItemsTable({ order }: OrderItemsTableProps) {
    const { message } = App.useApp();
    const { mutate: updateOrderItemMutate } = useUpdateOrderItem({
        onUpdateOrderItemSuccess: () => {
            message.success({
                content: "Precio fijo actualizado",
                duration: 2,
            });
        },
        onUpdateOrderItemError: () => {
            message.error({
                content: "Error al actualizar el precio personalizado",
                duration: 2,
            });
        },
    });

    const { mutate: deleteOrderItem } = useDeleteOrderItem({
        onDeleteOrderItemSuccess: () => {
            message.success({
                content: "Producto eliminado exitosamente",
                duration: 2,
            });
        },
    });

    // Estado para controlar qué items tienen habilitada la edición de precio personalizado
    const [customAmountEnabled, setCustomAmountEnabled] = useState<
        Record<string, boolean>
    >(
        // Inicializar con true para items que ya tienen customAmount
        order.orderItems.reduce(
            (acc, item) => {
                acc[item.oiid] = !!item.customAmount;
                return acc;
            },
            {} as Record<string, boolean>,
        ),
    );

    const handleRemoveProduct = useCallback(
        (orderItem: OrderItem) => {
            deleteOrderItem({
                oid: order.oid,
                orderItem,
            });
        },
        [deleteOrderItem, order.oid],
    );

    const handleCustomAmountToggle = useCallback(
        (record: OrderItem, enabled: boolean) => {
            const { oiid } = record;
            setCustomAmountEnabled((prev) => ({
                ...prev,
                [oiid]: enabled,
            }));

            if (!enabled) {
                // remover custom amount
                updateOrderItemMutate({
                    oid: order.oid,
                    orderItem: {
                        oiid,
                        customAmount: null,
                    },
                });
            } else {
                // Establecer custom amount
                updateOrderItemMutate({
                    oid: order.oid,
                    orderItem: {
                        oiid,
                        customAmount: record.effectiveUnitPrice,
                    },
                });
            }
        },
        [order.oid, updateOrderItemMutate],
    );
    const columns: TableProps<OrderItem>["columns"] = useMemo(
        () => [
            {
                title: "ID",
                dataIndex: "oiid",
                key: "oiid",
                render: (text: string) => (
                    <span className="font-semibold text-gray-600 text-xs">
                        {text?.slice(-6)}
                    </span>
                ),
            },
            {
                title: "PRODUCTO",
                dataIndex: "offering",
                key: "offering",
                render: (content: OrderItemOffering, record: OrderItem) => (
                    <OrderItemOfferingCell
                        content={content}
                        orderItem={record}
                        orderData={order}
                    />
                ),
            },
            {
                title: "DCTO",
                dataIndex: "discount",
                key: "discount",
                render: (_, record: OrderItem) => (
                    <OrderItemDiscountCell record={record} />
                ),
            },
            {
                title: "PRECIO PERSONALIZADO",
                dataIndex: "oiid",
                key: "customAmountSwitch",
                align: "center",
                render: (_, record: OrderItem) => (
                    <Switch
                        size="small"
                        checked={customAmountEnabled[record.oiid] || false}
                        onChange={(checked) =>
                            handleCustomAmountToggle(record, checked)
                        }
                        checkedChildren="ON"
                        unCheckedChildren="OFF"
                    />
                ),
            },
            {
                title: "PRECIO",
                dataIndex: "unitPrice",
                key: "unitPrice",
                render: (_, record: OrderItem) => (
                    <OrderItemPriceCell
                        order={order}
                        record={record}
                        editCustomAmount={customAmountEnabled[record.oiid] || false}
                    />
                ),
            },
            {
                title: "ACCIONES",
                dataIndex: "oiid",
                key: "actions",
                align: "center",
                render: (_, record: OrderItem) => (
                    <OrderItemActionsPopover
                        orderItem={record}
                        order={order}
                        onDeleteOrderItem={handleRemoveProduct}
                    />
                ),
            },
        ],
        [order, customAmountEnabled, handleCustomAmountToggle, handleRemoveProduct],
    );
    return (
        <>
            <ConfigProvider
                theme={{
                    components: {
                        Table: {
                            headerBg: "#FBFCFD",
                            headerColor: "#7a7a7a",
                            borderColor: "#fff",
                            headerSplitColor: "#fafafa",
                            headerBorderRadius: 8,
                            rowHoverBg: "#F6FAFD",
                            rowSelectedBg: "#F6FAFD",
                            rowSelectedHoverBg: "#F6FAFD",
                            footerBg: "#F1F1F1",
                        },
                    },
                }}
            >
                <Table
                    className="rounded-lg"
                    pagination={false}
                    columns={columns}
                    dataSource={order.orderItems}
                    footer={() => (
                        <div className="flex justify-between items-center px-4 py-3">
                            <span className="text-sm text-gray-600">
                                {order.orderItems.length} producto
                                {order.orderItems.length !== 1 ? "s" : ""}
                            </span>
                            <div className="text-right">
                                <span className="text-sm text-gray-600 mr-2">
                                    Total:
                                </span>
                                <span className="text-lg font-semibold text-blue-full">
                                    {order.isInternational ? "$" : "S/"}{" "}
                                    {order.total?.toFixed(2)}
                                </span>
                            </div>
                        </div>
                    )}
                />
            </ConfigProvider>
        </>
    );
}
