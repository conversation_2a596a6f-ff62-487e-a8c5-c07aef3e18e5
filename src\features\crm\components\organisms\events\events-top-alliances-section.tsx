import React from "react";
import { Row, Col } from "antd";
import TopAlliancesTable from "@/features/crm/components/molecules/events/top-alliances-table";
import { useDashboardEventsAnalytics } from "@/features/crm/hooks/use-dashboard-events";
import type { DashboardEventQueryParams } from "@/features/crm/types/dashboard/events";

interface EventsTopAlliancesSectionProps {
    queryParams: DashboardEventQueryParams;
}

const EventsTopAlliancesSection: React.FC<EventsTopAlliancesSectionProps> = ({
    queryParams,
}) => {
    const { data: analyticsData, isLoading } = useDashboardEventsAnalytics(queryParams);

    return (
        <div className="space-y-6">
            {/* Charts Row */}
            <Row gutter={[16, 16]}>
                {/* Top Alliances Table */}
                <Col xs={24}>
                    <TopAlliancesTable
                        data={analyticsData?.topAlliances}
                        isLoading={isLoading}
                    />
                </Col>
            </Row>
        </div>
    );
};

export default EventsTopAlliancesSection;
