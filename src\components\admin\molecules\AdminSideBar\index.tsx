import { Link, useLocation } from "react-router-dom";
import { Menu } from "antd";
import type { MenuProps } from "antd";

import ContactBook from "@assets/icons/huge/contact-book.svg?react";
import Megaphone from "@assets/icons/huge/megaphone.svg?react";
import Program from "@assets/icons/huge/program.svg?react";

type MenuItem = Required<MenuProps>["items"][number];

const items: MenuItem[] = [
    {
        key: "general",
        label: (
            <span className="text-black-medium font-semibold text-xs uppercase">
                general
            </span>
        ),
        type: "group",
        children: [
            {
                key: "dashboard",
                label: (
                    <Link to="/admin" className="font-medium text-sm">
                        Dashboard
                    </Link>
                ),
                icon: <ContactBook />,
            },
        ],
    },
    {
        key: "TABLAS",
        label: (
            <span className="text-black-medium font-semibold text-xs uppercase">
                TABLAS
            </span>
        ),
        type: "group",
        children: [
            {
                key: "instructors",
                label: (
                    <Link to="/admin/instructor" className="font-medium text-sm">
                        Instructores
                    </Link>
                ),
                icon: <ContactBook />,
            },
            {
                key: "testimonials",
                label: (
                    <Link to="/admin/testimonial" className="font-medium text-sm">
                        Testimonios
                    </Link>
                ),
                icon: <Megaphone />,
            },
            {
                key: "offering",
                label: (
                    <Link to="/admin/offering" className="font-medium text-sm">
                        Productos
                    </Link>
                ),
                icon: <Program />,
            },
        ],
    },
];

const getBaseRoute = (path: string): string => {
    const parts = path.split("/").filter(Boolean);
    if (parts.length >= 2) {
        return `/${parts[0]}/${parts[1]}`; // Retorna /admin/[resource]
    }
    return path;
};

export default function AdminSideBar() {
    const location = useLocation();
    const baseRoute = getBaseRoute(location.pathname);

    const PATHS: Record<string, string> = {
        "/admin": "dashboard",
        "/admin/instructor": "instructors",
        "/admin/testimonial": "testimonials",
        "/admin/offering": "offering",
    };

    const selectedKey = PATHS[baseRoute];

    return (
        <Menu
            style={{
                width: 256,
                overflowY: "scroll",
                scrollbarWidth: "none",
                border: 0,
            }}
            defaultSelectedKeys={[selectedKey]}
            defaultOpenKeys={[]}
            mode="inline"
            items={items}
            className="hidden md:block min-w-64 sticky left-0 top-0"
        />
    );
}
