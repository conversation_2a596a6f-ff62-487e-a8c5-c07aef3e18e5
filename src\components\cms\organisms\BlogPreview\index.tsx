import React from "react";
import { BlogPost } from "@myTypes/blog";
import dayjs from "dayjs";
import "dayjs/locale/es";
import "./BlogPreview.css";
import TableOfContents from "./TableOfContents";
import { renderYooptaContent } from "./renderContent";
import { extractHeadings } from "./utils";
import { Avatar } from "antd";
import { BLOG_COVER_DIMENSIONS } from "@lib/constants/blogImageDimensions";

interface BlogPreviewProps {
    blog: BlogPost;
}

/**
 * Component to preview a blog post as it would appear published
 */
const BlogPreview: React.FC<BlogPreviewProps> = ({ blog }) => {
    dayjs.locale("es");

    const formattedDate = blog.publishedAt
        ? dayjs(blog.publishedAt).format("MMM D, YYYY")
        : dayjs().format("MMM D, YYYY");

    // Extract headings for table of contents
    const headings = React.useMemo(() => {
        if (!blog.content) return [];
        return extractHeadings(blog.content);
    }, [blog.content]);

    return (
        <>
            <div className="blog-preview min-w-[100vw] ">
                <div className="max-w-7xl mx-auto w-full">
                    {/* Header with background */}
                    <header className="blog-preview-header">
                        <div className="blog-preview-date">{formattedDate}</div>
                        <h1 className="blog-preview-title">{blog.title}</h1>

                        {blog.authors && blog.authors.length > 0 && (
                            <div className="flex items-center gap-2">
                                {blog.authors.map((author) => (
                                    <div
                                        className="flex items-center gap-2"
                                        key={author.iid}
                                    >
                                        <Avatar
                                            src={author.profilePhoto?.url}
                                            alt={author.fullName}
                                            className="w-10 h-10 rounded-full object-cover"
                                        >
                                            {author.fullName.slice(0, 1)}
                                        </Avatar>
                                        <span>{author.fullName}</span>
                                    </div>
                                ))}
                            </div>
                        )}
                    </header>

                    <section className="blog-preview-toc-section">
                        {headings.length > 0 && (
                            <aside className="blog-preview-toc-aside">
                                {/* Table of contents */}
                                <div className="blog-preview-toc-container">
                                    <h3 className="blog-preview-toc-title">
                                        Tabla de contenido
                                    </h3>
                                    <TableOfContents headings={headings} />
                                </div>
                            </aside>
                        )}

                        <article
                            className={`${headings.length > 0 ? "md:col-span-3" : "md:col-span-4"}`}
                        >
                            {blog.coverImage && (
                                <img
                                    src={blog.coverImage.url}
                                    alt={blog.coverImage.description}
                                    className="mt-4 mx-auto h-auto w-full object-contain"
                                    style={{
                                        aspectRatio:
                                            (blog.coverImage.width ||
                                                BLOG_COVER_DIMENSIONS.width) /
                                            (blog.coverImage.height ||
                                                BLOG_COVER_DIMENSIONS.height),
                                        maxHeight:
                                            blog.coverImage.height ||
                                            BLOG_COVER_DIMENSIONS.height,
                                    }}
                                />
                            )}
                            <div className="blog-preview-container">
                                {/* Summary/Abstract */}
                                {blog.summary && (
                                    <div className="blog-preview-summary">
                                        <p className="blog-preview-summary-text">
                                            {blog.summary}
                                        </p>
                                    </div>
                                )}

                                {/* Main content */}
                                <div className="blog-preview-content">
                                    {blog.content && renderYooptaContent(blog.content)}
                                </div>
                            </div>
                        </article>
                    </section>
                </div>
            </div>

            {/* <pre>{JSON.stringify(blog.content, null, 2)}</pre> */}
        </>
    );
};

export default BlogPreview;
