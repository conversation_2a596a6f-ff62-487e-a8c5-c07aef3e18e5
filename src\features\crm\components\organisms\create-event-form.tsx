import { useMemo } from "react";
import { Button, Divider, Form, Input, Radio, Switch } from "antd";
import FormLabel from "@/features/crm/components/atoms/FormLabel";
import {
    CreateEventFormValues,
    EventModality,
    EventModalityLabels,
    EventStage,
    EventType,
    EventTypeLabels,
} from "@/features/crm/types/event";
import SelectOfferings from "@/features/crm/components/molecules/select-offerings";
import SelectInstructor from "@/features/crm/components/molecules/select-instructor";
import { useCreateEvent } from "@/features/crm/hooks/use-event";

type CreatePaymentFormProps = {
    closeModal?: () => void;
};

export default function CreatePaymentForm({ closeModal }: CreatePaymentFormProps) {
    const [createPaymentForm] = Form.useForm<CreateEventFormValues>();
    const { mutate } = useCreateEvent({
        onSuccess: closeModal,
    });

    const handleOnFinish = (values: CreateEventFormValues) => {
        const { completeInfo, ...payload } = values;
        console.log(completeInfo);
        mutate(payload);
    };

    const modalityOptions = useMemo(
        () =>
            Object.values(EventModality).map((modality) => ({
                value: modality,
                label: EventModalityLabels[modality],
            })),
        [],
    );

    const typeOptions = useMemo(
        () =>
            Object.values(EventType).map((type) => ({
                value: type,
                label: EventTypeLabels[type],
            })),
        [],
    );

    return (
        <Form
            name="create-payment-form"
            layout="vertical"
            onFinish={handleOnFinish}
            form={createPaymentForm}
            initialValues={{
                stage: EventStage.PLANNING,
                modality: EventModality.REMOTE,
                type: EventType.WEBINAR,
                completeInfo: false,
            }}
            colon={false}
        >
            <Form.Item<CreateEventFormValues>
                name="name"
                label={<FormLabel>Nombre del Evento</FormLabel>}
                rules={[
                    {
                        required: true,
                        message: "Por favor ingresa el nombre del evento",
                    },
                ]}
            >
                <Input placeholder="Nombre del Evento" />
            </Form.Item>

            <Form.Item<CreateEventFormValues>
                name="offering"
                label={<FormLabel>Producto</FormLabel>}
            >
                <SelectOfferings />
            </Form.Item>

            <Form.Item<CreateEventFormValues>
                name="instructor"
                label={<FormLabel>Instructor</FormLabel>}
            >
                <SelectInstructor />
            </Form.Item>

            <Form.Item<CreateEventFormValues>
                name="type"
                label={<FormLabel>Tipo</FormLabel>}
                rules={[
                    {
                        required: true,
                        message: "El evento requiere un tipo",
                    },
                ]}
            >
                <Radio.Group
                    block
                    optionType="button"
                    buttonStyle="solid"
                    options={typeOptions}
                />
            </Form.Item>

            <Form.Item<CreateEventFormValues>
                name="modality"
                label={<FormLabel>Modalidad</FormLabel>}
                rules={[
                    {
                        required: true,
                        message: "Debe iniciar el evento en una modalidad",
                    },
                ]}
            >
                <Radio.Group
                    block
                    optionType="button"
                    buttonStyle="solid"
                    options={modalityOptions}
                />
            </Form.Item>

            <Divider />

            <Form.Item<CreateEventFormValues>
                name="completeInfo"
                layout="horizontal"
                tooltip="Si se activa esta opción, se le redirigirá a la vista de edición."
                label={
                    <FormLabel className="text-xs">¿Completar información?</FormLabel>
                }
            >
                <Switch size="small" />
            </Form.Item>

            <div className="grid grid-cols-2 gap-2 items-end">
                <Button onClick={() => {}} className="h-fit py-1.5" size="large">
                    Cancelar
                </Button>
                <Button
                    type="primary"
                    htmlType="submit"
                    className="h-fit py-1.5"
                    size="large"
                    block
                >
                    Guardar
                </Button>
            </div>
        </Form>
    );
}
