import EditPaymentForm from "@/features/crm/components/organisms/edit-payment-form";
import { usePayment } from "@/features/crm/hooks/use-payment";
import CrmLayout from "@/features/crm/layout";
import Spinner from "@components/shared/atoms/Spinner";
import WelcomeBar from "@components/shared/molecules/WelcomeBar";
import { Breadcrumb, Tabs, TabsProps, Typography } from "antd";
import { useEffect } from "react";
import { Link, Navigate, useNavigate, useParams } from "react-router-dom";

const { Text } = Typography;

function PaymentDetail({ pid }: { pid: string }) {
    const navigate = useNavigate();
    const { isLoading, payment, isError } = usePayment(pid);

    useEffect(() => {
        if (isError) {
            navigate("/crm/orders");
        }
    }, [isError, navigate]);

    const tabItems: TabsProps["items"] = [
        {
            key: "general",
            label: "Detalles",
            children: payment ? <EditPaymentForm payment={payment} /> : <Spinner />,
        },
    ];

    return (
        <div className="w-full h-full space-y-5 max-w-7xl">
            <div className="flex justify-between items-center">
                <WelcomeBar helperText="Visualiza & Edita los campos necesarios" />
            </div>
            {isLoading ? (
                <Spinner />
            ) : (
                <>
                    <div className="p-5 bg-white-full rounded-lg space-y-5 shadow-sm">
                        <div className="flex items-center">
                            <Breadcrumb
                                separator=">"
                                items={[
                                    {
                                        title: (
                                            <Link
                                                to="/crm/orders"
                                                className="text-base"
                                            >
                                                Pagos
                                            </Link>
                                        ),
                                    },
                                    {
                                        title: (
                                            <Link
                                                to={`/crm/orders/${pid}`}
                                                className="text-base"
                                            >
                                                {payment?.order?.owner?.fullName}
                                                {" #"}
                                                {payment?.pid.slice(-6)}
                                            </Link>
                                        ),
                                    },
                                    {
                                        title: (
                                            <Text className="text-base">
                                                Ver & Editar
                                            </Text>
                                        ),
                                    },
                                ]}
                            />
                        </div>
                    </div>
                    <div className="space-y-5">
                        <Tabs items={tabItems} />
                    </div>
                </>
            )}
        </div>
    );
}

export default function PaymentsDetailPage() {
    const { pid } = useParams<{ pid: string }>();

    if (!pid) {
        return <Navigate to="/crm/orders" />;
    }

    return (
        <CrmLayout>
            <PaymentDetail pid={pid} />
        </CrmLayout>
    );
}
