import { App, <PERSON><PERSON>, Form } from "antd";
import { TestTemplateBody } from "../../types/template";
import { useTestTemplate } from "../../hooks/use-template";
import PhoneInput from "antd-phone-input";
import { phoneNumberToString } from "@lib/phone-number";

type TestTemplateFormProps = {
    templateId: string;
    onFinish?: () => void;
    closeModal?: () => void;
};

export default function TestTemplateForm({
    templateId,
    onFinish,
    closeModal,
}: TestTemplateFormProps) {
    const { message } = App.useApp();
    const [createTemplateForm] = Form.useForm<TestTemplateBody>();

    const onTestTemplateSuccess = () => {
        createTemplateForm.resetFields();
        onFinish?.();
    };

    const onTestTemplateError = () => {
        onFinish?.();
    };

    const { mutate: testTemplate } = useTestTemplate({
        onSuccess: onTestTemplateSuccess,
        onError: onTestTemplateError,
    });

    const handleOnFinish = (values: TestTemplateBody) => {
        const { phoneNumber: fullPhoneNumber } = values;

        // Parse phone number
        let finalPhoneNumber = "";
        try {
            finalPhoneNumber = phoneNumberToString(fullPhoneNumber);
        } catch (error) {
            message.error("Error al parsear el número de teléfono");
            return;
        }
        const finalData = {
            phoneNumber: finalPhoneNumber,
        };
        testTemplate({ tid: templateId, data: finalData });
    };

    return (
        <div className="space-y-4">
            <div className="mt-2 p-3 rounded-lg bg-blue-50 border border-blue-300">
                <span className="text-blue-700 font-semibold text-sm">
                    <strong>¡Importante!</strong>
                    <ul className="list-disc pl-5 mt-2 space-y-1">
                        <li>
                            Esta función es solo para{" "}
                            <strong className="underline">
                                fines de prueba interna
                            </strong>
                            .
                        </li>
                        <li>
                            Las variables de la plantilla se rellenarán con{" "}
                            <strong>datos de ejemplo</strong>.
                        </li>
                        <li>
                            Utilízalo únicamente para verificar el formato y contenido,{" "}
                            <strong className="underline">
                                NO para enviar mensajes reales a clientes
                            </strong>
                            .
                        </li>
                    </ul>
                </span>
            </div>
            <Form
                name="createTemplate"
                layout="vertical"
                form={createTemplateForm}
                onFinish={handleOnFinish}
                initialValues={{
                    phoneNumber: undefined,
                }}
            >
                <Form.Item<TestTemplateBody>
                    name="phoneNumber"
                    label={<span className="font-semibold text-base">WhatsApp</span>}
                    rules={[
                        {
                            required: true,
                            message: "Por favor, ingrese el nombre del programa",
                        },
                    ]}
                >
                    <PhoneInput
                        preferredCountries={["PE"]}
                        className="py-1"
                        enableSearch
                    />
                </Form.Item>

                <div className="grid grid-cols-2 gap-2">
                    <Button
                        onClick={() => closeModal?.()}
                        className="h-fit"
                        size="large"
                    >
                        Cancelar
                    </Button>
                    <Form.Item>
                        <Button
                            type="primary"
                            htmlType="submit"
                            className="h-fit"
                            size="large"
                            block
                        >
                            Enviar
                        </Button>
                    </Form.Item>
                </div>
            </Form>
        </div>
    );
}
