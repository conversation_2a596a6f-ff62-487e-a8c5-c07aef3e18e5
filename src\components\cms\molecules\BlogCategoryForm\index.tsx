import { Form, Input } from "antd";
import type { FormProps } from "antd";
import { BlogCategory } from "@myTypes/blog";

interface BlogCategoryFormProps {
    initialValues?: Partial<BlogCategory>;
    onFinish: FormProps["onFinish"];
    form: FormProps["form"];
}

export default function BlogCategoryForm({
    initialValues,
    onFinish,
    form,
}: BlogCategoryFormProps) {
    return (
        <Form
            name="blogCategory"
            layout="vertical"
            form={form}
            initialValues={initialValues}
            onFinish={onFinish}
        >
            <div className="grid grid-cols-1 gap-4">
                <Form.Item
                    name="name"
                    label={<span className="font-semibold">Nombre</span>}
                    rules={[
                        {
                            required: true,
                            message: "Por favor ingrese el nombre de la categoría",
                        },
                    ]}
                >
                    <Input placeholder="Ej. Desarrollo Web" className="py-1" />
                </Form.Item>

                <Form.Item
                    name="description"
                    label={<span className="font-semibold">Descripción</span>}
                >
                    <Input.TextArea
                        placeholder="Descripción de la categoría"
                        rows={3}
                        className="py-1"
                    />
                </Form.Item>
            </div>
        </Form>
    );
}
