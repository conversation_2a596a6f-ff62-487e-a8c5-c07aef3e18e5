import { Avatar, Typography } from "antd";
import { User } from "lucide-react";
import { ResponsibleUser } from "../../types/activity";

const { Text } = Typography;

interface ActivityResponsibleCellProps {
    responsible: ResponsibleUser | null;
}

const ActivityResponsibleCell = ({ responsible }: ActivityResponsibleCellProps) => {
    if (!responsible) {
        return (
            <div className="flex items-center gap-2">
                <Avatar size="small" icon={<User size={16} />} />
                <Text type="secondary">Sin asignar</Text>
            </div>
        );
    }

    return (
        <div className="flex items-center gap-2">
            <Avatar size="small">{responsible.fullName.charAt(0).toUpperCase()}</Avatar>
            <div className="flex flex-col">
                <Text className="font-medium">{responsible.fullName}</Text>
                <Text type="secondary" className="text-xs">
                    {responsible.email}
                </Text>
            </div>
        </div>
    );
};

export default ActivityResponsibleCell;
