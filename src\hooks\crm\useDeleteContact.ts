import { HTTP_NO_CONTENT } from "@lib/constants/httpCodes";
import { deleteContact } from "@/features/crm/services/portals/contact";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { AxiosError, AxiosResponse } from "axios";

export const useDeleteContact = () => {
    const queryClient = useQueryClient();

    return useMutation<AxiosResponse, AxiosError, string>({
        mutationFn: (uid: string) => deleteContact(uid),
        onSuccess: (res) => {
            if (res.status === HTTP_NO_CONTENT) {
                queryClient.invalidateQueries({ queryKey: ["contacts"] });
            }
        },
        onError: (err) => {
            console.log(err);
        },
    });
};
