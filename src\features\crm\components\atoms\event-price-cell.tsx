import { DollarSign } from "lucide-react";
import { Tooltip } from "antd";

type EventPriceCellProps = {
    price: string;
};

export default function EventPriceCell({ price }: EventPriceCellProps) {
    const formatPrice = (price: string) => {
        // If price is "0.00" or "0", show as free
        if (!price || price === "0.00" || price === "0") {
            return {
                formattedPrice: "Gratis",
                isFree: true,
            };
        }

        // Try to parse the price as a number
        const numPrice = parseFloat(price);
        if (isNaN(numPrice)) return { formattedPrice: price, isFree: false };

        // Format as currency (assuming PEN/Soles)
        return {
            formattedPrice: new Intl.NumberFormat("es-PE", {
                style: "currency",
                currency: "PEN",
                minimumFractionDigits: 2,
            }).format(numPrice),
            isFree: false,
        };
    };

    const { formattedPrice, isFree } = formatPrice(price);

    return (
        <Tooltip title={isFree ? "Este evento es gratuito" : "Precio del evento"}>
            <div className="flex items-center gap-2">
                <DollarSign
                    size={18}
                    className={isFree ? "text-green-500" : "text-amber-500"}
                    strokeWidth={1.75}
                />
                <span
                    className={`text-base font-semibold ${isFree ? "text-green-600" : "text-gray-700"}`}
                >
                    {formattedPrice}
                </span>
            </div>
        </Tooltip>
    );
}
