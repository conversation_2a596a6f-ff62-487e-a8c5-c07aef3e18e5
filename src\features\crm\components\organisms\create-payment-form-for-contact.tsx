import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>r, <PERSON>, InputNumber, Switch, Tooltip } from "antd";
import { PaymentCreateForm, PaymentCreateRequest } from "@/features/crm/types/payment";
import SelectOrderByContact from "@/features/crm/components/molecules/select-order-by-contact";
import RadioCurrency from "@/features/crm/components/molecules/radio-currency";
import { OrderCurrency } from "@/features/crm/types/order";
import SelectPaymentMethod from "@/features/crm/components/molecules/select-payment-method";
import UploadVoucher from "@/features/crm/components/molecules/upload-voucher";
import { useCreatePayment } from "../../hooks/use-payment";
import { Info } from "lucide-react";
import dayjs from "dayjs";

type CreatePaymentFormForContactProps = {
    onFinish?: () => void;
    contactId: string;
};

export default function CreatePaymentFormForContact({
    onFinish,
    contactId,
}: CreatePaymentFormForContactProps) {
    const [createPaymentForm] = Form.useForm();
    const isPaid = Form.useWatch(["isPaid"], createPaymentForm);
    const currency = Form.useWatch(["currency"], createPaymentForm);

    const handleOnCreatePaymentSuccess = () => {
        createPaymentForm.resetFields();
        onFinish?.();
    };

    const handleOnCreatePaymentError = () => {
        createPaymentForm.resetFields();
        onFinish?.();
    };

    const { mutate: createPaymentMutate } = useCreatePayment({
        onCreatePaymentSuccess: handleOnCreatePaymentSuccess,
        onCreatePaymentError: handleOnCreatePaymentError,
    });

    const handleOnFinish = (values: PaymentCreateForm) => {
        const {
            scheduledPaymentDate: dayjsPaymentDate,
            voucher: voucherFile,
            ...rest
        } = values;

        const paymentDate = isPaid
            ? new Date().toISOString()
            : dayjs(dayjsPaymentDate).toISOString();
        const voucher = voucherFile?.[0]?.response?.fid;

        const paymentCreateRequest: PaymentCreateRequest = {
            ...rest,
            scheduledPaymentDate: paymentDate,
            voucher,
            isFirstPayment: values.isFirstPayment || false,
        };
        createPaymentMutate(paymentCreateRequest);
    };

    return (
        <Form
            name="create-payment-form-contact"
            layout="vertical"
            form={createPaymentForm}
            onFinish={handleOnFinish}
            initialValues={{
                currency: OrderCurrency.PEN,
                isPaid: true,
                isFirstPayment: false,
                amount: 0,
            }}
        >
            <Form.Item<PaymentCreateForm>
                name="order"
                label={<span className="font-semibold">Orden</span>}
                rules={[{ required: true, message: "Por favor seleccione una orden" }]}
            >
                <SelectOrderByContact contactId={contactId} />
            </Form.Item>
            <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                    <Form.Item<PaymentCreateForm>
                        name="isPaid"
                        label={<span className="font-semibold">¿Pagar ahora?</span>}
                    >
                        <Switch />
                    </Form.Item>
                    <Form.Item<PaymentCreateForm>
                        name="isFirstPayment"
                        label={
                            <span className="font-semibold flex items-center gap-1">
                                ¿Primer pago?
                                <Tooltip title="Indica si este pago es para cerrar la venta (primer pago del cliente)">
                                    <Info
                                        size={14}
                                        className="text-gray-400 cursor-help"
                                    />
                                </Tooltip>
                            </span>
                        }
                    >
                        <Switch />
                    </Form.Item>
                </div>
                <div className="grid grid-cols-2 gap-4">
                    <Form.Item<PaymentCreateForm>
                        name="currency"
                        label={<span className="font-semibold">Moneda</span>}
                    >
                        <RadioCurrency />
                    </Form.Item>
                    <Form.Item<PaymentCreateForm>
                        name="amount"
                        label={<span className="font-semibold">Monto</span>}
                        rules={[
                            { required: true, message: "Por favor ingrese el monto" },
                        ]}
                    >
                        <InputNumber
                            prefix={currency === OrderCurrency.PEN ? "S/ " : "$ "}
                            min={0}
                            className="w-full"
                        />
                    </Form.Item>
                </div>
            </div>
            {isPaid ? (
                <Form.Item<PaymentCreateForm>
                    name="voucher"
                    label={<span className="font-semibold">Voucher</span>}
                    valuePropName="listFile"
                >
                    <UploadVoucher />
                </Form.Item>
            ) : (
                <Form.Item<PaymentCreateForm>
                    name="scheduledPaymentDate"
                    label={
                        <span className="font-semibold">Fecha programada de pago</span>
                    }
                    rules={
                        !isPaid
                            ? [
                                  {
                                      required: true,
                                      message: "Por favor ingrese la fecha de pago",
                                  },
                              ]
                            : []
                    }
                >
                    <DatePicker className="w-full" />
                </Form.Item>
            )}

            <Form.Item<PaymentCreateForm>
                name="paymentMethod"
                label={<span className="font-semibold">Método de pago</span>}
            >
                <SelectPaymentMethod />
            </Form.Item>

            <Divider />

            <div className="grid grid-cols-2 gap-2 items-end">
                <Button onClick={() => {}} className="h-fit" size="large">
                    Cancelar
                </Button>
                <Button
                    type="primary"
                    htmlType="submit"
                    className="h-fit"
                    size="large"
                    block
                >
                    Guardar
                </Button>
            </div>
        </Form>
    );
}
