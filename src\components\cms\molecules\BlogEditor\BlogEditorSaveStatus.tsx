import { Typography } from "antd";
import { SavePostStatusEnum, useSavePostStore } from "@store/savePostStore";
import CloudUload from "@assets/shapes/cloud-upload.svg?react";
import { Loader2 } from "lucide-react";

const { Text } = Typography;

const BlogEditorSaveStatus = () => {
    const { saveStatus } = useSavePostStore();

    switch (saveStatus) {
        case SavePostStatusEnum.SAVING:
            return (
                <div className="flex items-center text-gray-500">
                    <Loader2 size={14} className="animate-spin mr-1" />
                    <Text className="text-xs">Guardando...</Text>
                </div>
            );
        case SavePostStatusEnum.SAVED:
            return (
                <div className="flex items-center text-green-500">
                    <CloudUload className="mr-1" />
                    <Text className="text-xs">Guardado</Text>
                </div>
            );

        case SavePostStatusEnum.ERROR:
            return (
                <div className="flex items-center text-red-500">
                    <CloudUload className="mr-1" />
                    <Text className="text-xs">Error al guardar</Text>
                </div>
            );
        case SavePostStatusEnum.IDLE:
            return null;
        default:
            return null;
    }
};

export default BlogEditorSaveStatus;
