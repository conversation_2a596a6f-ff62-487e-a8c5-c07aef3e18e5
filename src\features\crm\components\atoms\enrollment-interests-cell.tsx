import { Tag, Tooltip } from "antd";
import { Heart, Radio } from "lucide-react";
import { EventScheduleEnrollmentPartnership } from "../../types/event-schedule-enrollment";

interface EnrollmentInterestsCellProps {
    interests: string[];
    diffusionChannel?: string;
    partnership?: EventScheduleEnrollmentPartnership | null;
}

export default function EnrollmentInterestsCell({
    interests,
    diffusionChannel,
    partnership,
}: EnrollmentInterestsCellProps) {
    // Ensure interests is always an array
    const interestsArray = Array.isArray(interests) ? interests : [];

    return (
        <div className="flex flex-col gap-2">
            {/* Interests */}
            {interestsArray && interestsArray.length > 0 && (
                <div className="flex flex-col gap-1">
                    <div className="flex items-center gap-1">
                        <Heart className="w-3 h-3 text-red-500" />
                        <span className="text-xs text-gray-500 font-medium">
                            Intereses:
                        </span>
                    </div>
                    <div className="flex flex-wrap gap-1">
                        {interestsArray.slice(0, 2).map((interest, index) => (
                            <Tag key={index} color="pink" className="text-xs">
                                {interest}
                            </Tag>
                        ))}
                        {interestsArray.length > 2 && (
                            <Tooltip title={interestsArray.slice(2).join(", ")}>
                                <Tag color="pink" className="text-xs">
                                    +{interestsArray.length - 2} más
                                </Tag>
                            </Tooltip>
                        )}
                    </div>
                </div>
            )}

            {/* Diffusion Channel */}
            {diffusionChannel && (
                <div className="flex items-center gap-1">
                    <Radio className="w-3 h-3 text-blue-500" />
                    <Tag color="blue" className="text-xs">
                        {diffusionChannel}
                    </Tag>
                </div>
            )}

            {/* Partnership */}
            {partnership && (
                <div className="flex items-center gap-1">
                    <Tag color="purple" className="text-xs">
                        {partnership.name}
                    </Tag>
                </div>
            )}
        </div>
    );
}
