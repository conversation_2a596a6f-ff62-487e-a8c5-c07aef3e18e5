import { useState } from "react";
import { <PERSON><PERSON>, Popconfirm, DatePicker, message } from "antd";
import { Edit } from "lucide-react";
import { useMutation } from "@tanstack/react-query";
import dayjs from "dayjs";
import { updateCredential } from "../../services";

export default function ChangeCertificateIssuedDate({
    cid,
    initialIssuedAt,
}: {
    cid: string | undefined;
    initialIssuedAt: string | undefined;
}) {
    const [selectedDate, setSelectedDate] = useState<dayjs.Dayjs | null>(
        initialIssuedAt ? dayjs(initialIssuedAt) : null,
    );
    const [open, setOpen] = useState(false);

    const mutation = useMutation({
        mutationFn: (data: { issuedAt: string }) => updateCredential(cid!, data),
        onSuccess: () => {
            message.success("Fecha de emisión actualizada exitosamente");
            setOpen(false);
            setSelectedDate(null);
        },
        onError: () => {
            message.error("Error al actualizar la fecha de emisión");
        },
    });

    const handleConfirm = () => {
        if (!selectedDate) {
            message.warning("Por favor selecciona una fecha");
            return;
        }
        mutation.mutate({ issuedAt: selectedDate.toISOString() });
    };

    const handleCancel = () => {
        setOpen(false);
        setSelectedDate(null);
    };

    const disabledDate = (current: dayjs.Dayjs) => {
        // Deshabilitar fechas futuras (después de hoy)
        return current && current > dayjs().endOf("day");
    };

    if (!cid) return null;

    return (
        <Popconfirm
            title="Cambiar fecha de emisión"
            description={
                <div className="py-2">
                    <DatePicker
                        value={selectedDate}
                        onChange={setSelectedDate}
                        placeholder="Selecciona la nueva fecha"
                        className="w-full"
                        format="DD/MM/YYYY"
                        disabledDate={disabledDate}
                    />
                </div>
            }
            open={open}
            onConfirm={handleConfirm}
            onCancel={handleCancel}
            okText="Confirmar"
            cancelText="Cancelar"
            okButtonProps={{ loading: mutation.isPending }}
        >
            <Button
                type="text"
                icon={<Edit size={14} />}
                size="small"
                onClick={() => setOpen(true)}
                className="text-blue-600 hover:text-blue-800"
            />
        </Popconfirm>
    );
}
