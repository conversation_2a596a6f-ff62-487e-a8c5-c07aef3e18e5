import { useMutation } from "@tanstack/react-query";
import { type AxiosError } from "axios";
import { CreateOrderItem, OrderItem } from "../types/order";
import queryClient from "@lib/queryClient";
import {
    createOrderItem,
    deleteOrderItem,
    partialUpdateOrderItem,
    sendClassroomInvitation,
    createEnrollment,
} from "../services/portals/order-item";
import { useApiError } from "@hooks/use-api-error";

type UseCreateOrderItemProps = {
    onCreateOrderSuccess?: (item: OrderItem) => void;
    onCreateOrderError?: (error: AxiosError) => void;
};

export const useCreateOrderItem = ({
    onCreateOrderSuccess,
    onCreateOrderError,
}: UseCreateOrderItemProps = {}) => {
    const { handleError } = useApiError({
        title: "Error al agregar producto",
        genericMessage: "No se pudo agregar el producto",
    });
    return useMutation<OrderItem, AxiosError, CreateOrderItem>({
        mutationFn: (orderItem) => createOrderItem(orderItem),

        onSuccess: (item, variables) => {
            onCreateOrderSuccess?.(item);
            queryClient.invalidateQueries({ queryKey: ["order", variables.order] });
        },
        onError: (error) => {
            handleError(error);
            onCreateOrderError?.(error);
        },
    });
};

type UseUpdateOrdenItemProps = {
    onUpdateOrderItemSuccess?: (item: Partial<OrderItem>) => void;
    onUpdateOrderItemError?: () => void;
};

export const useUpdateOrderItem = ({
    onUpdateOrderItemSuccess,
    onUpdateOrderItemError,
}: UseUpdateOrdenItemProps = {}) => {
    return useMutation<
        Partial<OrderItem>,
        AxiosError,
        { oid: string; orderItem: Partial<OrderItem> }
    >({
        mutationFn: ({ orderItem }) => partialUpdateOrderItem(orderItem),

        onSuccess: (item, variables) => {
            onUpdateOrderItemSuccess?.(item);
            queryClient.invalidateQueries({ queryKey: ["order", variables.oid] });
        },
        onError: () => {
            onUpdateOrderItemError?.();
        },
    });
};

type UseDeleteOrderItemProps = {
    onDeleteOrderItemSuccess?: (item: OrderItem) => void;
    onDeleteOrderItemError?: () => void;
};

export const useDeleteOrderItem = ({
    onDeleteOrderItemSuccess,
    onDeleteOrderItemError,
}: UseDeleteOrderItemProps = {}) => {
    const { handleError } = useApiError({
        title: "Error al eliminar el producto",
    });

    return useMutation<OrderItem, AxiosError, { oid: string; orderItem: OrderItem }>({
        mutationFn: async ({ orderItem }) => {
            await deleteOrderItem(orderItem);
            return orderItem;
        },

        onSuccess: (item, variables) => {
            onDeleteOrderItemSuccess?.(item);
            queryClient.invalidateQueries({ queryKey: ["order", variables.oid] });
        },
        onError: (error: AxiosError) => {
            handleError(error);
            onDeleteOrderItemError?.();
        },
    });
};

type UseSendClassroomInvitationProps = {
    onSendInvitationSuccess?: () => void;
    onSendInvitationError?: (error: AxiosError) => void;
};

export const useSendClassroomInvitation = ({
    onSendInvitationSuccess,
    onSendInvitationError,
}: UseSendClassroomInvitationProps = {}) => {
    return useMutation<void, AxiosError, { oid: string; orderItemId: string }>({
        mutationFn: async ({ orderItemId }) => {
            await sendClassroomInvitation(orderItemId);
        },

        onSuccess: (_, variables) => {
            onSendInvitationSuccess?.();
            queryClient.invalidateQueries({ queryKey: ["order", variables.oid] });
        },
        onError: (error) => {
            onSendInvitationError?.(error);
        },
    });
};

type UseCreateEnrollmentProps = {
    onCreateEnrollmentSuccess?: () => void;
    onCreateEnrollmentError?: (error: AxiosError) => void;
};

export const useCreateEnrollment = ({
    onCreateEnrollmentSuccess,
    onCreateEnrollmentError,
}: UseCreateEnrollmentProps = {}) => {
    const { handleError } = useApiError({
        title: "Error al crear matrícula",
        genericMessage: "No se pudo crear la matrícula",
    });

    return useMutation<void, AxiosError, { oid: string; orderItemId: string }>({
        mutationFn: async ({ orderItemId }) => {
            await createEnrollment(orderItemId);
        },

        onSuccess: (_, variables) => {
            onCreateEnrollmentSuccess?.();
            queryClient.invalidateQueries({ queryKey: ["order", variables.oid] });
        },
        onError: (error) => {
            handleError(error);
            onCreateEnrollmentError?.(error);
        },
    });
};
