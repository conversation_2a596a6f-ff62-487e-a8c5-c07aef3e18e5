import { Template, TemplateStatus, TemplateStatusLabel } from "../../types/template";
import { useMemo, useState } from "react";
import {
    Button,
    ConfigProvider,
    Dropdown,
    Empty,
    Modal,
    Table,
    Tag,
    Typography,
} from "antd";
import type { TableProps } from "antd";
import { Link, useNavigate } from "react-router-dom";

const { Text } = Typography;

import Spinner from "@components/shared/atoms/Spinner";
import Trash from "@assets/icons/huge/trash-white.svg?react";
import DeleteStroke from "@assets/icons/huge/delete-stroke.svg?react";
import EditStroke from "@assets/icons/huge/edit-stroke.svg?react";
import MoreVertical from "@assets/icons/huge/more-vertical.svg?react";

import { formatDateTime } from "@lib/helpers";
import { useBulkDeleteTemplates, useDeleteTemplate } from "../../hooks/use-template";

type TemplatesTableProps = {
    templates: Template[];
    isLoading?: boolean;
};

const INITIAL_COLUMNS: TableProps<Template>["columns"] = [
    {
        title: "NOMBRE",
        dataIndex: "name",
        key: "name",
        render: (name: string, record: Template) => (
            <Link to={`${record.tid}`} className="text-blue-full font-medium underline">
                {name}
            </Link>
        ),
    },
    {
        title: "ESTADO",
        dataIndex: "status",
        key: "status",
        render: (status: TemplateStatus) => (
            <Tag
                bordered={false}
                color={status === TemplateStatus.APPROVED ? "green" : "volcano"}
                className="rounded-full px-3"
            >
                {TemplateStatusLabel[status]}
            </Tag>
        ),
    },
    {
        title: "ÚLTIMA ACTUALIZACIÓN",
        dataIndex: "updatedAt",
        key: "updatedAt",
        render: (updatedAt: string) => {
            const formattedDate = formatDateTime(updatedAt);
            return <Text>{formattedDate}</Text>;
        },
    },
    {
        title: "FECHA DE CREACIÓN",
        dataIndex: "createdAt",
        key: "createdAt",
        render: (createdAt: string) => {
            const formattedDate = formatDateTime(createdAt);
            return <Text>{formattedDate}</Text>;
        },
    },
];

export default function TemplatesTable({ templates, isLoading }: TemplatesTableProps) {
    const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
    const navigate = useNavigate();
    const { mutate: deleteTemplateMutation } = useDeleteTemplate();
    const { mutate: bulkDeleteTemplatesMutation } = useBulkDeleteTemplates({
        onSuccess: () => {
            // limpiar selección
            setSelectedRowKeys([]);
        },
    });

    const tableColumns = useMemo(() => {
        return INITIAL_COLUMNS || [];
    }, []);

    const handleRowAction = (key: string, record: Template) => {
        switch (key) {
            case "delete":
                Modal.confirm({
                    title: "¿Está seguro que desea eliminar esta plantilla?",
                    content: `La plantilla "${record.name}" será eliminada permanentemente.`,
                    okText: "Eliminar",
                    okButtonProps: { danger: true },
                    cancelText: "Cancelar",
                    onOk() {
                        deleteTemplateMutation({ tid: record.tid });
                    },
                });
                break;
            case "edit":
                // Navigate to edit page
                navigate(`/crm/templates/${record.tid}`);
                break;
        }
    };

    const actionColumns: TableProps<Template>["columns"] = [
        {
            title: "ACCIONES",
            key: "actions",
            render: (record: Template) => (
                <Dropdown
                    trigger={["click"]}
                    menu={{
                        items: [
                            {
                                key: "edit",
                                label: (
                                    <div className="flex items-center gap-2 text-blue-full">
                                        <EditStroke className="w-5 h-5" /> Editar
                                    </div>
                                ),
                            },
                            {
                                key: "delete",
                                label: (
                                    <div className="flex items-center gap-2 text-state-red-full">
                                        <DeleteStroke className="w-5 h-5" /> Eliminar
                                    </div>
                                ),
                            },
                        ],
                        onClick: ({ key }) => {
                            handleRowAction(key, record);
                        },
                    }}
                    placement="bottomRight"
                >
                    <Button
                        icon={<MoreVertical className="w-5 h-5" />}
                        type="text"
                        size="small"
                    />
                </Dropdown>
            ),
        },
    ];

    const rowSelection = {
        selectedRowKeys,
        onChange: (newSelectedRowKeys: React.Key[]) => {
            setSelectedRowKeys(newSelectedRowKeys);
        },
    };

    const handleBulkDelete = () => {
        if (selectedRowKeys.length === 0) return;

        Modal.confirm({
            title: "¿Está seguro de eliminar las plantillas seleccionadas?",
            content: `Se eliminarán ${selectedRowKeys.length} plantillas.`,
            okText: "Eliminar",
            okButtonProps: { danger: true },
            cancelText: "Cancelar",
            onOk() {
                bulkDeleteTemplatesMutation({ tids: selectedRowKeys as string[] });
            },
        });
    };

    return (
        <ConfigProvider
            theme={{
                components: {
                    Table: {
                        headerBg: "#FBFCFD",
                        borderColor: "#fff",
                        headerSplitColor: "#fafafa",
                        headerBorderRadius: 8,
                        rowHoverBg: "#F6FAFD",
                        rowSelectedBg: "#F6FAFD",
                        rowSelectedHoverBg: "#F6FAFD",
                        footerBg: "#F1F1F1",
                    },
                },
            }}
        >
            <Table
                rowSelection={rowSelection}
                locale={{
                    emptyText: <>{isLoading ? <Spinner /> : <Empty />}</>,
                }}
                columns={[...tableColumns, ...actionColumns]}
                dataSource={templates}
                className="rounded-lg shadow-sm"
                footer={() => ""}
                pagination={false}
                rowKey="tid"
                scroll={{ x: "max-content" }}
            />
            <div className="flex justify-between items-center mt-4">
                <div className="flex items-center gap-3">
                    <Button
                        danger
                        type="primary"
                        size="large"
                        icon={<Trash />}
                        disabled={selectedRowKeys.length === 0}
                        onClick={handleBulkDelete}
                    >
                        Eliminar ({selectedRowKeys.length})
                    </Button>
                </div>
                <div>
                    <Text className="text-sm text-gray-500">
                        Mostrando {templates.length} plantillas
                    </Text>
                </div>
            </div>
        </ConfigProvider>
    );
}
