import {
    CreateBroadcastMessageBody,
    BroadcastMessage,
    PartialUpdateBroadcastMessageBody,
} from "@myTypes/broadcast-message";
import { portalsApi } from "@services/portals";
import dayjs from "dayjs";

export type ListBroadcastMessageQuery = {
    page?: number;
    pageSize?: number;
    search?: string;
    sortBy?: string;
    order?: "asc" | "desc";
};

export const listBroadcastMessages = async (params: ListBroadcastMessageQuery) => {
    const response = await portalsApi.get("crm/broadcast-messages", {
        params,
    });
    return response.data;
};

export const createBroadcastMessage = async (data: CreateBroadcastMessageBody) => {
    const dayjsDate = dayjs(data.sendAt);
    const normalDate: Date = dayjsDate.toDate();
    data.sendAt = normalDate;
    const response = await portalsApi.post("crm/broadcast-messages", data);
    return response.data;
};

export const retrieveBroadcastMessage = async (
    mid: string,
): Promise<BroadcastMessage> => {
    const response = await portalsApi.get(`crm/broadcast-messages/${mid}`);
    return response.data;
};

export const deleteBroadcastMessage = async (mid: string) => {
    const response = await portalsApi.delete(`crm/broadcast-messages/${mid}`);
    return response.data;
};

export const updateBroadcastMessage = async (
    mid: string,
    data: PartialUpdateBroadcastMessageBody,
) => {
    const dayjsDate = dayjs(data.sendAt);
    const normalDate: Date = dayjsDate.toDate();
    data.sendAt = normalDate;
    const response = await portalsApi.patch(`crm/broadcast-messages/${mid}`, data);
    return response.data;
};

export const sendBroadcastMessage = async (mid: string) => {
    const response = await portalsApi.post(`crm/broadcast-messages/${mid}/send`);
    return response.data;
};
