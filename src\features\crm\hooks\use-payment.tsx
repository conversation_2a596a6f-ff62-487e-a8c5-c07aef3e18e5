import { useMutation, useQuery } from "@tanstack/react-query";
import {
    createPayment,
    listPayments,
    retrievePayment,
} from "../services/portals/payment";
import { AxiosError } from "axios";
import {
    ListPaymentsQueryParams,
    PaymentCreateRequest,
    PaymentRetrieve,
} from "../types/payment";
import { App } from "antd";
import queryClient from "@lib/queryClient";
import { decamelize } from "humps";

type UsePaymentsProps = {
    queryParams?: ListPaymentsQueryParams;
    enabled?: boolean;
};

export const usePayments = ({ enabled, queryParams }: UsePaymentsProps = {}) => {
    const { data, isLoading, isError } = useQuery({
        queryKey: ["payments", queryParams],
        queryFn: () =>
            listPayments({
                ...queryParams,
                filterDateBy: queryParams?.filterDateBy
                    ? decamelize(queryParams.filterDateBy, { separator: "_" })
                    : undefined,
                status: queryParams?.status
                    ? decamelize(queryParams.status, { separator: "_" })
                    : undefined,
            }),
        enabled,
    });

    const { count, results: payments } = data || {
        count: 0,
        results: [],
    };

    return {
        isLoading,
        isError,
        payments,
        count,
    };
};

export const usePayment = (pid: string) => {
    const { data, isLoading, isError } = useQuery({
        queryKey: ["payment", pid],
        queryFn: () => retrievePayment(pid),
        enabled: !!pid,
    });

    return {
        isLoading,
        isError,
        payment: data,
    };
};

type UseCreatePaymentProps = {
    onCreatePaymentSuccess?: () => void;
    onCreatePaymentError?: () => void;
};

export const useCreatePayment = ({
    onCreatePaymentSuccess,
    onCreatePaymentError,
}: UseCreatePaymentProps = {}) => {
    const { message, notification } = App.useApp();
    return useMutation<PaymentRetrieve, AxiosError, PaymentCreateRequest>({
        mutationFn: (newPayment) => createPayment(newPayment),
        onSuccess: () => {
            message.success({
                content: "Pago creado exitosamente",
                duration: 2,
            });
            queryClient.invalidateQueries({
                queryKey: ["payments"],
            });
            onCreatePaymentSuccess?.();
        },
        onError: () => {
            notification.error({
                message: "Error al crear el pago",
                description: "Ha ocurrido un error al intentar crear el pago",
                duration: 2,
            });
            onCreatePaymentError?.();
        },
    });
};
