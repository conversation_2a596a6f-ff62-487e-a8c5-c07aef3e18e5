import React from "react";
import { Card, Empty } from "antd";
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveC<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Legend } from "recharts";

interface ChartDataItem {
    name: string;
    value: number;
}

interface PieChartCardProps {
    title: string;
    data: ChartDataItem[];
    colors: string[];
    icon?: React.ReactNode;
}

const RADIAN = Math.PI / 180;

interface CustomLabelProps {
    cx: number;
    cy: number;
    midAngle: number;
    innerRadius: number;
    outerRadius: number;
    percent: number;
    index: number;
}

interface CustomTooltipProps {
    active?: boolean;
    payload?: Array<{
        name: string;
        value: number;
        color: string;
    }>;
}

const renderCustomizedLabel = ({
    cx,
    cy,
    midAngle,
    innerRadius,
    outerRadius,
    percent,
}: CustomLabelProps) => {
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
        <text x={x} y={y} fill="white" textAnchor="middle" dominantBaseline="central">
            {`${(percent * 100).toFixed(0)}%`}
        </text>
    );
};

const CustomTooltip = ({ active, payload }: CustomTooltipProps) => {
    if (active && payload && payload.length) {
        const data = payload[0];
        return (
            <div className="bg-white-full p-3 border border-gray-200 shadow-md rounded">
                <p className="font-medium text-gray-800">{data.name}</p>
                <p className="text-sm text-gray-600">
                    Cantidad: <span className="font-semibold">{data.value}</span>
                </p>
            </div>
        );
    }
    return null;
};

const PieChartCard: React.FC<PieChartCardProps> = ({ title, data, colors, icon }) => {
    return (
        <Card
            title={
                <div className="flex items-center gap-2">
                    {icon}
                    <span>{title}</span>
                </div>
            }
            className="shadow-md h-full"
        >
            <div className="h-64">
                {data.length ? (
                    <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                            <Pie
                                data={data}
                                cx="50%"
                                cy="50%"
                                labelLine={false}
                                label={renderCustomizedLabel}
                                outerRadius={80}
                                fill="#8884d8"
                                dataKey="value"
                            >
                                {data.map((_, index) => (
                                    <Cell
                                        key={`cell-${index}`}
                                        fill={colors[index % colors.length]}
                                    />
                                ))}
                            </Pie>
                            <Tooltip content={<CustomTooltip />} />
                            <Legend />
                        </PieChart>
                    </ResponsiveContainer>
                ) : (
                    <div className="h-full flex items-center justify-center">
                        <Empty />
                    </div>
                )}
            </div>
        </Card>
    );
};

export default PieChartCard;
