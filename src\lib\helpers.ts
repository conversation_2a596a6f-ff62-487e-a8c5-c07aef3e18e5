import { FilterParams } from "@myTypes/base";

export const formatDateTime = (strDate: string | undefined): string => {
    if (!strDate) return "";
    const date = new Date(strDate);
    const formattedDate = Intl.DateTimeFormat("es-PE", {
        weekday: "short", // Abbreviated weekday name
        year: "numeric",
        month: "short", // Full month name
        day: "2-digit",
        hour: "numeric",
        minute: "2-digit",
        second: "2-digit",
        hour12: true, // 12-hour format
        // timeZone: "America/Lima", // Timezone for Peru
    }).format(date);

    return formattedDate;
};

export const formatDate = (date: string | undefined): string => {
    if (!date) return "";
    const formattedDate = Intl.DateTimeFormat("es-PE", {
        year: "numeric",
        month: "short", // Full month name
        day: "2-digit",
    }).format(new Date(date));

    return formattedDate;
};

export const getBase64 = (file: Blob): Promise<string> => {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = (error) => reject(error);
    });
};

export const filtersToUrlParams = (
    filters: FilterParams,
): Record<string, string | string[]> => {
    const params: Record<string, string | string[]> = {};

    Object.entries(filters).forEach(([key, value]) => {
        if (value) {
            if (Array.isArray(value)) {
                value.forEach((val) => {
                    params[key] = val;
                });
            } else {
                params[key] = value;
            }
        }
    });

    return params;
};

export const truncate = (str: string, n: number): string => {
    return str.length > n ? str.slice(0, n - 1) + "..." : str;
};

export function hexToRgb(hex: string) {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result
        ? {
              r: parseInt(result[1], 16),
              g: parseInt(result[2], 16),
              b: parseInt(result[3], 16),
          }
        : null;
}

/** Get the text color automatically based on the background color for better contrast
 * @param backgroundColor - The background color of the text in hexadecimal format
 * @returns The text color in hexadecimal format
 */
export const getTextColor = (backgroundColor: string) => {
    const calculateLuminance = ({ r, g, b }: { r: number; g: number; b: number }) => {
        const [R, G, B] = [r, g, b].map((channel) => {
            const c = channel / 255;
            return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
        });
        return 0.2126 * R + 0.7152 * G + 0.0722 * B;
    };

    const rgb = hexToRgb(backgroundColor);

    if (!rgb) {
        return "#FFFFFF";
    }

    const luminance = calculateLuminance(rgb);

    // Devuelve blanco o negro según la luminancia
    return luminance > 0.5 ? "#000000" : "#FFFFFF";
};

/** Get the file UUID from a public image using the file URL from bucket */
export function extractFileIdFromUrl(url: string): string | null {
    const match = url.match(/\/public\/([^/]+)/);
    return match ? match[1] : null;
}
