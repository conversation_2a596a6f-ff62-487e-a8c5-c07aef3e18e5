import React from "react";
import { Card, Empty } from "antd";
import {
    Re<PERSON>onsive<PERSON><PERSON><PERSON>,
    <PERSON>Chart,
    Bar,
    XAxis,
    <PERSON><PERSON><PERSON><PERSON>,
    Tooltip,
    Cell,
} from "recharts";
import { Target } from "lucide-react";
import type { InterestSegmentation } from "@/features/crm/types/dashboard/events";

interface InterestsSegmentationChartProps {
    data?: InterestSegmentation[];
    isLoading?: boolean;
}

const COLORS = ["#1890ff", "#52c41a", "#faad14", "#722ed1", "#fa541c", "#13c2c2"];

interface CustomTooltipProps {
    active?: boolean;
    payload?: Array<{
        color: string;
        name: string;
        value: string | number;
        dataKey: string;
    }>;
    label?: string;
}

const InterestsSegmentationChart: React.FC<InterestsSegmentationChartProps> = ({
    data = [],
    isLoading = false,
}) => {
    const getColor = (index: number) => {
        return COLORS[index % COLORS.length];
    };

    const CustomTooltip = ({ active, payload, label }: CustomTooltipProps) => {
        if (active && payload && payload.length) {
            const { value } = payload[0];
            return (
                <div className="bg-white-full p-3 border border-gray-200 shadow-md rounded">
                    <p className="font-medium">{label}</p>
                    <p style={{ color: payload[0].color }}>Cantidad: {value}</p>
                </div>
            );
        }
        return null;
    };

    // Calculate dynamic height based on number of items
    const chartHeight = Math.max(60 * data.length, 300);

    return (
        <Card
            title={
                <div className="flex items-center gap-2">
                    <Target size={20} className="text-blue-500" />
                    <span>Segmentación por Intereses</span>
                </div>
            }
            className="shadow-md h-full"
        >
            {data.length && !isLoading ? (
                <div className="max-h-[400px] overflow-y-auto">
                    <div style={{ height: chartHeight, width: "100%" }}>
                        <ResponsiveContainer width="100%" height="100%">
                            <BarChart
                                data={data}
                                layout="vertical"
                                margin={{
                                    left: 0,
                                    top: 10,
                                    bottom: 10,
                                    right: 30,
                                }}
                            >
                                <XAxis hide axisLine={false} type="number" />
                                <YAxis
                                    yAxisId={0}
                                    dataKey="specialization"
                                    type="category"
                                    axisLine={false}
                                    tickLine={false}
                                    width={200}
                                    tick={{ fontSize: 11 }}
                                    interval={0}
                                />
                                <YAxis
                                    orientation="right"
                                    yAxisId={1}
                                    dataKey="count"
                                    type="category"
                                    axisLine={false}
                                    tickLine={false}
                                />
                                <Tooltip content={<CustomTooltip />} />
                                <Bar dataKey="count" minPointSize={2} barSize={25}>
                                    {data.map((_, idx) => (
                                        <Cell
                                            key={`cell-${idx}`}
                                            fill={getColor(idx)}
                                        />
                                    ))}
                                </Bar>
                            </BarChart>
                        </ResponsiveContainer>
                    </div>
                </div>
            ) : (
                <div className="h-64 flex items-center justify-center">
                    <Empty description="No hay datos de intereses disponibles" />
                </div>
            )}
        </Card>
    );
};

export default InterestsSegmentationChart;
