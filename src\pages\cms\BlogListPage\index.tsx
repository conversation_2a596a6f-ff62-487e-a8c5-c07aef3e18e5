import { useMemo, useState } from "react";
import { Link, useNavigate, useSearchParams } from "react-router-dom";
import {
    Button,
    Checkbox,
    ConfigProvider,
    Dropdown,
    Empty,
    message,
    notification,
    Pagination,
    Popover,
    Table,
    Tag,
    Typography,
} from "antd";
import { useMutation, useQuery } from "@tanstack/react-query";
import type { TableProps } from "antd";

const { Text } = Typography;

import WelcomeBar from "@components/shared/molecules/WelcomeBar";

import Trash from "@assets/icons/huge/trash-white.svg?react";
import Plus from "@assets/icons/huge/plus-white.svg?react";
import DeleteStroke from "@assets/icons/huge/delete-stroke.svg?react";
import EditStroke from "@assets/icons/huge/edit-stroke.svg?react";
import MoreVertical from "@assets/icons/huge/more-vertical.svg?react";
import Settings from "@assets/icons/huge/settings.svg?react";
import Reload from "@assets/icons/huge/reload.svg?react";

import {
    createBlogPost,
    deleteBlogPost,
    getBlogPosts,
    updateBlogPost,
} from "@services/portals/cms/blogs/post";
import { formatDateTime, getTextColor } from "@lib/helpers";
import { BlogPost, BlogStatus } from "@myTypes/blog";
import { onErrorMessage, onSuccessMessage } from "@lib/message";
import { AxiosError } from "axios";
import Spinner from "@components/shared/atoms/Spinner";
import CmsLayout from "@layouts/cms/CmsLayout";
import SearchText from "@components/shared/atoms/SearchText";
import { ArchiveIcon, FileEdit, Send } from "lucide-react";
import { DeleteConfirm } from "@components/shared/atoms/DeleteConfirm";
import { openErrorNotification } from "@lib/notification";
// import Filters from "@components/shared/atoms/Filter";
// import { filters, InstructorPanelFilterSchema } from "./filters";

const INITIAL_COLUMNS: TableProps<BlogPost>["columns"] = [
    {
        title: "TÍTULO",
        dataIndex: "title",
        key: "title",
        render: (title, record) => (
            <Link
                to={`${record.bid}/edit?status=${record.status}`}
                className="text-blue-full font-semibold underline"
            >
                {title ? title : "<Sin título>"}
            </Link>
        ),
    },
    {
        title: "SLUG",
        dataIndex: "slug",
        key: "slug",
    },
    {
        title: "CATEGORÍAS",
        dataIndex: "categories",
        key: "categories",
        render: (_, record) => {
            return record.categories?.length ? (
                record.categories.map((category) => (
                    <Tag key={`${record.bid}-${category.bcid}`} color="blue">
                        {category.name}
                    </Tag>
                ))
            ) : (
                <Tag color="gray">Sin categorías</Tag>
            );
        },
    },
    {
        title: "ETIQUETAS",
        dataIndex: "tags",
        key: "tags",
        render: (_, record) => {
            return record.tags?.length ? (
                record.tags.map((tag) => (
                    <Tag key={`${record.bid}-${tag.btid}`} color={tag.badgeColor}>
                        <span style={{ color: getTextColor(tag.badgeColor) }}>
                            {tag.name}
                        </span>
                    </Tag>
                ))
            ) : (
                <Tag color="gray">Sin etiquetas</Tag>
            );
        },
    },
    {
        title: "ESTADO",
        dataIndex: "status",
        key: "status",
        render: (status: string) => {
            return (
                <Tag
                    className="capitalize"
                    bordered={false}
                    color={
                        status === BlogStatus.DRAFT
                            ? "processing"
                            : status === BlogStatus.PUBLISHED
                              ? "success"
                              : "yellow" // Archived
                    }
                >
                    {status}
                </Tag>
            );
        },
    },
    {
        title: "DESTACADO",
        dataIndex: "featured",
        key: "featured",
    },
    {
        title: "FECHA DE ACTUALIZACIÓN",
        dataIndex: "updatedAt",
        key: "updatedAt",
        render: (updatedAt: string) => {
            const formattedDate = formatDateTime(updatedAt);
            return <Text>{formattedDate}</Text>;
        },
    },
    {
        title: "FECHA DE CREACIÓN",
        dataIndex: "createdAt",
        key: "createdAt",
        render: (createdAt: string) => {
            const formattedDate = formatDateTime(createdAt);
            return <Text>{formattedDate}</Text>;
        },
    },
];

const INITIAL_CHECKED_VALUES = ["title", "categories", "tags", "status", "createdAt"];

const COLUMN_OPTIONS = [
    {
        label: "Título",
        value: "title",
    },
    {
        label: "Slug",
        value: "slug",
    },
    {
        label: "Estado",
        value: "status",
    },
    {
        label: "Destacado",
        value: "featured",
    },
    {
        label: "Fecha de Actualización",
        value: "updatedAt",
    },
    {
        label: "Fecha de Creación",
        value: "createdAt",
    },
];

export default function BlogListPage() {
    const navigate = useNavigate();
    const [messageApi, messageContextHolder] = message.useMessage();
    const [notificationApi, notificationContextHolder] = notification.useNotification();
    const [searchParams, setSearchParams] = useSearchParams();

    const DEFAULT_STATUS = searchParams.get("status")
        ? searchParams.get("status")
        : BlogStatus.PUBLISHED;

    const DEFAULT_PAGE = searchParams.get("page")
        ? Number(searchParams.get("page"))
        : 1;

    const [currentStatus, setCurrentStatus] = useState<BlogStatus>(
        DEFAULT_STATUS as BlogStatus,
    );
    const [currentPage, setCurrentPage] = useState(DEFAULT_PAGE);
    const [checkedValues, setCheckedValues] = useState(INITIAL_CHECKED_VALUES);
    const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

    const { isLoading, data, refetch } = useQuery({
        queryKey: ["blogs", currentPage, searchParams.toString()],
        queryFn: async () =>
            getBlogPosts({
                page: currentPage,
                filters: {
                    ...Object.fromEntries(searchParams.entries()),
                    status: currentStatus,
                },
            }),
    });

    const updateStatusMutation = useMutation({
        mutationFn: ({ bid, status }: { bid: string; status: BlogStatus }) =>
            updateBlogPost(bid, { status }),
        onSuccess: (data) => {
            const currentStatus = data.status;
            const message =
                currentStatus === BlogStatus.PUBLISHED
                    ? "publicado"
                    : currentStatus === BlogStatus.DRAFT
                      ? "movido a borradores"
                      : "archivado";

            onSuccessMessage(`Blog ${message} exitosamente`, messageApi);
            refetch();
        },
        onError: (error: AxiosError) => {
            const errorMessages: string[] = Object.values(error.response?.data || {});
            openErrorNotification(
                "Error al publicar",
                errorMessages.length ? errorMessages : error.message,
                notificationApi,
            );
        },
    });

    const addMutation = useMutation({
        mutationFn: () => createBlogPost(),
        onSuccess: (data) => {
            onSuccessMessage("Nuevo draft creado exitosamente", messageApi);
            navigate(`/cms/blog/${data.bid}/edit?status=${BlogStatus.DRAFT}`, {
                relative: "path",
            });
            refetch();
        },
        onError: () => {
            onErrorMessage("Error al crear un nuevo borrador", messageApi);
        },
    });

    const deleteMutation = useMutation({
        mutationFn: (bid: string) => deleteBlogPost(bid),
        onSuccess: () => {
            onSuccessMessage("Blog eliminado exitosamente", messageApi);
            refetch();
        },
        onError: () => {
            onErrorMessage("Error al eliminar el blog", messageApi);
        },
    });

    const tableColumns = useMemo(() => {
        return (INITIAL_COLUMNS ?? []).filter((column) => {
            return checkedValues.includes(column.key as string);
        });
    }, [checkedValues]);

    const TOTAL_COUNT = data?.count;
    const PAGE_SIZE = 10;
    const instructors = data?.results;

    const defaultColumn = {
        title: "ACCIONES",
        key: "actions",
        render: (record: BlogPost) => (
            <Dropdown
                trigger={["click"]}
                menu={{
                    items: [
                        {
                            key:
                                record.status === BlogStatus.DRAFT
                                    ? "publish"
                                    : "draft",
                            label: (
                                <>
                                    <div className="flex items-center gap-2 text-blue-full">
                                        {record.status === BlogStatus.DRAFT ? (
                                            <>
                                                <Send className="w-5 h-5" /> Publicar
                                            </>
                                        ) : (
                                            <>
                                                <FileEdit className="w-5 h-5" /> Mover a
                                                borradores
                                            </>
                                        )}
                                    </div>
                                </>
                            ),
                        },
                        {
                            key: "edit",
                            label: (
                                <>
                                    <div className="flex items-center gap-2 text-blue-full">
                                        <EditStroke className="w-5 h-5" /> Editar
                                    </div>
                                </>
                            ),
                        },
                        {
                            key: "archive",
                            label: (
                                <>
                                    <div className="flex items-center gap-2 text-state-red-full">
                                        <ArchiveIcon className="w-5 h-5" /> Archivar
                                    </div>
                                </>
                            ),
                        },
                        {
                            key: "delete",
                            label: (
                                <>
                                    <DeleteConfirm
                                        title="Eliminar blog"
                                        description="¿Estás seguro de eliminar el blog?"
                                        onConfirm={() =>
                                            deleteMutation.mutateAsync(record.bid)
                                        }
                                        customTrigger={
                                            <div className="flex items-center gap-2 text-state-red-full">
                                                <DeleteStroke className="w-5 h-5" />{" "}
                                                Eliminar
                                            </div>
                                        }
                                    />
                                </>
                            ),
                        },
                    ],
                    onClick: ({ key }) => {
                        handleRowAction(key, record);
                    },
                }}
                placement="bottomRight"
            >
                <Button
                    icon={<MoreVertical className="w-5 h-5" />}
                    type="text"
                    size="small"
                />
            </Dropdown>
        ),
    };

    const handleRowAction = (key: string, record: BlogPost) => {
        if (key === "edit") {
            navigate(`/cms/blog/${record.bid}/edit`);
        } else if (key === "publish") {
            updateStatusMutation.mutate({
                bid: record.bid,
                status: BlogStatus.PUBLISHED,
            });
        } else if (key === "draft") {
            updateStatusMutation.mutate({
                bid: record.bid,
                status: BlogStatus.DRAFT,
            });
        } else if (key === "archive") {
            updateStatusMutation.mutate({
                bid: record.bid,
                status: BlogStatus.ARCHIVED,
            });
        }
    };

    const handleShowHideColumns = (checkedValues: string[]) => {
        setCheckedValues(checkedValues);
    };

    // Param filters
    const handleStatusChange = (status: BlogStatus) => {
        setCurrentStatus(status);
        setSearchParams({ status: status });
    };

    const handlePageChange = (newPage: number) => {
        setSearchParams({ page: newPage.toString() });
        setCurrentPage(newPage);
    };

    const handleReloadData = () => {
        refetch();
    };

    const handleOnSelectChange = (selectedRowKeys: React.Key[]) => {
        setSelectedRowKeys(selectedRowKeys);
    };

    const hasSelected = selectedRowKeys.length > 0;

    return (
        <>
            {messageContextHolder}
            {notificationContextHolder}
            <CmsLayout>
                <div className="w-full h-full space-y-5">
                    <header className="flex justify-between items-center">
                        <WelcomeBar helperText="Gestiona aquí los blogs que se visualizarán en la Website" />
                        <div className="flex gap-3">
                            <Button
                                type="primary"
                                size="large"
                                style={{ fontSize: 16 }}
                                icon={<Plus />}
                                onClick={() => {
                                    addMutation.mutate();
                                }}
                            >
                                Crear nuevo
                            </Button>
                        </div>
                    </header>

                    {/* Tabs */}
                    <div className="space-x-2 [&>button]:rounded-full [&>button]:p-4">
                        <Button
                            onClick={() => handleStatusChange(BlogStatus.PUBLISHED)}
                            type={
                                currentStatus === BlogStatus.PUBLISHED
                                    ? "primary"
                                    : "default"
                            }
                        >
                            Publicados
                        </Button>
                        <Button
                            onClick={() => handleStatusChange(BlogStatus.DRAFT)}
                            type={
                                currentStatus === BlogStatus.DRAFT
                                    ? "primary"
                                    : "default"
                            }
                        >
                            Borradores
                        </Button>
                        <Button
                            onClick={() => handleStatusChange(BlogStatus.ARCHIVED)}
                            type={
                                currentStatus === BlogStatus.ARCHIVED
                                    ? "primary"
                                    : "default"
                            }
                        >
                            Archivados
                        </Button>
                    </div>

                    <section className="p-5 bg-white-full rounded-lg space-y-5">
                        <div className="flex justify-between items-center">
                            <Text className="text-black-medium text-2xl font-semibold">
                                Blogs
                            </Text>

                            <div className="flex items-center gap-3">
                                {/* Table actions bar */}
                                <SearchText />
                                <Button
                                    icon={<Reload />}
                                    size="large"
                                    type="text"
                                    onClick={handleReloadData}
                                />
                                {/* <Filters<InstructorPanelFilterSchema>
                                    filters={filters}
                                    initialValues={{
                                        ...Object.fromEntries(searchParams.entries()),
                                        createdAt: [
                                            dayjsUtils.format(
                                                searchParams.get("createdAtAfter") ??
                                                    "",
                                            ),
                                            dayjsUtils.format(
                                                searchParams.get("createdAtBefore") ??
                                                    "",
                                            ),
                                        ],
                                    }}
                                /> */}
                                <Popover
                                    content={
                                        <div className="p-2 space-y-3">
                                            <div className="uppercase text-black-medium font-medium">
                                                Mostrar/Ocultar Columnas
                                            </div>
                                            <div className="px-2">
                                                <Checkbox.Group
                                                    defaultValue={
                                                        INITIAL_CHECKED_VALUES
                                                    }
                                                    onChange={handleShowHideColumns}
                                                    name="columns"
                                                    className="flex flex-col gap-1"
                                                    options={COLUMN_OPTIONS}
                                                />
                                            </div>
                                        </div>
                                    }
                                    trigger={["click"]}
                                    placement="bottomRight"
                                >
                                    <Button
                                        icon={<Settings />}
                                        size="large"
                                        type="text"
                                    />
                                </Popover>
                            </div>
                        </div>
                        <ConfigProvider
                            theme={{
                                components: {
                                    Table: {
                                        headerBg: "#FBFCFD",
                                        borderColor: "#fff",
                                        headerSplitColor: "#fafafa",
                                        headerBorderRadius: 8,
                                        rowHoverBg: "#F6FAFD",
                                        rowSelectedBg: "#F6FAFD",
                                        rowSelectedHoverBg: "#F6FAFD",
                                        footerBg: "#F1F1F1",
                                    },
                                },
                            }}
                        >
                            <Table
                                rowSelection={{
                                    type: "checkbox",
                                    onChange: handleOnSelectChange,
                                    selectedRowKeys,
                                }}
                                columns={
                                    tableColumns ? [...tableColumns, defaultColumn] : []
                                }
                                locale={{
                                    emptyText: (
                                        <>{isLoading ? <Spinner /> : <Empty />}</>
                                    ),
                                }}
                                dataSource={instructors}
                                className="rounded-lg"
                                footer={() => ""}
                                pagination={false}
                            />
                            <div className="flex justify-between">
                                <div className="flex items-center gap-3">
                                    <Button
                                        danger
                                        disabled={!hasSelected}
                                        type="primary"
                                        size="large"
                                        icon={<Trash />}
                                        // onClick={handleMultiDelete}
                                    >
                                        Eliminar
                                    </Button>
                                </div>
                                <div>
                                    <Pagination
                                        defaultCurrent={DEFAULT_PAGE}
                                        total={TOTAL_COUNT}
                                        pageSize={PAGE_SIZE}
                                        onChange={handlePageChange}
                                    />
                                </div>
                            </div>
                        </ConfigProvider>
                    </section>
                </div>
            </CmsLayout>
        </>
    );
}
