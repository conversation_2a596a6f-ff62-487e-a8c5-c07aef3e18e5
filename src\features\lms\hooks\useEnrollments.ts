import { useQuery } from "@tanstack/react-query";
import { getEnrollments } from "../services/portals/enrollment";
import { EnrollmentQueryParams } from "../types/enrollment";

export const useEnrollments = (params: EnrollmentQueryParams = {}) => {
    const { data, isLoading, isError, refetch } = useQuery({
        queryKey: ["enrollments", params],
        queryFn: () => getEnrollments(params),
        refetchOnWindowFocus: false,
    });

    const { count, results: enrollments } = data || {
        count: 0,
        results: [],
    };

    return {
        isLoading,
        isError,
        enrollments,
        count,
        refetch,
    };
};
