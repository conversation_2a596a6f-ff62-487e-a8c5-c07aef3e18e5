import { useState } from "react";
import { AxiosError } from "axios";
import { useNavigate } from "react-router-dom";
import { Button, Input, Popconfirm } from "antd";
import { Offering, OfferingObjective } from "@myTypes/offering";
import { MessageInstance } from "antd/es/message/interface";
import { NotificationInstance } from "antd/es/notification/interface";

import { MinusCircle, PlusCircle } from "lucide-react";
import { useMutation } from "@tanstack/react-query";
import { deleteOffering, updateOffering } from "@services/portals/cms/offering";

import Save from "@assets/icons/general/save-stroke.svg?react";
import Trash from "@assets/icons/huge/trash-white.svg?react";

const { TextArea } = Input;

type ObjectivesOfferingEditProps = {
    data: Offering;
    oid: string;
    messageApi: MessageInstance;
    notificationApi: NotificationInstance;
    handleRefetch: () => void;
};

const ObjectivesOfferingEdit: React.FC<ObjectivesOfferingEditProps> = ({
    oid,
    data,
    messageApi,
    handleRefetch,
}) => {
    const navigate = useNavigate();
    const { objectives: initialObjectives } = data;
    const [objectives, setObjectives] = useState<OfferingObjective[]>([
        ...initialObjectives,
    ]);

    const handleAddObjective = () => {
        setObjectives([
            ...objectives,
            { title: "", description: "" } as OfferingObjective,
        ]);
    };

    const handleRemoveObjective = (index: number) => {
        const newObjectives = objectives.filter((_, idx) => idx !== index);
        setObjectives(newObjectives);
    };

    const handleObjectiveChange = (
        index: number,
        field: keyof OfferingObjective,
        value: string,
    ) => {
        const newObjectives = objectives.map((obj, idx) =>
            idx === index ? { ...obj, [field]: value } : obj,
        );
        setObjectives(newObjectives);
    };

    const { mutate, isPending } = useMutation({
        mutationFn: () => updateOffering(oid, { objectives }),
        onSuccess: () => {
            messageApi.success("Objetivos actualizados correctamente");
            handleRefetch();
        },
        onError: () => {
            messageApi.error("Error al actualizar los objetivos");
        },
    });

    const { mutate: deleteMutate, isError } = useMutation({
        mutationFn: () => deleteOffering(oid as string),
        onSuccess: () => {
            navigate("/cms/offering", { replace: true });
        },
        onError: (error: AxiosError) => {
            console.error(error);
        },
    });

    const handleSave = async () => {
        mutate();
    };

    return (
        <div className="grid grid-cols-1 lg:grid-cols-6 gap-y-6 lg:gap-6">
            <div className="bg-white-full col-span-1 lg:col-span-4 p-5 rounded-lg shadow-sm">
                <div className="flex justify-between items-center mb-6">
                    <p className="text-gray-400 font-semibold text-sm">OBJETIVOS</p>
                </div>

                <div className="grid grid-cols-12 gap-4 mb-4 px-4">
                    <div className="col-span-12 md:col-span-4 text-gray-600 font-medium">
                        Título
                    </div>
                    <div className="col-span-12 md:col-span-8 text-gray-600 font-medium">
                        Descripción
                    </div>
                </div>

                <div className="space-y-4">
                    {objectives.map((objective, idx) => (
                        <div
                            key={idx}
                            className="grid grid-cols-12 gap-4 items-start rounded-lg"
                        >
                            <div className="col-span-12 md:col-span-4">
                                <Input
                                    value={objective.title}
                                    onChange={(e) =>
                                        handleObjectiveChange(
                                            idx,
                                            "title",
                                            e.target.value,
                                        )
                                    }
                                    placeholder="Título del objetivo"
                                    className="w-full"
                                />
                            </div>
                            <div className="col-span-11 md:col-span-7">
                                <TextArea
                                    value={objective.description}
                                    onChange={(e) =>
                                        handleObjectiveChange(
                                            idx,
                                            "description",
                                            e.target.value,
                                        )
                                    }
                                    placeholder="Descripción del objetivo"
                                    autoSize={{ minRows: 2, maxRows: 4 }}
                                    className="w-full"
                                />
                            </div>
                            <div className="col-span-1">
                                <Popconfirm
                                    title="¿Eliminar objetivo?"
                                    description="Esta acción no se puede deshacer"
                                    onConfirm={() => handleRemoveObjective(idx)}
                                    okText="Sí"
                                    cancelText="No"
                                >
                                    <Button
                                        type="text"
                                        icon={
                                            <MinusCircle
                                                size={16}
                                                className="text-red-500"
                                            />
                                        }
                                    />
                                </Popconfirm>
                            </div>
                        </div>
                    ))}
                </div>

                {objectives.length === 0 && (
                    <div className="text-center text-gray-400 py-8">
                        No hay objetivos definidos. Haga clic en "Agregar" para
                        comenzar.
                    </div>
                )}
                <div className="flex justify-end mt-4">
                    <Button
                        type="dashed"
                        onClick={handleAddObjective}
                        icon={<PlusCircle size={16} />}
                    >
                        Agregar objetivo
                    </Button>
                </div>
            </div>

            <div className="col-span-1 lg:col-span-2 space-y-6">
                <div className="bg-white-full p-5 rounded-lg shadow-sm">
                    <p className="text-gray-400 font-semibold text-sm">ACCIONES</p>
                    <div className="flex justify-end gap-3">
                        <Button
                            type="primary"
                            size="large"
                            style={{ fontSize: 16 }}
                            icon={<Trash />}
                            danger
                            disabled={isError}
                            onClick={() => deleteMutate()}
                        >
                            Eliminar
                        </Button>
                        <Button
                            type="primary"
                            size="large"
                            style={{ fontSize: 16 }}
                            icon={<Save />}
                            disabled={isPending}
                            onClick={handleSave}
                        >
                            Guardar
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ObjectivesOfferingEdit;
