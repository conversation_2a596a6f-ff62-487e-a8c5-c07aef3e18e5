import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Drawer, Radio, Select, Space, Tag } from "antd";
import {
    OrderStage,
    OrderCurrency,
    OrderStageLabels,
    OrderCurrencyLabels,
} from "@/features/crm/types/order";
import { useSearchParams } from "react-router-dom";
import { useCallback, useEffect, useState } from "react";
import dayjs, { Dayjs } from "dayjs";
import SelectOfferings from "../molecules/select-offerings";
import "dayjs/locale/es";
import SelectStaffUser from "../molecules/select-staff-user";

dayjs.locale("es");

const getStageColor = (stage: OrderStage) => {
    switch (stage) {
        case OrderStage.PROSPECT:
            return "blue";
        case OrderStage.INTERESTED:
            return "green";
        case OrderStage.TO_PAY:
            return "orange";
        case OrderStage.SOLD:
            return "purple";
        case OrderStage.LOST:
            return "red";
        default:
            return "default";
    }
};

// Opciones para filtro de fecha
const dateFilterOptions = [
    { value: "createdAt", label: "Fecha de creación" },
    { value: "prospectAt", label: "Fecha de prospecto" },
    { value: "interestedAt", label: "Fecha de interés" },
    { value: "toPayAt", label: "Fecha de por pagar" },
    { value: "soldAt", label: "Fecha de vendido" },
    { value: "lostAt", label: "Fecha de perdido" },
];

interface OrdersFiltersProps {
    isOpen: boolean;
    onClose: () => void;
}

export default function OrdersFilters({ isOpen, onClose }: OrdersFiltersProps) {
    const [searchParams, setSearchParams] = useSearchParams();

    // Local filter states
    const [selectedStages, setSelectedStages] = useState<OrderStage[]>([]);
    const [selectedCurrency, setSelectedCurrency] = useState<string>("all");
    const [selectedOfferings, setSelectedOfferings] = useState<string[]>([]);
    const [selectedSalesAgent, setSelectedSalesAgent] = useState<string>("");
    const [dateRange, setDateRange] = useState<[Dayjs, Dayjs] | null>(null);
    const [selectedDateFilters, setSelectedDateFilters] = useState<string[]>([
        "createdAt",
    ]);

    // Load offerings
    // const { offerings, isLoading: isLoadingOfferings } = useOfferings();

    // Initialize filters from URL
    useEffect(() => {
        const stages = searchParams.get("stages");
        const currency = searchParams.get("currency") || "all";
        const offeringsParam = searchParams.get("offerings");
        const startDate = searchParams.get("startDate");
        const endDate = searchParams.get("endDate");
        const dateFilters = searchParams.get("filterDateBy");
        const salesAgent = searchParams.get("salesAgent");

        if (stages) {
            setSelectedStages(stages.split(",") as OrderStage[]);
        } else {
            setSelectedStages(Object.values(OrderStage));
        }

        setSelectedCurrency(currency);

        if (offeringsParam) {
            setSelectedOfferings(offeringsParam.split(","));
        }

        if (startDate && endDate) {
            setDateRange([dayjs(startDate), dayjs(endDate)]);
        }

        if (dateFilters) {
            setSelectedDateFilters(dateFilters.split(","));
        } else {
            setSelectedDateFilters(["createdAt"]);
        }

        if (salesAgent) {
            setSelectedSalesAgent(salesAgent);
        } else {
            setSelectedSalesAgent("");
        }
    }, [searchParams]);

    const handleStageChange = useCallback((stages: OrderStage[]) => {
        setSelectedStages(stages);
    }, []);

    const handleApplyFilters = useCallback(() => {
        setSearchParams((prev) => {
            // Clear previous filters
            prev.delete("stages");
            prev.delete("currency");
            prev.delete("isInternational");
            prev.delete("offerings");
            prev.delete("startDate");
            prev.delete("endDate");
            prev.delete("filterDateBy");
            prev.delete("salesAgent");

            // Apply stage filters (como string separado por comas)
            if (
                selectedStages.length > 0 &&
                selectedStages.length < Object.values(OrderStage).length
            ) {
                prev.set("stages", selectedStages.join(","));
            }

            // Apply currency filter - mantener currency para UI y añadir isInternational para backend
            if (selectedCurrency !== "all") {
                prev.set("currency", selectedCurrency);

                if (selectedCurrency === "usd") {
                    prev.set("isInternational", "true");
                } else if (selectedCurrency === "pen") {
                    prev.set("isInternational", "false");
                }
            }

            if (selectedOfferings.length > 0) {
                prev.set("offerings", selectedOfferings.join(","));
            }

            if (dateRange && dateRange[0] && dateRange[1]) {
                prev.set("startDate", dateRange[0].format("YYYY-MM-DD"));
                prev.set("endDate", dateRange[1].format("YYYY-MM-DD"));

                // Add date filter types
                if (selectedDateFilters.length > 0) {
                    prev.set("filterDateBy", selectedDateFilters.join(","));
                }
            }

            if (selectedSalesAgent) {
                prev.set("salesAgent", selectedSalesAgent);
            }

            // Reset to page 1
            prev.set("page", "1");

            return prev;
        });
        onClose();
    }, [
        selectedStages,
        selectedCurrency,
        selectedOfferings,
        dateRange,
        selectedDateFilters,
        selectedSalesAgent,
        setSearchParams,
        onClose,
    ]);

    const handleClearFilters = useCallback(() => {
        setSelectedStages(Object.values(OrderStage));
        setSelectedCurrency("all");
        setSelectedOfferings([]);
        setDateRange(null);
        setSelectedDateFilters(["createdAt"]);
        setSelectedSalesAgent("");

        setSearchParams((prev) => {
            prev.delete("stages");
            prev.delete("currency");
            prev.delete("isInternational");
            prev.delete("offerings");
            prev.delete("startDate");
            prev.delete("endDate");
            prev.delete("filterDateBy");
            prev.delete("salesAgent");
            prev.set("page", "1");
            return prev;
        });
        onClose();
    }, [setSearchParams, onClose]);

    return (
        <Drawer
            title="Aplicar filtros"
            placement="right"
            closable={true}
            onClose={onClose}
            open={isOpen}
            width={480}
        >
            <div className="space-y-4">
                <div>
                    <h4 className="font-medium mb-2">Etapas</h4>
                    <Select
                        mode="multiple"
                        style={{ width: "100%" }}
                        placeholder="Seleccionar etapas"
                        value={selectedStages}
                        onChange={handleStageChange}
                        allowClear
                        showSearch={false}
                    >
                        {Object.values(OrderStage).map((stage) => (
                            <Select.Option key={stage} value={stage}>
                                <div className="flex items-center gap-2">
                                    <Tag
                                        color={getStageColor(stage)}
                                        style={{ margin: 0 }}
                                    >
                                        {OrderStageLabels[stage]}
                                    </Tag>
                                </div>
                            </Select.Option>
                        ))}
                    </Select>
                </div>

                <div>
                    <h4 className="font-medium mb-2">Moneda</h4>
                    <Radio.Group
                        value={selectedCurrency}
                        onChange={(e) => setSelectedCurrency(e.target.value)}
                    >
                        <Space direction="vertical">
                            <Radio value="all">Todas</Radio>
                            <Radio value={OrderCurrency.USD}>
                                {OrderCurrencyLabels[OrderCurrency.USD]} (USD)
                            </Radio>
                            <Radio value={OrderCurrency.PEN}>
                                {OrderCurrencyLabels[OrderCurrency.PEN]} (PEN)
                            </Radio>
                        </Space>
                    </Radio.Group>
                </div>

                <div>
                    <h4 className="font-medium mb-2">Programas</h4>
                    <SelectOfferings
                        mode="multiple"
                        className="w-full"
                        onChange={(value) =>
                            setSelectedOfferings(
                                Array.isArray(value) ? value : value ? [value] : [],
                            )
                        }
                        value={selectedOfferings}
                    />
                </div>

                <div>
                    <h4 className="font-medium mb-2">Rango de fecha</h4>
                    <div className="space-y-3">
                        <div>
                            <label className="text-sm text-gray-600 mb-1 block">
                                Filtrar por tipo de fecha
                            </label>
                            <Select
                                mode="multiple"
                                style={{ width: "100%" }}
                                placeholder="Seleccionar tipos de fecha"
                                value={selectedDateFilters}
                                onChange={setSelectedDateFilters}
                                options={dateFilterOptions}
                                allowClear={false}
                            />
                        </div>
                        <div>
                            <label className="text-sm text-gray-600 mb-1 block">
                                Seleccionar rango
                            </label>
                            <DatePicker.RangePicker
                                style={{ width: "100%" }}
                                placeholder={["Fecha inicio", "Fecha fin"]}
                                value={dateRange}
                                onChange={(dates) =>
                                    setDateRange(dates as [Dayjs, Dayjs] | null)
                                }
                                presets={[
                                    {
                                        label: "Hoy",
                                        value: [
                                            dayjs().startOf("day"),
                                            dayjs().endOf("day"),
                                        ],
                                    },
                                    {
                                        label: "Esta semana",
                                        value: [
                                            dayjs().startOf("week"),
                                            dayjs().endOf("week"),
                                        ],
                                    },
                                    {
                                        label: "Este mes",
                                        value: [
                                            dayjs().startOf("month"),
                                            dayjs().endOf("month"),
                                        ],
                                    },
                                    {
                                        label: "Último mes",
                                        value: [
                                            dayjs()
                                                .subtract(1, "month")
                                                .startOf("month"),
                                            dayjs().subtract(1, "month").endOf("month"),
                                        ],
                                    },
                                    {
                                        label: "Este año",
                                        value: [
                                            dayjs().startOf("year"),
                                            dayjs().endOf("year"),
                                        ],
                                    },
                                ]}
                            />
                        </div>
                    </div>
                </div>
                <div>
                    <h4 className="font-medium mb-2">Agente de ventas</h4>
                    <div className="space-y-3">
                        <SelectStaffUser
                            className="w-full"
                            placeholder="Seleccionar agente"
                            onChange={(value) => setSelectedSalesAgent(value)}
                            value={selectedSalesAgent}
                        />
                    </div>
                </div>

                <div className="pt-4 space-y-2">
                    <Button type="primary" block onClick={handleApplyFilters}>
                        Aplicar filtros
                    </Button>
                    <Button block onClick={handleClearFilters}>
                        Limpiar filtros
                    </Button>
                </div>
            </div>
        </Drawer>
    );
}
