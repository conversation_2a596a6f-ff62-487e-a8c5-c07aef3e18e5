import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";
import { validateTokenWithServer, performLogout } from "@/core/auth/tokenValidator";

type Group = "marketing" | "commercial" | "development" | "management";

export type AuthUser = {
    uid: string;
    username: string;
    email: string;
    firstName: string;
    lastName: string;
    groups: Group[] | undefined;
    permissions: string[] | undefined;
};

export type AuthState = {
    key?: string;
    user?: AuthUser;
    isAuthenticated?: boolean;
    isHydrated: boolean;
};

type AuthActions = {
    setToken: (token: string) => void;
    login: ({
        key,
        user,
        isAuthenticated,
    }: {
        key: string;
        user: AuthUser;
        isAuthenticated: boolean;
    }) => void;
    logout: () => void;
    setIsHydrated: (isHydrated: boolean) => void;
    validateToken: () => Promise<void>;
};

type AuthStore = AuthState & AuthActions;

const initialState: AuthState = {
    key: undefined,
    user: undefined,
    isAuthenticated: false,
    isHydrated: false,
};

export const useAuthStore = create<AuthStore>()(
    persist(
        (set, get) => ({
            ...initialState,
            setToken: (token) => {
                set((state) => ({ ...state, key: token }));
            },
            setIsHydrated: (isHydrated) => {
                set((state) => ({ ...state, isHydrated }));
            },
            login: ({ key, user, isAuthenticated }) => {
                set((state) => ({ ...state, key, user, isAuthenticated }));
            },
            logout: async () => {
                try {
                    await performLogout();
                } catch (error) {
                    console.error("Error during logout:", error);
                } finally {
                    set((state) => ({
                        ...state,
                        key: undefined,
                        user: undefined,
                        isAuthenticated: false,
                    }));
                }
            },
            validateToken: async () => {
                const state = get();

                try {
                    const result = await validateTokenWithServer(state);

                    if (result.action === "logout") {
                        // Force logout due to security concerns
                        await performLogout();
                        set((currentState) => ({
                            ...currentState,
                            key: undefined,
                            user: undefined,
                            isAuthenticated: false,
                        }));
                    }
                    // If action is 'keep', do nothing - current state is valid
                } catch (error) {
                    console.error("Token validation error:", error);
                    // On any error, force logout for security
                    set((state) => ({
                        ...state,
                        key: undefined,
                        user: undefined,
                        isAuthenticated: false,
                    }));
                }
            },
        }),
        {
            name: "authStore",
            storage: createJSONStorage(() => localStorage),
            onRehydrateStorage: () => (state) => {
                try {
                    state?.validateToken();
                } catch (error) {
                    console.error(
                        "Error during token validation on rehydration:",
                        error,
                    );
                }
                state?.setIsHydrated(true);
            },
        },
    ),
);
