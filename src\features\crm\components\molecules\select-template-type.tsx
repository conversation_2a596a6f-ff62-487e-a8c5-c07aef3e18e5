import { useDebounce } from "@hooks/use-debounce";
import { Select, type SelectProps } from "antd";
import { useState } from "react";
import { UseTemplateTypeQuery, useTemplateTypes } from "../../hooks/use-template-type";
import { TemplateType } from "../../types/template-type";

interface SelectTemplateTypeProps extends Omit<SelectProps, "options"> {
    value?: string | string[]; // Add value prop for controlled component
    onChange?: (value: string | string[]) => void; // Add onChange prop
    selectedTemplateType?: TemplateType;
}

export default function SelectTemplateTypes({
    value,
    onChange,
    selectedTemplateType,
    ...restProps
}: SelectTemplateTypeProps) {
    const [query, setQuery] = useState<UseTemplateTypeQuery | null>(null);

    const debouncedQuery = useDebounce(query, 1000);

    const { templateTypes, isLoading } = useTemplateTypes({
        query: {
            search: debouncedQuery?.search,
        },
    });

    const templateTypesOptions: SelectProps["options"] = templateTypes?.map(
        (templateType) => ({
            value: templateType.ttid,
            label: templateType.name,
            data: {
                info: templateType,
            },
        }),
    );

    const options = [...(templateTypesOptions || [])];

    // Agregar valores seleccionados que no están en las opciones actuales
    if (value && selectedTemplateType) {
        const valuesToCheck = Array.isArray(value) ? value : [value];

        valuesToCheck.forEach((selectedValue) => {
            const isValueInOptions = templateTypesOptions?.some(
                (option) => option.value === selectedValue,
            );

            if (!isValueInOptions) {
                // Verificar que no hayamos agregado ya esta opción
                const alreadyAdded = options.some(
                    (option) => option.value === selectedValue,
                );

                if (!alreadyAdded) {
                    options.unshift({
                        value: selectedValue,
                        label: selectedTemplateType.name,
                        data: {
                            info: selectedTemplateType,
                        },
                    });
                }
            }
        });
    }

    const handleSearch = (value: string) => {
        setQuery({ search: value });
    };

    return (
        <>
            <Select
                {...restProps}
                value={value}
                onChange={onChange}
                onSearch={handleSearch}
                options={options}
                filterOption={false}
                loading={isLoading}
                showSearch
                allowClear
            />
        </>
    );
}
