import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Badge } from "antd";
import { Plus } from "lucide-react";
import { usePayments } from "../../hooks/use-payment";
import { ListPaymentsQueryParams } from "../../types/payment";
import PaymentsTable from "./payments-table";
import CreatePaymentFormForContact from "./create-payment-form-for-contact";

const { Text } = Typography;

type ContactPaymentsTabProps = {
    contactId: string;
};

export default function ContactPaymentsTab({ contactId }: ContactPaymentsTabProps) {
    const [isCreatePaymentModalOpen, setIsCreatePaymentModalOpen] =
        useState<boolean>(false);

    const queryParams: ListPaymentsQueryParams = {
        owner: contactId,
    };
    const { payments } = usePayments({ queryParams });

    const handleCreatePaymentModalOpen = () => {
        setIsCreatePaymentModalOpen(true);
    };

    const handleCreatePaymentModalClose = () => {
        setIsCreatePaymentModalOpen(false);
        // List will refresh automatically thanks to query invalidation in useCreatePayment
    };

    return (
        <div className="space-y-4">
            {/* Header with add payment button */}
            <div className="flex justify-between items-center">
                <Text className="text-black-medium text-xl font-semibold">
                    Pagos <Badge count={payments.length} color="blue" size="default" />
                </Text>
                <Button
                    type="primary"
                    size="large"
                    icon={<Plus />}
                    onClick={handleCreatePaymentModalOpen}
                >
                    Agregar Pago
                </Button>
            </div>

            {/* Modal to create payment */}
            <Modal
                centered
                open={isCreatePaymentModalOpen}
                onCancel={handleCreatePaymentModalClose}
                footer={false}
                title="Agregar/Programar nuevo pago"
                width={600}
            >
                <CreatePaymentFormForContact
                    onFinish={handleCreatePaymentModalClose}
                    contactId={contactId}
                />
            </Modal>

            {/* Payments table */}
            <PaymentsTable initialData={payments} />
        </div>
    );
}
