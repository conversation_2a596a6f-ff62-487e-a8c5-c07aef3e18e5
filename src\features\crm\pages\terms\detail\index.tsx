import CrmLayout from "@/features/crm/layout";
import { usePara<PERSON>, <PERSON> } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { retrieveTerm } from "@/features/crm/services/portals/term";
import { Breadcrumb, Card, Descriptions, Typography } from "antd";

const { Title } = Typography;

export default function TermDetailPage() {
    const { tid } = useParams();
    const id = tid as string;

    const { data: term, isLoading } = useQuery({
        queryKey: ["term", id],
        queryFn: () => retrieveTerm(id),
        enabled: !!id,
    });

    return (
        <CrmLayout>
            <div className="space-y-4">
                <Breadcrumb
                    items={[
                        { title: <Link to="/crm/terms">Ciclos</Link> },
                        { title: term?.name || "Detalle" },
                    ]}
                />
                <Title level={3}>Ciclo académico</Title>
                <Card loading={isLoading}>
                    {term && (
                        <Descriptions bordered column={1} size="middle">
                            <Descriptions.Item label="ID">{term.tid}</Descriptions.Item>
                            <Descriptions.Item label="Nombre">
                                {term.name}
                            </Descriptions.Item>
                        </Descriptions>
                    )}
                </Card>
            </div>
        </CrmLayout>
    );
}
