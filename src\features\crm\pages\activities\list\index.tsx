import CreateActivityForm from "@/features/crm/components/organisms/create-activity-form";
import ActivitiesTable from "@/features/crm/components/organisms/activities-table";
import { useActivities } from "@/features/crm/hooks/use-activity";
import CrmLayout from "@/features/crm/layout";
import {
    ActivityQueryParams,
    ActivityStatus,
    ACTIVITY_STATUS_CHOICES,
} from "@/features/crm/types/activity";
import WelcomeBar from "@components/shared/molecules/WelcomeBar";
import {
    Badge,
    Button,
    Drawer,
    Input,
    Modal,
    Pagination,
    Typography,
    DatePicker,
    Radio,
    Space,
} from "antd";
import { Plus, SlidersHorizontal, Calendar } from "lucide-react";
import { useState } from "react";
import { useSearchParams } from "react-router-dom";
import dayjs from "dayjs";
import SelectStaffUser from "@/features/crm/components/molecules/select-staff-user";

const { Text } = Typography;
const { Search } = Input;

const DEFAULT_PAGE = 1;
const DEFAULT_PAGE_SIZE = 20;

const STATUS_FILTER_TAGS = [
    {
        value: undefined,
        label: "Todos",
    },
    ...ACTIVITY_STATUS_CHOICES.map((choice) => ({
        value: choice.value,
        label: choice.label,
    })),
];

export default function ActivitiesListPage() {
    const [searchParams, setSearchParams] = useSearchParams();
    const status = searchParams.get("status") as ActivityStatus | null;
    const page = Number(searchParams.get("page")) || DEFAULT_PAGE;
    const pageSize = Number(searchParams.get("pageSize")) || DEFAULT_PAGE_SIZE;
    const search = searchParams.get("search") || "";
    const responsible = searchParams.get("responsible") || "";
    const order = searchParams.get("order") || "";
    const deadlineFrom = searchParams.get("deadlineFrom") || "";
    const deadlineTo = searchParams.get("deadlineTo") || "";
    const deadlineDate = searchParams.get("deadlineDate") || "";

    const queryParams: ActivityQueryParams = {
        status: status || undefined,
        page,
        pageSize,
        search: search || undefined,
        responsible: responsible || undefined,
        order: order || undefined,
        deadlineFrom: deadlineFrom || undefined,
        deadlineTo: deadlineTo || undefined,
        deadlineDate: deadlineDate || undefined,
    };

    const { activities, count } = useActivities({ queryParams });

    const [isFilterDrawerOpen, setIsFilterDrawerOpen] = useState<boolean>(false);
    const [isCreateActivityModalOpen, setIsCreateActivityModalOpen] =
        useState<boolean>(false);
    const [filterResponsible, setFilterResponsible] = useState<string>(responsible);
    const [filterOrder, setFilterOrder] = useState<string>(order);
    const [dateFilterType, setDateFilterType] = useState<"range" | "specific">("range");
    const [filterDeadlineFrom, setFilterDeadlineFrom] = useState<string>(deadlineFrom);
    const [filterDeadlineTo, setFilterDeadlineTo] = useState<string>(deadlineTo);
    const [filterDeadlineDate, setFilterDeadlineDate] = useState<string>(deadlineDate);

    const handleSetSearchQuery = (value: string) => {
        setSearchParams((prev) => {
            if (value) {
                prev.set("search", value);
            } else {
                prev.delete("search");
            }
            prev.set("page", "1"); // Reset to first page
            return prev;
        });
    };

    const handleCreateActivityModalOpen = () => {
        setIsCreateActivityModalOpen(true);
    };

    const handleCreateActivityModalClose = () => {
        setIsCreateActivityModalOpen(false);
    };

    const setStatusFilter = (status: ActivityStatus | undefined) => {
        setSearchParams((prev) => {
            if (status) {
                prev.set("status", status);
            } else {
                prev.delete("status");
            }
            prev.set("page", "1"); // Reset to first page
            return prev;
        });
    };

    const handleSetPage = (page: number, pageSize: number) => {
        setSearchParams((prev) => {
            prev.set("page", String(page));
            prev.set("pageSize", String(pageSize));
            return prev;
        });
    };

    const applyFilters = () => {
        setSearchParams((prev) => {
            if (filterResponsible) {
                prev.set("responsible", filterResponsible);
            } else {
                prev.delete("responsible");
            }
            if (filterOrder) {
                prev.set("order", filterOrder);
            } else {
                prev.delete("order");
            }

            // Handle date filters based on filter type
            if (dateFilterType === "specific") {
                if (filterDeadlineDate) {
                    prev.set("deadlineDate", filterDeadlineDate);
                } else {
                    prev.delete("deadlineDate");
                }
                // Clear range filters when using specific date
                prev.delete("deadlineFrom");
                prev.delete("deadlineTo");
            } else {
                // Handle range filters
                if (filterDeadlineFrom) {
                    prev.set("deadlineFrom", filterDeadlineFrom);
                } else {
                    prev.delete("deadlineFrom");
                }
                if (filterDeadlineTo) {
                    prev.set("deadlineTo", filterDeadlineTo);
                } else {
                    prev.delete("deadlineTo");
                }
                // Clear specific date filter when using range
                prev.delete("deadlineDate");
            }

            prev.set("page", "1"); // Reset to first page
            return prev;
        });
        setIsFilterDrawerOpen(false);
    };

    const clearFilters = () => {
        setFilterResponsible("");
        setFilterOrder("");
        setFilterDeadlineFrom("");
        setFilterDeadlineTo("");
        setFilterDeadlineDate("");
        setDateFilterType("range");
        setSearchParams((prev) => {
            prev.delete("responsible");
            prev.delete("order");
            prev.delete("status");
            prev.delete("search");
            prev.delete("deadlineFrom");
            prev.delete("deadlineTo");
            prev.delete("deadlineDate");
            prev.set("page", "1");
            return prev;
        });
        setIsFilterDrawerOpen(false);
    };

    return (
        <CrmLayout>
            <div className="w-full h-full space-y-5">
                <div className="flex justify-between items-center">
                    <WelcomeBar helperText="Gestiona aquí las actividades y tareas del equipo" />
                    <div className="flex gap-3">
                        <Button
                            type="primary"
                            size="large"
                            style={{ fontSize: 16 }}
                            icon={<Plus />}
                            onClick={handleCreateActivityModalOpen}
                        >
                            Agregar
                        </Button>
                        <Modal
                            centered
                            open={isCreateActivityModalOpen}
                            onCancel={handleCreateActivityModalClose}
                            footer={false}
                            title="Agregar nueva actividad"
                            width={800}
                        >
                            <CreateActivityForm
                                onFinish={handleCreateActivityModalClose}
                            />
                        </Modal>
                    </div>
                </div>

                <div className="p-5 bg-white-full rounded-lg space-y-5">
                    <div className="flex flex-col lg:flex-row justify-between items-center">
                        <Text className="text-black-medium text-2xl font-semibold">
                            Actividades{" "}
                            <Badge count={count} color="blue" size="default" />
                        </Text>
                        <div className="flex items-center gap-3">
                            <Search
                                size="large"
                                placeholder="Buscar por título o descripción"
                                onSearch={handleSetSearchQuery}
                                enterButton
                                allowClear
                                className="max-w-screen-sm"
                                defaultValue={search}
                            />
                            <div className="relative">
                                <Button
                                    icon={<SlidersHorizontal size={16} />}
                                    onClick={() => setIsFilterDrawerOpen(true)}
                                >
                                    Filtros
                                </Button>
                                {/* Active filters indicator */}
                                {(responsible ||
                                    order ||
                                    deadlineFrom ||
                                    deadlineTo ||
                                    deadlineDate) && (
                                    <Badge
                                        count={
                                            [
                                                responsible,
                                                order,
                                                deadlineFrom ||
                                                    deadlineTo ||
                                                    deadlineDate,
                                            ].filter(Boolean).length
                                        }
                                        size="small"
                                        className="absolute -top-1 -right-1"
                                    />
                                )}
                            </div>
                        </div>
                    </div>
                </div>

                {/* Status Filter Tags */}
                <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg shadow-sm">
                    {STATUS_FILTER_TAGS.map((tag) => (
                        <button
                            key={tag.value || "all"}
                            onClick={() => setStatusFilter(tag.value as ActivityStatus)}
                            className={`px-4 py-2 text-sm font-medium rounded-full transition-all duration-200 ${
                                status === tag.value
                                    ? "bg-blue-500 text-white-full shadow-md"
                                    : "bg-white text-gray-600 hover:bg-gray-100"
                            }`}
                        >
                            {tag.label}
                        </button>
                    ))}
                </div>

                {/* Active Filters Summary */}
                {(responsible ||
                    order ||
                    deadlineFrom ||
                    deadlineTo ||
                    deadlineDate) && (
                    <div className="flex flex-wrap items-center gap-2 p-3 bg-blue-50 rounded-lg border border-blue-200">
                        <Text type="secondary" className="text-sm font-medium">
                            Filtros activos:
                        </Text>
                        {responsible && (
                            <Badge
                                count="Responsable"
                                color="blue"
                                style={{ backgroundColor: "#1890ff" }}
                            />
                        )}
                        {order && (
                            <Badge
                                count="Orden"
                                color="blue"
                                style={{ backgroundColor: "#1890ff" }}
                            />
                        )}
                        {(deadlineFrom || deadlineTo || deadlineDate) && (
                            <Badge
                                count={
                                    deadlineDate
                                        ? "Fecha específica"
                                        : deadlineFrom && deadlineTo
                                          ? "Rango de fechas"
                                          : deadlineFrom
                                            ? "Desde fecha"
                                            : "Hasta fecha"
                                }
                                color="blue"
                                style={{ backgroundColor: "#52c41a" }}
                            />
                        )}
                        <Button
                            type="link"
                            size="small"
                            onClick={clearFilters}
                            className="text-blue-600 hover:text-blue-800 p-0 h-auto"
                        >
                            Limpiar todos
                        </Button>
                    </div>
                )}

                <ActivitiesTable initialData={activities} />

                <div className="flex justify-between items-center p-4 bg-white-full rounded-lg shadow-sm">
                    <Text type="secondary">
                        {activities.length} de {count} actividades
                    </Text>
                    <Pagination
                        current={page}
                        pageSize={pageSize}
                        total={count}
                        onChange={handleSetPage}
                        showSizeChanger
                    />
                </div>

                {/* Filter Drawer */}
                <Drawer
                    title="Aplicar filtros"
                    placement="right"
                    closable={true}
                    onClose={() => setIsFilterDrawerOpen(false)}
                    open={isFilterDrawerOpen}
                    width={400}
                    footer={
                        <div className="flex gap-2">
                            <Button
                                type="primary"
                                onClick={applyFilters}
                                className="flex-1"
                            >
                                Aplicar filtros
                            </Button>
                            <Button onClick={clearFilters} className="flex-1">
                                Limpiar
                            </Button>
                        </div>
                    }
                >
                    <div className="space-y-4">
                        <div>
                            <Text className="font-medium mb-2 block">Responsable</Text>
                            <SelectStaffUser
                                placeholder="Seleccionar responsable"
                                value={filterResponsible || undefined}
                                onChange={setFilterResponsible}
                                className="w-full"
                            />
                        </div>

                        {/* Date Filter Section */}
                        <div>
                            <Text className="font-medium mb-3 flex items-center gap-2">
                                <Calendar size={16} />
                                Filtro por fecha límite
                            </Text>

                            <Space
                                direction="vertical"
                                className="w-full"
                                size="middle"
                            >
                                <Radio.Group
                                    value={dateFilterType}
                                    onChange={(e) => setDateFilterType(e.target.value)}
                                    className="w-full"
                                >
                                    <Space direction="vertical">
                                        <Radio value="range">Rango de fechas</Radio>
                                        <Radio value="specific">Fecha específica</Radio>
                                    </Space>
                                </Radio.Group>

                                {dateFilterType === "range" ? (
                                    <Space direction="vertical" className="w-full">
                                        <div>
                                            <Text type="secondary" className="text-sm">
                                                Desde:
                                            </Text>
                                            <DatePicker
                                                placeholder="Fecha inicio"
                                                value={
                                                    filterDeadlineFrom
                                                        ? dayjs(filterDeadlineFrom)
                                                        : null
                                                }
                                                onChange={(date) =>
                                                    setFilterDeadlineFrom(
                                                        date
                                                            ? date.format("YYYY-MM-DD")
                                                            : "",
                                                    )
                                                }
                                                className="w-full"
                                                format="DD/MM/YYYY"
                                            />
                                        </div>
                                        <div>
                                            <Text type="secondary" className="text-sm">
                                                Hasta:
                                            </Text>
                                            <DatePicker
                                                placeholder="Fecha fin"
                                                value={
                                                    filterDeadlineTo
                                                        ? dayjs(filterDeadlineTo)
                                                        : null
                                                }
                                                onChange={(date) =>
                                                    setFilterDeadlineTo(
                                                        date
                                                            ? date.format("YYYY-MM-DD")
                                                            : "",
                                                    )
                                                }
                                                className="w-full"
                                                format="DD/MM/YYYY"
                                                disabled={!filterDeadlineFrom}
                                                disabledDate={(current) =>
                                                    filterDeadlineFrom
                                                        ? current.isBefore(
                                                              dayjs(filterDeadlineFrom),
                                                          )
                                                        : false
                                                }
                                            />
                                        </div>
                                    </Space>
                                ) : (
                                    <DatePicker
                                        placeholder="Seleccionar fecha específica"
                                        value={
                                            filterDeadlineDate
                                                ? dayjs(filterDeadlineDate)
                                                : null
                                        }
                                        onChange={(date) =>
                                            setFilterDeadlineDate(
                                                date ? date.format("YYYY-MM-DD") : "",
                                            )
                                        }
                                        className="w-full"
                                        format="DD/MM/YYYY"
                                    />
                                )}
                            </Space>
                        </div>
                    </div>
                </Drawer>
            </div>
        </CrmLayout>
    );
}
