import { OrderCurrency, OrderCurrencyLabels } from "@/features/crm/types/order";
import { Radio, RadioChangeEvent } from "antd";
import type { RadioGroupProps } from "antd/es/radio";

// Use RadioGroupProps instead of CheckboxGroupProps
interface RadioCurrencyProps extends Omit<RadioGroupProps, "options"> {
    value?: OrderCurrency;
    onChange?: (e: RadioChangeEvent) => void;
}

export default function RadioCurrency({
    value,
    onChange,
    ...restProps
}: RadioCurrencyProps) {
    const options = Object.values(OrderCurrency).map((currency) => ({
        value: currency,
        label: OrderCurrencyLabels[currency],
    }));

    return (
        <Radio.Group
            {...restProps}
            value={value}
            optionType="button"
            buttonStyle="solid"
            onChange={onChange}
            options={options}
        />
    );
}
