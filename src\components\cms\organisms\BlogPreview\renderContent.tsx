import React from "react";
import { Yo<PERSON>taContentValue } from "@yoopta/editor";
import type {
    YooptaChildNode,
    YooptaNodeBase,
    YooptaTextNode,
} from "@myTypes/yoopta-editor";

/**
 * Renders Yoopta editor content as React components
 */
export const renderYooptaContent = (content: YooptaContentValue): React.ReactNode[] => {
    if (!content) return [];

    // Sort content by order
    const sortedContent = Object.values(content as Record<string, YooptaNodeBase>).sort(
        (a, b) => a.meta.order - b.meta.order,
    );

    return sortedContent
        .map((node) => {
            const key = node.id;
            const valueItem = node.value[0]; // Get the first item from value array

            switch (node.type) {
                case "HeadingOne":
                    return (
                        <h1 id={valueItem.id} key={key} className="blog-content-h1">
                            {(valueItem.children[0] as YooptaTextNode).text || ""}
                        </h1>
                    );

                case "HeadingTwo":
                    return (
                        <h2 id={valueItem.id} key={key} className="blog-content-h2">
                            {(valueItem.children[0] as YooptaTextNode).text || ""}
                        </h2>
                    );

                case "HeadingThree":
                    return (
                        <h3 id={valueItem.id} key={key} className="blog-content-h3">
                            {(valueItem.children[0] as YooptaTextNode).text || ""}
                        </h3>
                    );

                case "Paragraph":
                    return (
                        <p
                            key={key}
                            className={`blog-content-paragraph ${node.meta.align ? `text-${node.meta.align}` : ""}`}
                        >
                            {renderTextWithFormatting(
                                valueItem.children as YooptaTextNode[],
                            )}
                        </p>
                    );

                case "BulletedList":
                    return (
                        <ul key={key} className="blog-content-ul">
                            <li className="blog-content-li">
                                {renderTextWithFormatting(valueItem.children)}
                            </li>
                        </ul>
                    );

                case "NumberedList":
                    return (
                        <ol key={key} className="blog-content-ol mb-2">
                            <li className="blog-content-li">
                                {renderTextWithFormatting(valueItem.children)}
                            </li>
                        </ol>
                    );

                case "Blockquote":
                    return (
                        <blockquote key={key} className="blog-content-blockquote">
                            {valueItem.children.map((child) =>
                                renderTextWithFormatting([child as YooptaTextNode]),
                            )}
                        </blockquote>
                    );

                case "Image":
                    return (
                        <figure
                            key={key}
                            className={`blog-content-figure ${node.meta.align ? `text-${node.meta.align}` : ""}`}
                        >
                            <img
                                src={valueItem.props?.src || ""}
                                alt={valueItem.props?.alt || ""}
                                className={`blog-content-image object-${valueItem.props?.fit || "cover"}`}
                                width={valueItem.props?.sizes?.width || 650}
                                height={valueItem.props?.sizes?.height || 500}
                                style={{
                                    display: "block",
                                    margin:
                                        node.meta.align === "center"
                                            ? "0 auto"
                                            : node.meta.align === "right"
                                              ? "0 0 0 auto"
                                              : node.meta.align === "left"
                                                ? "0 auto 0 0"
                                                : "",
                                }}
                            />
                        </figure>
                    );

                case "Divider":
                    return <hr key={key} className="blog-content-divider" />;

                default:
                    return null;
            }
        })
        .filter(Boolean);
};

/**
 * Renders text with formatting (bold, italic, etc.)
 */
const renderTextWithFormatting = (
    textNodes: (YooptaTextNode | YooptaChildNode)[] = [],
): React.ReactNode[] => {
    return textNodes
        .flatMap((node, index) => {
            // Handle link nodes
            if ("type" in node && node.type === "link" && node.props && node.children) {
                const linkContent = renderTextWithFormatting(
                    node.children as (YooptaTextNode | YooptaChildNode)[],
                );

                return (
                    <a
                        key={`link-${node.id || index}`}
                        href={node.props.url || "#"}
                        className="blog-content-link"
                        target={node.props.target || "_blank"}
                        rel={node.props.rel || "noopener noreferrer"}
                        title={node.props.title}
                    >
                        {linkContent}
                    </a>
                );
            }

            // Handle normal text nodes
            if ("text" in node) {
                const textNode = node as YooptaTextNode;
                const textContent = textNode.text || "";
                const parts = textContent.split("\n");

                return parts.map((part, partIndex) => {
                    let result: React.ReactNode = part;

                    // Apply formatting in the correct order
                    if (textNode.bold) {
                        result = (
                            <strong key={`bold-${index}-${partIndex}`}>{result}</strong>
                        );
                    }
                    if (textNode.italic) {
                        result = <em key={`italic-${index}-${partIndex}`}>{result}</em>;
                    }
                    if (textNode.underline) {
                        result = (
                            <u key={`underline-${index}-${partIndex}`}>{result}</u>
                        );
                    }
                    if (textNode.code) {
                        result = (
                            <code
                                key={`code-${index}-${partIndex}`}
                                className="blog-content-code"
                            >
                                {result}
                            </code>
                        );
                    }
                    if (textNode.highlight) {
                        result = (
                            <span
                                key={`highlight-${index}-${partIndex}`}
                                style={{ color: textNode.highlight.color }}
                            >
                                {result}
                            </span>
                        );
                    }
                    // Handle simple links (not link type nodes)
                    if (textNode.link) {
                        result = (
                            <a
                                key={`link-${index}-${partIndex}`}
                                href={textNode.link}
                                className="blog-content-link"
                                target="_blank"
                                rel="noopener noreferrer"
                            >
                                {result}
                            </a>
                        );
                    }

                    // Add explicit line break if not the last fragment
                    return partIndex < parts.length - 1
                        ? [
                              result,
                              <React.Fragment key={`br-${index}-${partIndex}`}>
                                  <br />
                              </React.Fragment>,
                          ]
                        : result;
                });
            }

            if ("children" in node && node.children) {
                return renderTextWithFormatting(
                    node.children as (YooptaTextNode | YooptaChildNode)[],
                );
            }

            return null;
        })
        .filter(Boolean);
};
