import { Link, useParams, useSearchParams } from "react-router-dom";
import { Breadcrumb, Typography, message, notification, Tabs } from "antd";
import { useQuery } from "@tanstack/react-query";
import type { TabsProps } from "antd";

const { Text } = Typography;

import Spinner from "@components/shared/atoms/Spinner";

import { retrieveOffering } from "@services/portals/cms/offering";
import CmsLayout from "@layouts/cms/CmsLayout";
import WelcomeBar from "@components/shared/molecules/WelcomeBar";
import GeneralOfferingEdit from "@components/cms/organisms/GeneralOfferingEdit";
import ObjectivesOfferingEdit from "@components/cms/organisms/ObjectivesOfferingEdit";
import CurriculumOfferingEdit from "@components/cms/organisms/CurriculumOfferingEdit";
import IntegrationsOfferingEdit from "@components/cms/organisms/IntegrationsOfferingEdit";

export default function OfferingEditPage() {
    const [messageApi, messageHolder] = message.useMessage();
    const [notificationApi, notificationHolder] = notification.useNotification();
    const { oid } = useParams<{ oid: string }>();
    const [searchParams, setSearchParams] = useSearchParams();

    const { isLoading, data, refetch } = useQuery({
        queryKey: ["offering", oid],
        queryFn: () => retrieveOffering(oid as string),
        enabled: oid !== undefined,
        refetchOnWindowFocus: false,
    });

    const handleRefetch = () => {
        refetch();
    };

    const handleTabChange = (key: string) => {
        setSearchParams({ tab: key });
    };

    const tabItems: TabsProps["items"] = [
        {
            key: "general",
            label: "General",
            children: data && oid && (
                <GeneralOfferingEdit
                    oid={oid}
                    data={data}
                    messageApi={messageApi}
                    notificationApi={notificationApi}
                    handleRefetch={handleRefetch}
                />
            ),
        },
        {
            key: "curriculum",
            label: "Currícula & Docentes",
            children: data && oid && (
                <CurriculumOfferingEdit
                    oid={oid}
                    data={data}
                    messageApi={messageApi}
                    notificationApi={notificationApi}
                    handleRefetch={handleRefetch}
                />
            ),
        },
        {
            key: "objectives",
            label: "Objetivos",
            children: data && oid && (
                <ObjectivesOfferingEdit
                    oid={oid}
                    data={data}
                    messageApi={messageApi}
                    notificationApi={notificationApi}
                    handleRefetch={handleRefetch}
                />
            ),
        },
        {
            key: "integrations",
            label: "Integraciones",
            children: data && oid && (
                <IntegrationsOfferingEdit
                    oid={oid}
                    data={data}
                    handleRefetch={handleRefetch}
                />
            ),
        },
    ];

    const defaultTab = searchParams.get("tab") || "general";

    return (
        <>
            {messageHolder}
            {notificationHolder}
            <CmsLayout>
                <div className="max-w-7xl w-full h-full space-y-5">
                    <div className="flex justify-between items-center">
                        <WelcomeBar helperText="Edita aquí los detalles de Productos (Programas y Talleres) que se visualizarán en la Website." />
                    </div>
                    {isLoading ? (
                        <Spinner />
                    ) : (
                        <>
                            <div className="p-5 bg-white-full rounded-lg space-y-5 shadow-sm">
                                <div className="flex items-center">
                                    <Breadcrumb
                                        separator=">"
                                        items={[
                                            {
                                                title: (
                                                    <Link
                                                        to="/cms/offering"
                                                        className="text-base"
                                                    >
                                                        Productos
                                                    </Link>
                                                ),
                                            },
                                            {
                                                title: (
                                                    <Link
                                                        to={`/cms/offering/${oid}`}
                                                        className="text-base"
                                                    >
                                                        {data?.name}
                                                    </Link>
                                                ),
                                            },
                                            {
                                                title: (
                                                    <Text className="text-base">
                                                        Editar
                                                    </Text>
                                                ),
                                            },
                                        ]}
                                    />
                                </div>
                            </div>
                            <div className="space-y-5">
                                <Tabs
                                    items={tabItems}
                                    onChange={handleTabChange}
                                    activeKey={defaultTab}
                                />
                            </div>
                        </>
                    )}
                </div>
            </CmsLayout>
        </>
    );
}
