import React, { useState } from "react";
import { Layout, Button, Space, Tooltip, message, notification } from "antd";
import BlogEditorSettingsDrawer from "../../../components/cms/organisms/BlogEdit/BlogEditSettings";
import { PanelRightClose, PanelRightOpen, Settings, Send, Eye } from "lucide-react";
import BlogEditorSidebar from "../../../components/cms/molecules/BlogEditor/BlogEditorSidebar";
import BlogEditorMobileHeader from "../../../components/cms/molecules/BlogEditor/BlogEditorMobileHeader";
import useIsMobile from "@hooks/use-is-mobile";
import BlogEditorSaveStatus from "@components/cms/molecules/BlogEditor/BlogEditorSaveStatus";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { retrieveBlogPost, updateBlogPost } from "@services/portals/cms/blogs/post";
import { useNavigate, useParams } from "react-router-dom";
import { BlogStatus } from "@myTypes/blog";
import { onSuccessMessage } from "@lib/message";
import type { AxiosError } from "axios";
import { openErrorNotification } from "@lib/notification";

const { Header, Content, Sider } = Layout;

interface BlogEditorLayoutProps {
    children: React.ReactNode;
}

const BlogEditorLayout: React.FC<BlogEditorLayoutProps> = ({ children }) => {
    const { bid } = useParams<{ bid: string }>();
    const navigate = useNavigate();
    const [settingsVisible, setSettingsVisible] = useState(false);
    const [sidebarVisible, setSidebarVisible] = useState(true);
    const isMobile = useIsMobile();

    const [messageApi, contextHolder] = message.useMessage();
    const [notificationApi, notificationContextHolder] = notification.useNotification();

    const queryClient = useQueryClient();

    const { data } = useQuery({
        queryKey: ["blog", bid],
        queryFn: () => retrieveBlogPost(bid),
        enabled: !!bid,
    });

    const publishPostMutation = useMutation({
        mutationFn: () => updateBlogPost(bid, { status: BlogStatus.PUBLISHED }),
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: ["blog", bid],
            });
            navigate(`/cms/blog/${bid}/edit?status=published`, { replace: true });
            onSuccessMessage("Blog publicado", messageApi);
        },
        onError: (error: AxiosError) => {
            const errorMessages: string[] = Object.values(error.response?.data || {});
            openErrorNotification(
                "Error al publicar",
                errorMessages.length ? errorMessages : error.message,
                notificationApi,
            );
        },
    });

    const onPublishClick = () => {
        publishPostMutation.mutate();
    };

    const toggleSidebar = () => {
        setSidebarVisible(!sidebarVisible);
    };

    const toggleSettings = () => {
        setSettingsVisible(!settingsVisible);
    };

    return (
        <Layout className=" min-h-screen flex flex-row w-[100vw]">
            {contextHolder}
            {notificationContextHolder}
            <Sider
                width={280}
                breakpoint="lg"
                collapsedWidth={0}
                collapsible
                trigger={null}
                collapsed={!sidebarVisible}
                onCollapse={toggleSidebar}
                theme="light"
                className={`
                        shadow-sm z-0 border-r border-gray-100
                        transition-all duration-300 ease-in-out
                        ${sidebarVisible ? "visible" : "invisible"} 
                    `}
            >
                <BlogEditorSidebar />
            </Sider>

            <Layout className="flex flex-col bg-white-full transition-all duration-300 ease-in-out">
                <Header
                    className="
                        bg-white-full px-6 flex items-center justify-between shadow-sm
                        h-16 sticky top-0 z-10 tansition-all duration-300 border-b
                    "
                >
                    <div className="flex items-center w-full">
                        <Tooltip
                            title={sidebarVisible ? "Colapsar panel" : "Expandir panel"}
                        >
                            <Button
                                type="text"
                                icon={
                                    sidebarVisible ? (
                                        <PanelRightClose size={18} />
                                    ) : (
                                        <PanelRightOpen size={18} />
                                    )
                                }
                                onClick={toggleSidebar}
                                className="mr-4 flex items-center justify-center"
                            />
                        </Tooltip>

                        <BlogEditorSaveStatus />
                    </div>
                    {data &&
                        (!isMobile ? (
                            <Space size="middle">
                                <Space>
                                    <Button
                                        type="link"
                                        icon={<Eye size={16} />}
                                        className="rounded-full"
                                        href={`/cms/blog/${data?.bid}/preview`}
                                        target="_blank"
                                    >
                                        Vista previa
                                    </Button>
                                    {data?.status !== BlogStatus.PUBLISHED && (
                                        <Button
                                            type="primary"
                                            icon={<Send size={16} />}
                                            className="rounded-full"
                                            onClick={onPublishClick}
                                        >
                                            Publicar
                                        </Button>
                                    )}
                                </Space>
                                <Tooltip title="Configuración del blog">
                                    <Button
                                        type="text"
                                        icon={<Settings size={18} />}
                                        onClick={toggleSettings}
                                        className="flex items-center justify-center"
                                    />
                                </Tooltip>
                            </Space>
                        ) : (
                            <BlogEditorMobileHeader
                                blog={data}
                                onSettingsClick={toggleSettings}
                                onPublishClick={onPublishClick}
                            />
                        ))}
                </Header>

                <Content
                    className={`
                        mx-auto my-6 p-4 md:p-8 bg-white rounded-lg shadow-sm
                        min-h-[calc(100vh-112px)] w-full bg-white-full
                        ${isMobile ? "max-w-full" : "max-w-[1200px]"}
                    `}
                >
                    {children}
                </Content>
            </Layout>

            {data && (
                <BlogEditorSettingsDrawer
                    visible={settingsVisible}
                    onClose={() => setSettingsVisible(false)}
                    blog={data}
                />
            )}
        </Layout>
    );
};

export default BlogEditorLayout;
