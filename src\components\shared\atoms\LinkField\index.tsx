import { Typography } from "antd";

const { Link } = Typography;

type LinkFieldProps = {
    url: string;
    label: string;
    text?: string;
};

export default function LinkField({ url, label, text }: LinkFieldProps) {
    return (
        <div>
            <p className="text-sm text-black-medium font-semibold">{label}</p>
            <Link target="_blank" href={url}>
                {text ? text : url}
            </Link>
        </div>
    );
}
