import React from "react";
import {
    ResponsiveContainer,
    Funnel<PERSON><PERSON>,
    Funnel,
    Cell,
    Tooltip,
    LabelList,
} from "recharts";
import { Card, Typography } from "antd";

const { Text } = Typography;

interface FunnelChartCardProps {
    title: string;
    data: Array<{
        name: string;
        value: number;
        rate?: number;
    }>;
    colors: string[];
    icon?: React.ReactNode;
    showConversionRates?: boolean;
}

export default function FunnelChartCard({
    title,
    data,
    colors,
    icon,
    showConversionRates = false,
}: FunnelChartCardProps) {
    // Calculate conversion rates between steps if not provided
    const dataWithRates = React.useMemo(() => {
        if (data.length <= 1) return data;

        return data.map((item, index) => {
            if (index === 0) return item;

            const previousValue = data[index - 1].value;
            const currentValue = item.value;
            const rate =
                previousValue > 0
                    ? Math.round((currentValue / previousValue) * 100)
                    : 0;

            return {
                ...item,
                rate: rate,
            };
        });
    }, [data]);

    interface CustomTooltipProps {
        active?: boolean;
        payload?: Array<{
            payload: {
                name: string;
                value: number;
                rate?: number;
            };
            color: string;
        }>;
    }

    const CustomTooltip = ({ active, payload }: CustomTooltipProps) => {
        if (active && payload && payload.length) {
            const item = payload[0].payload;

            return (
                <div className="bg-white-full p-3 border border-gray-200 shadow-lg rounded-md">
                    <div className="font-medium text-gray-800 mb-2">{item.name}</div>
                    <div className="flex items-center gap-2 mb-1">
                        <div
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: payload[0].color }}
                        ></div>
                        <div>
                            <span className="font-semibold">{item.value}</span>{" "}
                            {item.name}
                        </div>
                    </div>

                    {item.rate !== undefined && (
                        <div className="mt-2 pt-2 border-t border-gray-200">
                            <div className="flex items-center">
                                <Text type="secondary">Tasa de conversión: </Text>
                                <Text strong className="ml-1 text-green-600">
                                    {item.rate}%
                                </Text>
                            </div>
                        </div>
                    )}
                </div>
            );
        }
        return null;
    };

    return (
        <Card
            title={
                <div className="flex items-center gap-2">
                    {icon}
                    <span>{title}</span>
                </div>
            }
            className="h-full shadow-md overflow-visible"
        >
            <div className="h-80 flex justify-center overflow-visible">
                <ResponsiveContainer width="100%" height="100%">
                    <FunnelChart>
                        <Tooltip content={<CustomTooltip />} />
                        <Funnel
                            dataKey="value"
                            data={dataWithRates}
                            isAnimationActive
                            width={"60%"}
                        >
                            <LabelList
                                position="left"
                                fill="#000"
                                stroke="none"
                                dataKey="name"
                                formatter={(value: string) => [value]}
                            />
                            <LabelList
                                position="right"
                                fill="#666"
                                stroke="none"
                                dataKey="value"
                                offset={40}
                                formatter={(value: number) => [`(${value})`]}
                            />
                            {showConversionRates && (
                                <LabelList
                                    position="right"
                                    fill="#389e0d"
                                    stroke="none"
                                    dataKey="rate"
                                    offset={80}
                                    formatter={(value: number | undefined) =>
                                        value !== undefined ? [`${value}%`] : [""]
                                    }
                                />
                            )}
                            {dataWithRates.map((_, index) => (
                                <Cell
                                    key={`cell-${index}`}
                                    fill={colors[index % colors.length]}
                                />
                            ))}
                        </Funnel>
                    </FunnelChart>
                </ResponsiveContainer>
            </div>
        </Card>
    );
}
