import { <PERSON>, <PERSON>, Card, Statistic, Tooltip, Skeleton } from "antd";
import {
    DollarSign,
    TrendingUp,
    Per<PERSON>,
    Clock,
    CheckCircle,
    Info,
} from "lucide-react";

import PaymentFilterCard from "@/features/crm/components/molecules/payment-filter-card";
import PaymentHistoricalChart from "@/features/crm/components/molecules/payment-historical-chart";
import PaymentCurrencyDistributionChart from "@/features/crm/components/molecules/payment-currency-distribution-chart";
import PaymentHistoricalMethodsChart from "@/features/crm/components/molecules/payment-historical-methods-chart";
import PaymentMethodsDistributionChart from "@/features/crm/components/molecules/payment-methods-distribution-chart";
import PaymentRecentTable from "@/features/crm/components/molecules/payment-recent-table";

// Hooks and types
import { useSearchParams } from "react-router-dom";
import {
    useDashboardPaymentsSummary,
    createDashboardPaymentsQueryParams,
} from "@/features/crm/hooks/use-dashboard-payments";
import { usePaymentMethods } from "@/features/crm/hooks/use-payment-method";
import dayjs from "dayjs";
import "dayjs/locale/es";

dayjs.locale("es");

export default function PaymentsDashboardTab() {
    const [searchParams] = useSearchParams();

    // Create reusable query parameters
    const queryParams = createDashboardPaymentsQueryParams(searchParams);

    // Hook for payment summary data
    const { data: summaryData, isLoading: summaryLoading } =
        useDashboardPaymentsSummary(queryParams);

    // Hook for payment methods data
    const { paymentMethods } = usePaymentMethods();

    return (
        <div>
            {/* Filters */}
            <PaymentFilterCard paymentMethods={paymentMethods} />

            {/* Consolidated Payment Summary */}
            <Row gutter={[16, 16]} className="mb-6">
                <Col xs={24}>
                    {summaryLoading ? (
                        <Card>
                            <Skeleton active paragraph={{ rows: 6 }} />
                        </Card>
                    ) : (
                        <Card
                            title={
                                <div className="flex items-center">
                                    <DollarSign className="mr-2 h-5 w-5 text-green-500" />
                                    <span>Resumen de Pagos</span>
                                </div>
                            }
                        >
                            <Row gutter={[16, 16]}>
                                {/* Total Registered */}
                                <Col xs={24} sm={12} lg={8}>
                                    <div className="bg-blue-50 p-4 rounded-lg">
                                        <div className="text-blue-800 font-medium mb-2 flex items-center">
                                            Total Registrado
                                            <Tooltip title="Monto total de todos los pagos registrados en el sistema, incluyendo pagados y pendientes">
                                                <Info
                                                    size={14}
                                                    className="ml-1 text-blue-600 cursor-help"
                                                />
                                            </Tooltip>
                                        </div>
                                        <div className="space-y-2">
                                            <div className="flex justify-between">
                                                <span className="text-sm text-blue-600">
                                                    PEN:
                                                </span>
                                                <span className="font-semibold text-blue-800">
                                                    S/.{" "}
                                                    {summaryData?.totalRegisteredAmount?.pen?.toLocaleString() ||
                                                        "0.00"}
                                                </span>
                                            </div>
                                            <div className="flex justify-between">
                                                <span className="text-sm text-blue-600">
                                                    USD:
                                                </span>
                                                <span className="font-semibold text-blue-800">
                                                    ${" "}
                                                    {summaryData?.totalRegisteredAmount?.usd?.toLocaleString() ||
                                                        "0.00"}
                                                </span>
                                            </div>
                                            <div className="pt-2 border-t border-blue-200">
                                                <div className="flex justify-between">
                                                    <span className="text-sm font-medium text-blue-700">
                                                        Total:
                                                    </span>
                                                    <span className="font-bold text-blue-900">
                                                        S/.{" "}
                                                        {summaryData?.totalRegisteredAmount?.total?.toLocaleString() ||
                                                            "0.00"}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </Col>

                                {/* Total Paid */}
                                <Col xs={24} sm={12} lg={8}>
                                    <div className="bg-green-50 p-4 rounded-lg">
                                        <div className="text-green-800 font-medium mb-2 flex items-center">
                                            Monto recaudado
                                            <Tooltip title="Total de deudas que han sido pagadas completamente por los clientes">
                                                <Info
                                                    size={14}
                                                    className="ml-1 text-green-600 cursor-help"
                                                />
                                            </Tooltip>
                                        </div>
                                        <div className="space-y-2">
                                            <div className="flex justify-between">
                                                <span className="text-sm text-green-600">
                                                    PEN:
                                                </span>
                                                <span className="font-semibold text-green-800">
                                                    S/.{" "}
                                                    {summaryData?.totalDebtPaidAmount?.pen?.toLocaleString() ||
                                                        "0.00"}
                                                </span>
                                            </div>
                                            <div className="flex justify-between">
                                                <span className="text-sm text-green-600">
                                                    USD:
                                                </span>
                                                <span className="font-semibold text-green-800">
                                                    ${" "}
                                                    {summaryData?.totalDebtPaidAmount?.usd?.toLocaleString() ||
                                                        "0.00"}
                                                </span>
                                            </div>
                                            <div className="pt-2 border-t border-green-200">
                                                <div className="flex justify-between">
                                                    <span className="text-sm font-medium text-green-700">
                                                        Total:
                                                    </span>
                                                    <span className="font-bold text-green-900">
                                                        S/.{" "}
                                                        {summaryData?.totalDebtPaidAmount?.total?.toLocaleString() ||
                                                            "0.00"}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </Col>

                                {/* Total Pending */}
                                <Col xs={24} sm={12} lg={8}>
                                    <div className="bg-yellow-50 p-4 rounded-lg">
                                        <div className="text-yellow-800 font-medium mb-2 flex items-center">
                                            Deuda Pendiente
                                            <Tooltip title="Total de deudas pendientes que aún no han sido pagadas por los clientes">
                                                <Info
                                                    size={14}
                                                    className="ml-1 text-yellow-600 cursor-help"
                                                />
                                            </Tooltip>
                                        </div>
                                        <div className="space-y-2">
                                            <div className="flex justify-between">
                                                <span className="text-sm text-yellow-600">
                                                    PEN:
                                                </span>
                                                <span className="font-semibold text-yellow-800">
                                                    S/.{" "}
                                                    {summaryData?.totalPendingDebtAmount?.pen?.toLocaleString() ||
                                                        "0.00"}
                                                </span>
                                            </div>
                                            <div className="flex justify-between">
                                                <span className="text-sm text-yellow-600">
                                                    USD:
                                                </span>
                                                <span className="font-semibold text-yellow-800">
                                                    ${" "}
                                                    {summaryData?.totalPendingDebtAmount?.usd?.toLocaleString() ||
                                                        "0.00"}
                                                </span>
                                            </div>
                                            <div className="pt-2 border-t border-yellow-200">
                                                <div className="flex justify-between">
                                                    <span className="text-sm font-medium text-yellow-700">
                                                        Total:
                                                    </span>
                                                    <span className="font-bold text-yellow-900">
                                                        S/.{" "}
                                                        {summaryData?.totalPendingDebtAmount?.total?.toLocaleString() ||
                                                            "0.00"}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </Col>
                            </Row>

                            {/* Currency Exchange Rate */}
                            <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                                <div className="flex items-center justify-between">
                                    <span className="text-sm text-gray-600">
                                        Tipo de cambio:
                                    </span>
                                    <span className="font-medium text-gray-800">
                                        1 USD = S/.{" "}
                                        {summaryData?.currencyExchangeRate || "0.00"}
                                    </span>
                                </div>
                            </div>

                            {/* First Payment Information */}
                            <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="p-3 bg-green-50 rounded-lg">
                                    <div className="text-green-800 font-medium mb-2 text-sm flex items-center">
                                        Pagos de cierre de venta
                                        <Tooltip title="Monto total de los primeros pagos (cuotas iniciales o matrículas) que ya han sido pagados por los clientes">
                                            <Info
                                                size={12}
                                                className="ml-1 text-green-600 cursor-help"
                                            />
                                        </Tooltip>
                                    </div>
                                    <div className="space-y-1">
                                        <div className="flex justify-between text-xs">
                                            <span className="text-green-600">PEN:</span>
                                            <span className="font-medium text-green-800">
                                                S/.{" "}
                                                {summaryData?.totalPaidFirstPaymentAmount?.pen?.toLocaleString() ||
                                                    "0.00"}
                                            </span>
                                        </div>
                                        <div className="flex justify-between text-xs">
                                            <span className="text-green-600">USD:</span>
                                            <span className="font-medium text-green-800">
                                                ${" "}
                                                {summaryData?.totalPaidFirstPaymentAmount?.usd?.toLocaleString() ||
                                                    "0.00"}
                                            </span>
                                        </div>
                                        <div className="pt-1 border-t border-green-200">
                                            <div className="flex justify-between text-xs">
                                                <span className="font-medium text-green-700">
                                                    Total:
                                                </span>
                                                <span className="font-bold text-green-900">
                                                    S/.{" "}
                                                    {summaryData?.totalPaidFirstPaymentAmount?.total?.toLocaleString() ||
                                                        "0.00"}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div className="p-3 bg-yellow-50 rounded-lg">
                                    <div className="text-yellow-800 font-medium mb-2 text-sm flex items-center">
                                        Pagos de cierre de venta para recaudar
                                        <Tooltip title="Monto total de los primeros pagos (cuotas iniciales o matrículas) que aún están pendientes de pago">
                                            <Info
                                                size={12}
                                                className="ml-1 text-yellow-600 cursor-help"
                                            />
                                        </Tooltip>
                                    </div>
                                    <div className="space-y-1">
                                        <div className="flex justify-between text-xs">
                                            <span className="text-yellow-600">
                                                PEN:
                                            </span>
                                            <span className="font-medium text-yellow-800">
                                                S/.{" "}
                                                {summaryData?.totalPendingFirstPaymentAmount?.pen?.toLocaleString() ||
                                                    "0.00"}
                                            </span>
                                        </div>
                                        <div className="flex justify-between text-xs">
                                            <span className="text-yellow-600">
                                                USD:
                                            </span>
                                            <span className="font-medium text-yellow-800">
                                                ${" "}
                                                {summaryData?.totalPendingFirstPaymentAmount?.usd?.toLocaleString() ||
                                                    "0.00"}
                                            </span>
                                        </div>
                                        <div className="pt-1 border-t border-yellow-200">
                                            <div className="flex justify-between text-xs">
                                                <span className="font-medium text-yellow-700">
                                                    Total:
                                                </span>
                                                <span className="font-bold text-yellow-900">
                                                    S/.{" "}
                                                    {summaryData?.totalPendingFirstPaymentAmount?.total?.toLocaleString() ||
                                                        "0.00"}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </Card>
                    )}
                </Col>
            </Row>

            {/* KPI Cards */}
            <Row gutter={[16, 16]} className="mb-6">
                <Col xs={24} sm={12} lg={6}>
                    {summaryLoading ? (
                        <Card>
                            <Skeleton active paragraph={{ rows: 2 }} />
                        </Card>
                    ) : (
                        <Card>
                            <Statistic
                                title={
                                    <span className="text-gray-600 font-medium flex items-center">
                                        Tasa de cobro de deudas
                                        <Tooltip title="Porcentaje de deudas que han sido exitosamente cobradas del total de deudas registradas">
                                            <Info
                                                size={14}
                                                className="ml-1 text-gray-500 cursor-help"
                                            />
                                        </Tooltip>
                                    </span>
                                }
                                value={summaryData?.debtCollectionRate?.percentage || 0}
                                suffix="%"
                                precision={1}
                                valueStyle={{ color: "#3f8600" }}
                                prefix={
                                    <CheckCircle
                                        size={20}
                                        className="mr-2 text-green-500"
                                    />
                                }
                            />
                            <div className="text-xs text-gray-500 mt-2">
                                {summaryData?.debtCollectionRate?.successful || 0} de{" "}
                                {summaryData?.debtCollectionRate?.total || 0} deudas
                                cobradas
                            </div>
                        </Card>
                    )}
                </Col>
                <Col xs={24} sm={12} lg={6}>
                    {summaryLoading ? (
                        <Card>
                            <Skeleton active paragraph={{ rows: 2 }} />
                        </Card>
                    ) : (
                        <Card>
                            <Statistic
                                title={
                                    <span className="text-gray-600 font-medium flex items-center">
                                        Promedio días para pagar
                                        <Tooltip title="Tiempo promedio en días que toma a los clientes realizar un pago desde la fecha de creación de la deuda">
                                            <Info
                                                size={14}
                                                className="ml-1 text-gray-500 cursor-help"
                                            />
                                        </Tooltip>
                                    </span>
                                }
                                value={summaryData?.averagePaymentDays || 0}
                                precision={1}
                                valueStyle={{ color: "#1890ff" }}
                                prefix={
                                    <Clock size={20} className="mr-2 text-blue-500" />
                                }
                                suffix="días"
                            />
                            <div className="text-xs text-gray-500 mt-2">
                                Desde creación hasta pago
                            </div>
                        </Card>
                    )}
                </Col>
                <Col xs={24} sm={12} lg={6}>
                    {summaryLoading ? (
                        <Card>
                            <Skeleton active paragraph={{ rows: 2 }} />
                        </Card>
                    ) : (
                        <Card>
                            <Statistic
                                title={
                                    <span className="text-gray-600 font-medium flex items-center">
                                        Conversión de pagos
                                        <Tooltip title="Porcentaje de órdenes o deudas que se convierten exitosamente en pagos completados">
                                            <Info
                                                size={14}
                                                className="ml-1 text-gray-500 cursor-help"
                                            />
                                        </Tooltip>
                                    </span>
                                }
                                value={
                                    summaryData?.paymentConversionRate?.percentage || 0
                                }
                                suffix="%"
                                precision={1}
                                valueStyle={{ color: "#722ed1" }}
                                prefix={
                                    <Percent
                                        size={20}
                                        className="mr-2 text-purple-500"
                                    />
                                }
                            />
                            <div className="text-xs text-gray-500 mt-2">
                                {summaryData?.paymentConversionRate?.paid || 0} de{" "}
                                {summaryData?.paymentConversionRate?.total || 0}{" "}
                                convertidos
                            </div>
                        </Card>
                    )}
                </Col>
                <Col xs={24} sm={12} lg={6}>
                    {summaryLoading ? (
                        <Card>
                            <Skeleton active paragraph={{ rows: 2 }} />
                        </Card>
                    ) : (
                        <Card>
                            <Statistic
                                title={
                                    <span className="text-gray-600 font-medium flex items-center">
                                        Recuperación de deuda
                                        <Tooltip title="Porcentaje de pagos tardíos que fueron recuperados exitosamente después de acciones de seguimiento">
                                            <Info
                                                size={14}
                                                className="ml-1 text-gray-500 cursor-help"
                                            />
                                        </Tooltip>
                                    </span>
                                }
                                value={summaryData?.recoveryRate?.percentage || 0}
                                suffix="%"
                                precision={1}
                                valueStyle={{ color: "#fa8c16" }}
                                prefix={
                                    <TrendingUp
                                        size={20}
                                        className="mr-2 text-orange-500"
                                    />
                                }
                            />
                            <div className="text-xs text-gray-500 mt-2">
                                {summaryData?.recoveryRate?.latePaid || 0} pagos tardíos
                                de {summaryData?.recoveryRate?.totalScheduled || 0}{" "}
                                programados
                            </div>
                        </Card>
                    )}
                </Col>
            </Row>

            {/* Charts Row */}
            <Row gutter={[16, 16]} className="mb-6">
                <Col xs={24} lg={16}>
                    <PaymentHistoricalChart />
                </Col>
                <Col xs={24} lg={8}>
                    <PaymentCurrencyDistributionChart />
                </Col>
            </Row>

            {/* Payment Methods Charts Row */}
            <Row gutter={[16, 16]} className="mb-6">
                <Col xs={24} lg={16}>
                    <PaymentHistoricalMethodsChart />
                </Col>
                <Col xs={24} lg={8}>
                    <PaymentMethodsDistributionChart />
                </Col>
            </Row>

            {/* Recent Payments */}
            <Row gutter={[16, 16]}>
                <Col xs={24}>
                    <PaymentRecentTable />
                </Col>
            </Row>
        </div>
    );
}
