import {
    EventModality,
    EventModalityLabels,
    EventType,
    EventTypeLabels,
} from "../../types/event";
import { Tablet, Users, Video, Monitor } from "lucide-react";

type EventTypeCellProps = {
    type: EventType;
    modality: EventModality;
};

export default function EventTypeCell({ type, modality }: EventTypeCellProps) {
    const getTypeIcon = (type: EventType) => {
        switch (type) {
            case EventType.WEBINAR:
                return <Video size={16} className="text-cyan-600" strokeWidth={2} />;
            case EventType.WORKSHOP:
                return <Users size={16} className="text-indigo-600" strokeWidth={2} />;
            case EventType.HANDS_OF_WORKSHOP:
                return <Tablet size={16} className="text-purple-600" strokeWidth={2} />;
            default:
                return <Monitor size={16} className="text-gray-600" strokeWidth={2} />;
        }
    };

    const getModalityInfo = (modality: EventModality) => {
        switch (modality) {
            case EventModality.REMOTE:
                return {
                    icon: (
                        <Monitor
                            size={14}
                            className="text-purple-600"
                            strokeWidth={2}
                        />
                    ),
                    bgColor: "bg-purple-50",
                    borderColor: "border-purple-200",
                    textColor: "text-purple-800",
                };
            case EventModality.IN_PERSON:
                return {
                    icon: <Users size={14} className="text-blue-600" strokeWidth={2} />,
                    bgColor: "bg-blue-50",
                    borderColor: "border-blue-200",
                    textColor: "text-blue-800",
                };
            default:
                return {
                    icon: null,
                    bgColor: "bg-gray-50",
                    borderColor: "border-gray-200",
                    textColor: "text-gray-800",
                };
        }
    };

    const typeIcon = getTypeIcon(type);
    const modalityInfo = getModalityInfo(modality);

    return (
        <div className="flex flex-col gap-1.5">
            <div className="flex items-center gap-1.5">
                {typeIcon}
                <span className="text-sm font-medium">{EventTypeLabels[type]}</span>
            </div>

            <div className="flex">
                <span
                    className={`text-xs font-medium px-2 py-0.5 rounded-full border
                    ${modalityInfo.bgColor} ${modalityInfo.textColor} ${modalityInfo.borderColor}`}
                >
                    <div className="flex items-center gap-1">
                        {modalityInfo.icon}
                        <span>{EventModalityLabels[modality]}</span>
                    </div>
                </span>
            </div>
        </div>
    );
}
