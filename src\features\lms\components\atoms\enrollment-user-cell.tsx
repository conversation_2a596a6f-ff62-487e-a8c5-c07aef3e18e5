import { Typography } from "antd";
import { User, Mail, Phone } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import { EnrollmentUser } from "../../types/enrollment";

const { Text } = Typography;

interface EnrollmentUserCellProps {
    user: EnrollmentUser;
}

export default function EnrollmentUserCell({ user }: EnrollmentUserCellProps) {
    const displayName = user.fullName || `${user.firstName} ${user.lastName}`;

    return (
        <div className="space-y-1">
            <div className="flex items-center gap-2">
                <User size={14} className="text-gray-500" />
                <Link
                    to={`/crm/contacts/${user.uid}`}
                    className="font-semibold text-blue-600 hover:text-blue-800"
                >
                    {displayName}
                </Link>
            </div>
            <div className="flex items-center gap-2">
                <Mail size={12} className="text-gray-400" />
                <Text className="text-xs text-gray-600">{user.email}</Text>
            </div>
            {user.phoneNumber && (
                <div className="flex items-center gap-2">
                    <Phone size={12} className="text-gray-400" />
                    <Text className="text-xs text-gray-600">{user.phoneNumber}</Text>
                </div>
            )}
        </div>
    );
}
