import { PaginatedResponse } from "@myTypes/base";
import { portalsApi } from "@services/portals";
import {
    DEFAULT_PAGE,
    DEFAULT_PAGE_SIZE,
    ListPaymentsQueryParams,
    PaymentCreateRequest,
    PaymentListItem,
    PaymentRetrieve,
    PaymentUpdateRequest,
} from "../../types/payment";
import { UploadFile } from "antd";

export const listPayments = async (
    queryParams: ListPaymentsQueryParams = {
        page: DEFAULT_PAGE,
        pageSize: DEFAULT_PAGE_SIZE,
    },
): Promise<PaginatedResponse<PaymentListItem>> => {
    const response = await portalsApi.get("crm/payments", {
        params: queryParams,
    });
    return response.data;
};

export const retrievePayment = async (pid: string): Promise<PaymentRetrieve> => {
    const response = await portalsApi.get(`crm/payments/${pid}`);
    return response.data;
};

export const createPayment = async (
    payment: PaymentCreateRequest,
): Promise<PaymentRetrieve> => {
    const response = await portalsApi.post("crm/payments", payment);
    return response.data;
};

export const uploadVoucher = async (file: UploadFile) => {
    const formData = new FormData();
    formData.append("file", file as unknown as Blob);
    const response = await portalsApi.post("crm/payments/upload-voucher", formData);
    return response.data;
};

export const attachVoucher = async (pid: string, file: UploadFile) => {
    const formData = new FormData();
    formData.append("file", file as unknown as Blob);
    const response = await portalsApi.post(
        `crm/payments/${pid}/attach-voucher`,
        formData,
    );
    return response.data;
};

export const removeVoucher = async (fid: string) => {
    const response = await portalsApi.delete(`crm/payments/remove-voucher/${fid}`);
    return response.data;
};

export const updatePayment = async (
    pid: string,
    payment: PaymentUpdateRequest,
): Promise<PaymentRetrieve> => {
    const response = await portalsApi.put(`crm/payments/${pid}`, payment);
    return response.data;
};

export const deletePayment = async (pid: string): Promise<void> => {
    await portalsApi.delete(`crm/payments/${pid}`);
};
