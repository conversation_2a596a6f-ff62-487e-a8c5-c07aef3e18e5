import { RcFile } from "antd/es/upload";

export const getImageDimensions = (
    image: RcFile | Blob | File,
): Promise<{ width: number; height: number }> => {
    return new Promise((resolve, reject) => {
        const img = new window.Image();
        img.onload = () => resolve({ width: img.width, height: img.height });
        img.onerror = reject;
        img.src = URL.createObjectURL(image);
    });
};
