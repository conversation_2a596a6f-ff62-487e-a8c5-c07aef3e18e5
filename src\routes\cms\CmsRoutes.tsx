import { Routes, Route } from "react-router-dom";
import CmsDashboardPage from "@pages/cms/DashboardPage";
import NotFoundPage from "@pages/cms/NotFoundPage";
import InstructorListPage from "@pages/cms/InstructorListPage";
import InstructorEditPage from "@pages/cms/InstructorEditPage";
import TestimonialListPage from "@pages/cms/TestimonialListPage";
import TestimoniaEditPage from "@pages/cms/TestimonialEditPage";
import OfferingListPage from "@pages/cms/OfferingListPage";
import OfferingEditPage from "@pages/cms/OfferingEditPage";
import BlogEditPage from "@pages/cms/BlogEditPage";
import BlogListPage from "@pages/cms/BlogListPage";
import BlogCategoryListPage from "@pages/cms/BlogCategoryListPage";
import BlogCategoryEditPage from "@pages/cms/BlogCategoryEditPage";
import BlogTagListPage from "@pages/cms/BlogTagListPage";
import BlogTagEditPage from "@pages/cms/BlogTagEditPage";
import BlogEditBlankPage from "@components/cms/organisms/BlogEdit/BlogEditBlankPage";
import BlogPreviewPage from "@pages/cms/BlogPreviewPage";
import { SubModuleProtectedRoute } from "../ProtectedOutlet/module-protected-outlet";

export default function CmsRoutes() {
    return (
        <Routes>
            <Route path="" element={<CmsDashboardPage />} />
            <Route path="*" element={<NotFoundPage />} />

            {/** Instructor Routes */}
            <Route
                path="instructor"
                element={
                    <SubModuleProtectedRoute
                        subModule="cms.instructor"
                        redirectTo="/cms"
                    />
                }
            >
                <Route index element={<InstructorListPage />} />
                <Route path=":iid/edit" element={<InstructorEditPage />} />
            </Route>

            {/** Testimonial Routes */}
            <Route
                path="testimonial"
                element={
                    <SubModuleProtectedRoute
                        subModule="cms.testimonial"
                        redirectTo="/cms"
                    />
                }
            >
                <Route index element={<TestimonialListPage />} />
                <Route path=":tid/edit" element={<TestimoniaEditPage />} />
            </Route>

            {/** Offering Routes */}
            <Route
                path="offering"
                element={
                    <SubModuleProtectedRoute
                        subModule="cms.offering"
                        redirectTo="/cms"
                    />
                }
            >
                <Route index element={<OfferingListPage />} />
                <Route path=":oid" element={<OfferingEditPage />} />
            </Route>

            {/* Blog Routes */}
            <Route
                path="blog"
                element={
                    <SubModuleProtectedRoute subModule="cms.blog" redirectTo="/cms" />
                }
            >
                <Route index element={<BlogListPage />} />
                <Route path=":bid/edit" element={<BlogEditPage />} />
                <Route path="edit" element={<BlogEditBlankPage />} />
                <Route path=":bid/preview" element={<BlogPreviewPage />} />
            </Route>

            {/* Blog categories Routes */}
            <Route
                path="blog/categories"
                element={
                    <SubModuleProtectedRoute
                        subModule="cms.blogcategory"
                        redirectTo="/cms"
                    />
                }
            >
                <Route index element={<BlogCategoryListPage />} />
                <Route path=":id/edit" element={<BlogCategoryEditPage />} />
            </Route>

            {/* Blog tags Routes */}
            <Route
                path="blog/tags"
                element={
                    <SubModuleProtectedRoute
                        subModule="cms.blogtag"
                        redirectTo="/cms"
                    />
                }
            >
                <Route index element={<BlogTagListPage />} />
                <Route path=":id/edit" element={<BlogTagEditPage />} />
            </Route>
        </Routes>
    );
}
