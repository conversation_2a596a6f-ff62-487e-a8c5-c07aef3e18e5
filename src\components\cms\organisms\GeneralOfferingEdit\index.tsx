import {
    Form,
    Image,
    Input,
    Select,
    Tag,
    Typography,
    Upload,
    DatePicker,
    Button,
    Tooltip,
} from "antd";
import { useNavigate } from "react-router-dom";
import { AxiosError } from "axios";
import ImgCrop from "antd-img-crop";
import { GetProp, UploadFile, UploadProps, InputNumber } from "antd";
import dayjs from "dayjs";

const { Text } = Typography;
const { Dragger } = Upload;
const { Option } = Select;
const { RangePicker } = DatePicker;

import CloudUpload from "@assets/shapes/cloud-upload.svg?react";

import {
    Offering,
    OfferingFormat,
    OfferingFormatLabel,
    OfferingModality,
    OfferingModalityLabel,
    OfferingStage,
    OfferingStageLabel,
    OfferingType,
    OfferingTypeColor,
    OfferingTypeLabel,
    PartialUpdateOfferingBody,
} from "@myTypes/offering";
import { getBase64 } from "@lib/helpers";
import { useMutation } from "@tanstack/react-query";
import { deleteOffering, updateOffering } from "@services/portals/cms/offering";
import { useEffect, useState } from "react";
import { MessageInstance } from "antd/es/message/interface";
import { openErrorNotification } from "@lib/notification";
import { NotificationInstance } from "antd/es/notification/interface";
import Save from "@assets/icons/general/save-stroke.svg?react";
import Trash from "@assets/icons/huge/trash-white.svg?react";
import FormLabel from "@/features/crm/components/atoms/FormLabel";

type FileType = Parameters<GetProp<UploadProps, "beforeUpload">>[0];

type DraggerProps = {
    customRequest: UploadProps["customRequest"];
    onRemove?: UploadProps["onRemove"];
    onPreview?: UploadProps["onPreview"];
    fileList?: UploadProps<FileType>["fileList"];
};

type GeneralOfferingEditProps = {
    data: Offering;
    oid: string;
    messageApi: MessageInstance;
    notificationApi: NotificationInstance;
    handleRefetch: () => void;
};

const GeneralOfferingEdit: React.FC<GeneralOfferingEditProps> = ({
    data,
    oid,
    messageApi,
    notificationApi,
    handleRefetch,
}) => {
    const navigate = useNavigate();
    const [form] = Form.useForm<PartialUpdateOfferingBody>();

    const [fileList, setFileList] = useState<UploadFile<FileType>[]>([]);
    const [previewOpen, setPreviewOpen] = useState(false);

    useEffect(() => {
        if (data && data.thumbnail) {
            const currentFile = {
                uid: data?.thumbnail.fid,
                name: data?.thumbnail.name,
                status: "done",
                url: data?.thumbnail.url,
            };
            setFileList([currentFile as UploadFile<FileType>]);
        }
    }, [data, form]);

    const updateMutation = useMutation({
        mutationFn: (data: PartialUpdateOfferingBody) => {
            return updateOffering(oid as string, data);
        },
        onSuccess: () => {
            messageApi.success("Testimonio actualizado correctamente");
            handleRefetch();
        },
        onError: (error: Error) => {
            openErrorNotification(
                "Error al actualizar el testimonio",
                error.message,
                notificationApi,
            );
        },
    });

    const { mutate: deleteMutate, isError } = useMutation({
        mutationFn: () => deleteOffering(oid as string),
        onSuccess: () => {
            navigate("/cms/offering", { replace: true });
        },
        onError: (error: AxiosError) => {
            console.error(error);
        },
    });

    const handleFormFinish = (values: PartialUpdateOfferingBody) => {
        if (data?.thumbnail && fileList.length === 0) {
            values.deleteThumbnail = true;
        }
        const payload = {
            ...values,
            startDate: values.rangeDate![0].format("YYYY-MM-DD"),
            endDate: values.rangeDate![1].format("YYYY-MM-DD"),
        };
        updateMutation.mutate(payload);
    };

    const draggerProps: DraggerProps = {
        customRequest: async (options) => {
            const { file, onSuccess, onError } = options;
            try {
                if (typeof onSuccess === "function") {
                    const base64file = await getBase64(file as FileType);

                    const uploadFile: UploadFile<FileType> =
                        file as UploadFile<FileType>;
                    uploadFile.url = base64file;

                    form.setFieldsValue({
                        thumbnailFile: [uploadFile],
                    });
                    setFileList([uploadFile]);
                    onSuccess(uploadFile);
                }
            } catch (error) {
                typeof onError === "function" && onError(error as Error);
            }
        },
        onRemove: () => {
            form.setFieldsValue({
                thumbnailFile: undefined,
            });
            setFileList([]);
        },
        onPreview: async () => {
            setPreviewOpen(true);
        },
        fileList,
    };

    const handleSave = () => {
        form.submit();
    };

    return (
        <Form
            name="offeringEdit"
            layout="vertical"
            form={form}
            initialValues={{
                ...data,
                rangeDate: [dayjs(data?.startDate), dayjs(data?.endDate)],
            }}
            onFinish={handleFormFinish}
        >
            <div className="grid grid-cols-1 lg:grid-cols-6 gap-y-6 lg:gap-6">
                <div className="bg-white-full col-span-4 p-5 rounded-lg shadow-sm">
                    <p className="text-gray-400 font-semibold text-sm">
                        INFORMACIÓN GENERAL
                    </p>
                    <Form.Item<PartialUpdateOfferingBody>
                        name="name"
                        label={
                            <FormLabel className="font-semibold text-base">
                                Nombre del Producto
                            </FormLabel>
                        }
                        rules={[
                            {
                                required: true,
                                message: "Por favor, ingrese el nombre del Producto",
                            },
                        ]}
                    >
                        <Input
                            placeholder="Ej. Curso de Marketing Digital"
                            className="py-2"
                        />
                    </Form.Item>

                    <Form.Item<PartialUpdateOfferingBody>
                        name="longName"
                        label={
                            <FormLabel className="font-semibold text-base">
                                Nombre largo del producto
                            </FormLabel>
                        }
                    >
                        <Input
                            placeholder="Ej. I Curso Completo de Marketing Digital y Estrategias de Ventas 2025"
                            className="py-2"
                        />
                    </Form.Item>

                    <Form.Item<PartialUpdateOfferingBody>
                        name="description"
                        label={
                            <FormLabel className="font-semibold text-base">
                                Descripción del producto
                            </FormLabel>
                        }
                    >
                        <Input.TextArea
                            placeholder="Descripción del producto"
                            className="py-2"
                            autoSize={{
                                minRows: 3,
                                maxRows: 7,
                            }}
                        />
                    </Form.Item>

                    <div className="grid grid-cols-2 gap-4">
                        <Form.Item<PartialUpdateOfferingBody>
                            name="duration"
                            label={
                                <FormLabel className="font-semibold text-base">
                                    Duración
                                </FormLabel>
                            }
                        >
                            <Input
                                placeholder="Ej. 6 semanas, 3 meses"
                                className="py-2"
                            />
                        </Form.Item>
                        <Form.Item<PartialUpdateOfferingBody>
                            name="hours"
                            label={
                                <FormLabel className="font-semibold text-base">
                                    Horas
                                </FormLabel>
                            }
                        >
                            <Input
                                type="number"
                                placeholder="Ej. 40"
                                className="py-2"
                            />
                        </Form.Item>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <Form.Item<PartialUpdateOfferingBody>
                            name="type"
                            label={
                                <FormLabel className="font-semibold text-base">
                                    Tipo
                                </FormLabel>
                            }
                            rules={[
                                {
                                    required: true,
                                    message:
                                        "Por favor, seleccione el tipo de producto.",
                                },
                            ]}
                        >
                            <Select>
                                {Object.values(OfferingType).map((type) => (
                                    <Option key={type} value={type}>
                                        <Tag
                                            color={OfferingTypeColor[type]}
                                            className="rounded-full px-3"
                                            bordered={false}
                                        >
                                            {OfferingTypeLabel[type]}
                                        </Tag>
                                    </Option>
                                ))}
                            </Select>
                        </Form.Item>
                        <Form.Item<PartialUpdateOfferingBody>
                            name="format"
                            label={
                                <FormLabel className="font-semibold text-base">
                                    Formato
                                </FormLabel>
                            }
                            rules={[
                                {
                                    required: true,
                                    message: "Por favor, el formato del producto",
                                },
                            ]}
                        >
                            <Select>
                                <Option value={OfferingFormat.ASYNCHRONOUS}>
                                    <Tag
                                        color="green"
                                        className="rounded-full px-3"
                                        bordered={false}
                                    >
                                        {
                                            OfferingFormatLabel[
                                                OfferingFormat.ASYNCHRONOUS
                                            ]
                                        }
                                    </Tag>
                                </Option>
                                <Option value={OfferingFormat.LIVE}>
                                    <Tag
                                        color="blue"
                                        className="rounded-full px-3"
                                        bordered={false}
                                    >
                                        {OfferingFormatLabel[OfferingFormat.LIVE]}
                                    </Tag>
                                </Option>
                            </Select>
                        </Form.Item>
                        <Form.Item<PartialUpdateOfferingBody>
                            name="modality"
                            label={
                                <FormLabel className="font-semibold text-base">
                                    Modalidad
                                </FormLabel>
                            }
                            rules={[
                                {
                                    required: true,
                                    message: "Por favor, la modalidad del producto",
                                },
                            ]}
                        >
                            <Select>
                                <Option value={OfferingModality.IN_PERSON}>
                                    <Tag
                                        color="geekblue"
                                        className="rounded-full px-3"
                                        bordered={false}
                                    >
                                        {
                                            OfferingModalityLabel[
                                                OfferingModality.IN_PERSON
                                            ]
                                        }
                                    </Tag>
                                </Option>
                                <Option value={OfferingModality.REMOTE}>
                                    <Tag
                                        color="purple"
                                        className="rounded-full px-3"
                                        bordered={false}
                                    >
                                        {OfferingModalityLabel[OfferingModality.REMOTE]}
                                    </Tag>
                                </Option>
                            </Select>
                        </Form.Item>
                    </div>
                    <div className="grid grid-cols-4 gap-4">
                        <Form.Item<PartialUpdateOfferingBody>
                            name="basePrice"
                            rules={[
                                {
                                    required: true,
                                    message: "Por favor, ingrese el precio base",
                                },
                            ]}
                            label={
                                <FormLabel className="font-semibold text-base">
                                    Precio base (S/.)
                                </FormLabel>
                            }
                        >
                            <Input type="number" placeholder="Ej. 2" />
                        </Form.Item>
                        <Form.Item<PartialUpdateOfferingBody>
                            name="foreignBasePrice"
                            rules={[
                                {
                                    required: true,
                                    message: "Por favor, ingrese el precio base",
                                },
                            ]}
                            label={
                                <FormLabel className="font-semibold text-base">
                                    Precio base ($)
                                </FormLabel>
                            }
                        >
                            <Input type="number" placeholder="Ej. 2" />
                        </Form.Item>
                        <Form.Item<PartialUpdateOfferingBody>
                            name="discount"
                            rules={[
                                {
                                    required: true,
                                    message:
                                        "Por favor, ingrese porcentaje de descuento",
                                },
                            ]}
                            label={
                                <FormLabel className="font-semibold text-base">
                                    Descuento (%)
                                </FormLabel>
                            }
                        >
                            <Input type="number" placeholder="Ej. 2" />
                        </Form.Item>
                        <div className="space-y-3">
                            <p className="text-base font-semibold">Precio final</p>
                            <p className="text-base font-bold">
                                S/ {data?.finalPrice} ó $ {data?.foreignFinalPrice}
                            </p>
                        </div>
                    </div>
                    <div className="grid grid-cols-3 gap-4">
                        <Form.Item<PartialUpdateOfferingBody>
                            name="frequency"
                            label={
                                <FormLabel className="font-semibold text-base">
                                    Frecuencia
                                </FormLabel>
                            }
                            rules={[
                                {
                                    required: true,
                                    message: "Por favor, ingrese la frecuencia",
                                },
                            ]}
                        >
                            <Input
                                placeholder="Ej. Sábados y Domingos"
                                className="py-2"
                            />
                        </Form.Item>
                        <Form.Item<PartialUpdateOfferingBody>
                            name="schedule"
                            label={
                                <FormLabel className="font-semibold text-base">
                                    Horario
                                </FormLabel>
                            }
                            rules={[
                                {
                                    required: true,
                                    message: "Por favor, ingrese el horario",
                                },
                            ]}
                        >
                            <Input
                                placeholder="Ej. 9:00 AM - 1:00 PM"
                                className="py-2"
                            />
                        </Form.Item>
                        <Form.Item<PartialUpdateOfferingBody>
                            name="rangeDate"
                            label={
                                <FormLabel className="font-semibold text-base">
                                    Inicio {"y"} Fin
                                </FormLabel>
                            }
                            rules={[
                                {
                                    required: true,
                                    message:
                                        "Por favor, ingrese las fechas de inicio y fin del programa",
                                },
                            ]}
                        >
                            <RangePicker className="py-2 w-full" />
                        </Form.Item>
                    </div>
                </div>
                <div className="col-span-2 space-y-6">
                    <div className="bg-white-full p-5 rounded-lg shadow-sm">
                        <p className="text-gray-400 font-semibold text-sm">ACCIONES</p>
                        <div className="flex justify-end gap-3">
                            <Button
                                type="primary"
                                size="large"
                                style={{ fontSize: 16 }}
                                icon={<Trash />}
                                danger
                                disabled={isError}
                                onClick={() => deleteMutate()}
                            >
                                Eliminar
                            </Button>
                            <Button
                                type="primary"
                                size="large"
                                style={{ fontSize: 16 }}
                                icon={<Save />}
                                onClick={handleSave}
                            >
                                Guardar
                            </Button>
                        </div>
                    </div>
                    <div className="bg-white-full p-5 rounded-lg shadow-sm">
                        <p className="text-gray-400 font-semibold text-sm">
                            CONTENIDO MULTIMEDIA
                        </p>
                        <Form.Item<PartialUpdateOfferingBody>
                            name="thumbnailFile"
                            label={
                                <div className="flex items-center gap-2">
                                    <FormLabel className="font-semibold text-base">
                                        Thumbnail del producto
                                    </FormLabel>
                                    <Tooltip title="Esta imagen aparecerá como miniatura en la página web del producto">
                                        <span className="text-gray-400 text-sm cursor-help">
                                            ⓘ
                                        </span>
                                    </Tooltip>
                                </div>
                            }
                            valuePropName="listFile"
                            getValueFromEvent={(e) => {
                                return e?.fileList;
                            }}
                        >
                            <ImgCrop aspect={323 / 249}>
                                <Dragger
                                    {...draggerProps}
                                    listType="picture"
                                    maxCount={1}
                                    multiple={false}
                                >
                                    <div className="flex flex-col justify-center items-center">
                                        <CloudUpload />
                                        <Text className="font-medium text-black-full">
                                            Arrastre una imagen de perfil o haga click
                                            aquí
                                        </Text>
                                        <Text className="text-xs text-black-medium">
                                            Solo una imagen (Máx. 2MB)
                                        </Text>
                                    </div>
                                </Dragger>
                            </ImgCrop>
                        </Form.Item>
                        <Image
                            wrapperStyle={{ display: "none" }}
                            preview={{
                                visible: previewOpen,
                                onVisibleChange: setPreviewOpen,
                            }}
                            src={fileList[0]?.url}
                        />
                    </div>
                    <div className="bg-white-full p-5 rounded-lg shadow-sm">
                        <p className="text-gray-400 font-semibold text-sm">
                            VISIBILIDAD DEL CONTENIDO
                        </p>
                        <Form.Item<PartialUpdateOfferingBody>
                            name="stage"
                            label={
                                <FormLabel className="font-semibold text-base">
                                    Estado/Fase del producto
                                </FormLabel>
                            }
                            rules={[
                                {
                                    required: true,
                                    message: "Por favor, seleccione un estado/fase",
                                },
                            ]}
                        >
                            <Select>
                                <Option value={OfferingStage.PLANNING}>
                                    <Tag
                                        color="orange"
                                        className="rounded-full px-3"
                                        bordered={false}
                                    >
                                        {OfferingStageLabel[OfferingStage.PLANNING]}
                                    </Tag>
                                </Option>
                                <Option value={OfferingStage.LAUNCHED}>
                                    <Tag
                                        color="green"
                                        className="rounded-full px-3"
                                        bordered={false}
                                    >
                                        {OfferingStageLabel[OfferingStage.LAUNCHED]}
                                    </Tag>
                                </Option>
                                <Option value={OfferingStage.ENROLLMENT}>
                                    <Tag
                                        color="blue"
                                        className="rounded-full px-3"
                                        bordered={false}
                                    >
                                        {OfferingStageLabel[OfferingStage.ENROLLMENT]}
                                    </Tag>
                                </Option>
                                <Option value={OfferingStage.ENROLLMENT_CLOSED}>
                                    <Tag
                                        color="purple"
                                        className="rounded-full px-3"
                                        bordered={false}
                                    >
                                        {
                                            OfferingStageLabel[
                                                OfferingStage.ENROLLMENT_CLOSED
                                            ]
                                        }
                                    </Tag>
                                </Option>
                                <Option value={OfferingStage.FINISHED}>
                                    <Tag
                                        color="red"
                                        className="rounded-full px-3"
                                        bordered={false}
                                    >
                                        {OfferingStageLabel[OfferingStage.FINISHED]}
                                    </Tag>
                                </Option>
                            </Select>
                        </Form.Item>
                        <Form.Item<PartialUpdateOfferingBody>
                            name="slug"
                            label={
                                <div className="flex items-center gap-2">
                                    <FormLabel className="font-semibold text-base">
                                        Slug del Producto
                                    </FormLabel>
                                    <Tooltip title="URL amigable del producto. Use solo letras minúsculas, números y guiones. Ej: curso-marketing-digital">
                                        <span className="text-gray-400 text-sm cursor-help">
                                            ⓘ
                                        </span>
                                    </Tooltip>
                                </div>
                            }
                            rules={[
                                {
                                    required: true,
                                    message: "Por favor, ingrese el slug del Producto",
                                },
                            ]}
                        >
                            <Input
                                placeholder="Ej. curso-marketing-digital"
                                className="py-2"
                            />
                        </Form.Item>
                        <div className="grid grid-cols-2 gap-2">
                            <Form.Item<PartialUpdateOfferingBody>
                                name="order"
                                className="w-full"
                                label={
                                    <div className="flex items-center gap-2">
                                        <FormLabel className="font-semibold text-base">
                                            Orden
                                        </FormLabel>
                                        <Tooltip title="Orden de aparición del producto en la página web. Números menores aparecen primero">
                                            <span className="text-gray-400 text-sm cursor-help">
                                                ⓘ
                                            </span>
                                        </Tooltip>
                                    </div>
                                }
                                rules={[
                                    {
                                        type: "integer",
                                        message: "Por favor, ingrese un número entero",
                                    },
                                ]}
                            >
                                <InputNumber
                                    className="w-full"
                                    min={1}
                                    step={1}
                                    placeholder="Ej. 1"
                                />
                            </Form.Item>
                            <Form.Item<PartialUpdateOfferingBody>
                                name="codeName"
                                label={
                                    <FormLabel className="font-semibold text-base">
                                        Código del Producto
                                    </FormLabel>
                                }
                            >
                                <Input placeholder="Ej. PPCEU-2023-I" />
                            </Form.Item>
                        </div>
                    </div>
                </div>
            </div>
        </Form>
    );
};

export default GeneralOfferingEdit;
