import LinkSquare from "@assets/icons/huge/link-square.svg?react";
import WhatsappTextFormatter from "../../WhatsappTextFormatter";

interface WhatsAppMessagePreviewProps {
    imageUrl?: string;
    bodyText: string;
    buttons?: string[];
}

export default function WhatsAppMessagePreview({
    imageUrl,
    bodyText,
    buttons,
}: WhatsAppMessagePreviewProps) {
    return (
        <div className="max-w-md">
            <div className="bg-white-full shadow-sm overflow-hidden">
                {imageUrl && (
                    <div className="relative bg-gray-100">
                        <img
                            src={imageUrl || "/placeholder.svg"}
                            alt="Preview image"
                            className="w-full h-auto object-contain aspect-square"
                        />
                    </div>
                )}

                <div className="p-4">
                    <div className="space-y-4">
                        <WhatsappTextFormatter
                            text={bodyText}
                            className="text-[15px] text-gray-800 whitespace-pre-line"
                        />

                        {buttons && buttons.length > 0 && (
                            <div className="pt-2 border-t">
                                {buttons.map((button, index) => (
                                    <div
                                        key={index}
                                        className="w-full flex items-center justify-center"
                                    >
                                        <button className="flex items-center gap-2 text-center text-blue-500 font-medium px-6 py-2 hover:bg-blue-50">
                                            <LinkSquare className="w-4 h-4" />
                                            {button}
                                        </button>
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>

                    <div className="text-right mt-2">
                        <span className="text-xs text-gray-500">
                            {new Date().toLocaleString().split(",")[1]}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    );
}
