import React from "react";

interface WhatsappTextFormatterProps {
    text: string;
    className?: string;
}

/**
 * Componente para formatear texto con el formato de WhatsApp:
 * - *texto* -> negrita
 * - _texto_ -> cursiva
 * - ~texto~ -> tachado
 */
export default function WhatsappTextFormatter({
    text,
    className = "",
}: WhatsappTextFormatterProps) {
    const formatText = (inputText: string): React.ReactNode[] => {
        if (!inputText) return [];

        const parts: React.ReactNode[] = [];
        let currentIndex = 0;
        let keyCounter = 0;

        const formatRegex =
            /((?:^|[\s\p{P}])(\*[^*{}\s][^*{}]*[^*{}\s]\*|_[^_{}\s][^_{}]*[^_{}\s]_|~[^~{}\s][^~{}]*[^~{}\s]~))(?=[\s\p{P}]|$)/gu;
        let match;

        while ((match = formatRegex.exec(inputText)) !== null) {
            const fullMatch = match[1]; // El match completo incluyendo el carácter anterior
            const formatMatch = match[2]; // Solo la parte del formato
            const matchStart = match.index;

            // Determinar si hay un carácter antes del formato
            const beforeChar =
                fullMatch.length > formatMatch.length ? fullMatch[0] : "";
            const actualFormatStart = matchStart + beforeChar.length;

            // Añadir texto antes del match
            if (actualFormatStart > currentIndex) {
                const beforeText = inputText.slice(currentIndex, actualFormatStart);
                parts.push(<span key={`text-${keyCounter++}`}>{beforeText}</span>);
            }

            // Procesar el texto formateado
            const innerText = formatMatch.slice(1, -1); // Remover los caracteres de formato
            const formatChar = formatMatch[0];

            switch (formatChar) {
                case "*":
                    parts.push(
                        <strong key={`bold-${keyCounter++}`} className="font-bold">
                            {innerText}
                        </strong>,
                    );
                    break;
                case "_":
                    parts.push(
                        <em key={`italic-${keyCounter++}`} className="italic">
                            {innerText}
                        </em>,
                    );
                    break;
                case "~":
                    parts.push(
                        <span
                            key={`strikethrough-${keyCounter++}`}
                            className="line-through"
                        >
                            {innerText}
                        </span>,
                    );
                    break;
                default:
                    parts.push(
                        <span key={`default-${keyCounter++}`}>{formatMatch}</span>,
                    );
            }

            currentIndex = actualFormatStart + formatMatch.length;
        }

        // Añadir texto restante
        if (currentIndex < inputText.length) {
            const remainingText = inputText.slice(currentIndex);
            parts.push(<span key={`remaining-${keyCounter++}`}>{remainingText}</span>);
        }

        return parts;
    };

    // Dividir por saltos de línea y procesar cada línea
    const lines = text.split("\n");

    return (
        <div className={className}>
            {lines.map((line, lineIndex) => (
                <React.Fragment key={`line-${lineIndex}`}>
                    {lineIndex > 0 && <br />}
                    {formatText(line)}
                </React.Fragment>
            ))}
        </div>
    );
}
