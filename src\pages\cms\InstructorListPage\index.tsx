import { useMemo, useState } from "react";
import { Link, useNavigate, useSearchParams } from "react-router-dom";
import {
    Button,
    Checkbox,
    ConfigProvider,
    Dropdown,
    Empty,
    Form,
    Input,
    message,
    Modal,
    Pagination,
    Popover,
    Table,
    Tag,
    Typography,
    Upload,
} from "antd";
import ImgCrop from "antd-img-crop";
import { useMutation, useQuery } from "@tanstack/react-query";
import type { TableProps, UploadFile } from "antd";

const { Text } = Typography;
const { Dragger } = Upload;

import WelcomeBar from "@components/shared/molecules/WelcomeBar";

import Trash from "@assets/icons/huge/trash-white.svg?react";
import Plus from "@assets/icons/huge/plus-white.svg?react";
import Import from "@assets/icons/huge/import.svg?react";
import DeleteStroke from "@assets/icons/huge/delete-stroke.svg?react";
import EditStroke from "@assets/icons/huge/edit-stroke.svg?react";
import MoreVertical from "@assets/icons/huge/more-vertical.svg?react";
import Settings from "@assets/icons/huge/settings.svg?react";
import Reload from "@assets/icons/huge/reload.svg?react";

import CloudUpload from "@assets/shapes/cloud-upload.svg?react";

import {
    bulkDeleteInstructors,
    createInstructor,
    deleteInstructor,
    getInstructors,
} from "@services/portals/cms/instructor";
import { formatDateTime } from "@lib/helpers";
import { Instructor, CreateInstructor } from "@myTypes/instructor";
import { onErrorMessage, onSuccessMessage } from "@lib/message";
import { AxiosError } from "axios";
import Spinner from "@components/shared/atoms/Spinner";
import CmsLayout from "@layouts/cms/CmsLayout";
import SearchText from "@components/shared/atoms/SearchText";
import Filters from "@components/shared/atoms/Filter";
import { filters, InstructorPanelFilterSchema } from "./filters";
import { dayjsUtils } from "@lib/dayjs-helpers";

const INITIAL_COLUMNS: TableProps<Instructor>["columns"] = [
    {
        title: "NOMBRE COMPLETO",
        dataIndex: "fullName",
        key: "fullName",
        render: (fullName, record) => (
            <Link
                to={`${record.iid}/edit`}
                className="text-blue-full font-semibold underline"
            >
                {fullName}
            </Link>
        ),
    },
    {
        title: "TÍTULO",
        dataIndex: "title",
        key: "title",
    },
    {
        title: "ESTADO",
        dataIndex: "status",
        key: "status",
        render: (status: string) => {
            return (
                <Tag
                    bordered={false}
                    color={status === "Draft" ? "processing" : "success"}
                >
                    {status}
                </Tag>
            );
        },
    },
    {
        title: "ORDEN",
        dataIndex: "order",
        key: "order",
    },
    {
        title: "FECHA DE ACTUALIZACIÓN",
        dataIndex: "updatedAt",
        key: "updatedAt",
        render: (updatedAt: string) => {
            const formattedDate = formatDateTime(updatedAt);
            return <Text>{formattedDate}</Text>;
        },
    },
    {
        title: "FECHA DE CREACIÓN",
        dataIndex: "createdAt",
        key: "createdAt",
        render: (createdAt: string) => {
            const formattedDate = formatDateTime(createdAt);
            return <Text>{formattedDate}</Text>;
        },
    },
];

const INITIAL_CHECKED_VALUES = ["fullName", "title", "status", "order", "createdAt"];

const COLUMN_OPTIONS = [
    {
        label: "Nombre Completo",
        value: "fullName",
    },
    {
        label: "Título",
        value: "title",
    },
    {
        label: "Estado",
        value: "status",
    },
    {
        label: "Orden",
        value: "order",
    },
    {
        label: "Fecha de Actualización",
        value: "updatedAt",
    },
    {
        label: "Fecha de Creación",
        value: "createdAt",
    },
];

const PROFILE_IMAGE_WIDTH = 276;
const PROFILE_IMAGE_HEIGHT = 386;

export default function InstructorListPage() {
    const navigate = useNavigate();
    const [messageApi, messageContextHolder] = message.useMessage();
    const [searchParams, setSearchParams] = useSearchParams();
    const DEFAULT_PAGE = searchParams.get("page")
        ? Number(searchParams.get("page"))
        : 1;

    const [currentPage, setCurrentPage] = useState(DEFAULT_PAGE);
    const [checkedValues, setCheckedValues] = useState(INITIAL_CHECKED_VALUES);
    const [modalOpen, setModalOpen] = useState(false);
    const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

    const [addForm] = Form.useForm();

    const { isLoading, data, refetch } = useQuery({
        queryKey: ["instructors", currentPage, searchParams.toString()],
        queryFn: async () =>
            getInstructors({
                page: currentPage,
                filters: Object.fromEntries(searchParams.entries()),
            }),
    });

    const addMutation = useMutation({
        mutationFn: (createInstructorPayload: CreateInstructor) =>
            createInstructor(createInstructorPayload),
        onSuccess: () => {
            onSuccessMessage("Instructor creado exitosamente", messageApi);
            setModalOpen(false);
            addForm.resetFields();
            refetch();
        },
        onError: (error: AxiosError) => {
            onErrorMessage(error.message, messageApi);
        },
    });

    const deleteMutation = useMutation({
        mutationFn: (iid: string) => deleteInstructor(iid),
        onSuccess: () => {
            onSuccessMessage("Instructor eliminado exitosamente", messageApi);
            refetch();
        },
        onError: (error: AxiosError) => {
            onErrorMessage(error.message, messageApi);
        },
    });

    const bulkDeleteMutation = useMutation({
        mutationFn: (ids: number[]) => bulkDeleteInstructors(ids),
        onSuccess: () => {
            onSuccessMessage("Instructores eliminados exitosamente", messageApi);
            setSelectedRowKeys([]);
            refetch();
        },
        onError: (error: AxiosError) => {
            onErrorMessage(error.message, messageApi);
        },
    });

    const tableColumns = useMemo(() => {
        return (INITIAL_COLUMNS ?? []).filter((column) => {
            return checkedValues.includes(column.key as string);
        });
    }, [checkedValues]);

    const TOTAL_COUNT = data?.count;
    const PAGE_SIZE = 10;
    const instructors = data?.results;

    const defaultColumn = {
        title: "ACCIONES",
        key: "actions",
        render: (record: Instructor) => (
            <Dropdown
                trigger={["click"]}
                menu={{
                    items: [
                        {
                            key: "edit",
                            label: (
                                <>
                                    <div className="flex items-center gap-2 text-blue-full">
                                        <EditStroke className="w-5 h-5" /> Editar
                                    </div>
                                </>
                            ),
                        },
                        {
                            key: "delete",
                            label: (
                                <>
                                    <div className="flex items-center gap-2 text-state-red-full">
                                        <DeleteStroke className="w-5 h-5" /> Eliminar
                                    </div>
                                </>
                            ),
                        },
                    ],
                    onClick: ({ key }) => {
                        handleRowAction(key, record);
                    },
                }}
                placement="bottomRight"
            >
                <Button
                    icon={<MoreVertical className="w-5 h-5" />}
                    type="text"
                    size="small"
                />
            </Dropdown>
        ),
    };

    const handleRowAction = (key: string, record: Instructor) => {
        if (key === "edit") {
            navigate(`/cms/instructor/${record.iid}/edit`);
        } else if (key === "delete") {
            const instructorId = record.iid;
            deleteMutation.mutate(instructorId);
        }
    };

    const handleShowHideColumns = (checkedValues: string[]) => {
        setCheckedValues(checkedValues);
    };

    const handlePageChange = (newPage: number) => {
        setSearchParams({ page: newPage.toString() });
        setCurrentPage(newPage);
    };

    const handleReloadData = () => {
        refetch();
    };
    const handleFormFinish = (values: CreateInstructor) => {
        addMutation.mutate(values);
    };
    const handleOnSelectChange = (selectedRowKeys: React.Key[]) => {
        setSelectedRowKeys(selectedRowKeys);
    };
    const handleMultiDelete = () => {
        bulkDeleteMutation.mutate(selectedRowKeys as number[]);
    };
    const hasSelected = selectedRowKeys.length > 0;

    return (
        <>
            {messageContextHolder}
            <CmsLayout>
                <div className="w-full h-full space-y-5">
                    <div className="flex justify-between items-center">
                        <WelcomeBar helperText="Gestiona aquí los instructores que se visualizarán en la Website" />
                        <div className="flex gap-3">
                            <Button
                                size="large"
                                style={{ fontSize: 16 }}
                                icon={<Import />}
                            >
                                Importar
                            </Button>
                            <Button
                                type="primary"
                                size="large"
                                style={{ fontSize: 16 }}
                                icon={<Plus />}
                                onClick={() => {
                                    setModalOpen(true);
                                }}
                            >
                                Agregar
                            </Button>
                            <Modal
                                centered
                                open={modalOpen}
                                onCancel={() => setModalOpen(false)}
                                footer={false}
                                title={
                                    <div className="w-full flex justify-center text-2xl py-4">
                                        Agregar nuevo Instructor
                                    </div>
                                }
                            >
                                <Form
                                    name="instructor"
                                    layout="vertical"
                                    form={addForm}
                                    onFinish={handleFormFinish}
                                >
                                    <div className="grid grid-cols-1 gap-4">
                                        <Form.Item<CreateInstructor>
                                            name="fullName"
                                            label={
                                                <span className="font-semibold">
                                                    Nombres completos
                                                </span>
                                            }
                                            rules={[
                                                {
                                                    required: true,
                                                    message:
                                                        "Por favor ingrese los nombres completos",
                                                },
                                            ]}
                                        >
                                            <Input
                                                placeholder="Ej. Juan Pablo"
                                                className="py-1"
                                            />
                                        </Form.Item>
                                    </div>
                                    <Form.Item<CreateInstructor>
                                        name="title"
                                        label={
                                            <span className="font-semibold">
                                                Título
                                            </span>
                                        }
                                        rules={[
                                            {
                                                required: true,
                                                message: "Por favor, ingrese el título",
                                            },
                                        ]}
                                    >
                                        <Input
                                            type="text"
                                            placeholder="Ej. Ing. Economista"
                                            className="py-1"
                                        />
                                    </Form.Item>
                                    <Form.Item<CreateInstructor>
                                        name="biography"
                                        label={
                                            <span className="font-semibold">
                                                Biografía
                                            </span>
                                        }
                                        rules={[
                                            {
                                                required: true,
                                                message:
                                                    "Por favor, ingrese la biografía",
                                            },
                                        ]}
                                    >
                                        <Input.TextArea className="py-1" />
                                    </Form.Item>
                                    <Form.Item<CreateInstructor>
                                        name="highlightedInfo"
                                        label={
                                            <span className="font-semibold">
                                                Información relevante
                                            </span>
                                        }
                                        rules={[
                                            {
                                                required: true,
                                                message:
                                                    "Por favor, ingrese la información relevante",
                                            },
                                        ]}
                                    >
                                        <Input
                                            type="text"
                                            placeholder="Ej. Gerente general de la empresa Y"
                                            className="py-1"
                                        />
                                    </Form.Item>
                                    <Form.Item<CreateInstructor>
                                        name="profilePhotoFile"
                                        label={
                                            <span className="font-semibold">
                                                Imagen de perfil
                                            </span>
                                        }
                                        rules={[
                                            {
                                                required: true,
                                                message: "Por favor, sube una imagen",
                                            },
                                        ]}
                                        valuePropName="fileList"
                                        getValueFromEvent={(e) => {
                                            if (Array.isArray(e)) {
                                                return e;
                                            }
                                            return e && e.fileList;
                                        }}
                                    >
                                        <ImgCrop
                                            aspect={
                                                PROFILE_IMAGE_WIDTH /
                                                PROFILE_IMAGE_HEIGHT
                                            }
                                            showGrid={true}
                                            modalTitle="Editar imagen de perfil"
                                            modalOk="Guardar"
                                            modalCancel="Cancelar"
                                        >
                                            <Dragger
                                                maxCount={1}
                                                multiple={false}
                                                listType="picture"
                                                customRequest={async (options) => {
                                                    const { file, onSuccess, onError } =
                                                        options;
                                                    try {
                                                        typeof onSuccess ===
                                                            "function" &&
                                                            onSuccess(file);
                                                        addForm.setFieldsValue({
                                                            profilePhotoFile: [
                                                                file as UploadFile,
                                                            ],
                                                        });
                                                    } catch (error) {
                                                        typeof onError === "function" &&
                                                            onError(error as Error);
                                                    }
                                                }}
                                                onRemove={() => {
                                                    addForm.setFieldsValue({
                                                        profilePhotoFile: [],
                                                    });
                                                }}
                                            >
                                                <div className="flex flex-col justify-center items-center">
                                                    <CloudUpload />
                                                    <Text className="font-medium text-black-full">
                                                        Arrastre una imagen de perfil o
                                                        haga click aquí
                                                    </Text>
                                                    <Text className="text-xs text-black-medium">
                                                        Este campo admite formatos de
                                                        imagen. Solo una imagen
                                                    </Text>
                                                </div>
                                            </Dragger>
                                        </ImgCrop>
                                    </Form.Item>
                                    <div className="grid grid-cols-2 gap-2 items-end">
                                        <Button
                                            onClick={() => setModalOpen(false)}
                                            className="h-fit"
                                            size="large"
                                        >
                                            Cancelar
                                        </Button>
                                        <Button
                                            type="primary"
                                            htmlType="submit"
                                            className="h-fit"
                                            size="large"
                                            block
                                        >
                                            Guardar
                                        </Button>
                                    </div>
                                </Form>
                            </Modal>
                        </div>
                    </div>
                    <div className="p-5 bg-white-full rounded-lg space-y-5">
                        <div className="flex justify-between items-center">
                            <Text className="text-black-medium text-2xl font-semibold">
                                Instructores
                            </Text>

                            <div className="flex items-center gap-3">
                                {/* Table actions bar */}
                                <SearchText />
                                <Button
                                    icon={<Reload />}
                                    size="large"
                                    type="text"
                                    onClick={handleReloadData}
                                />
                                <Filters<InstructorPanelFilterSchema>
                                    filters={filters}
                                    initialValues={{
                                        ...Object.fromEntries(searchParams.entries()),
                                        createdAt: [
                                            dayjsUtils.format(
                                                searchParams.get("createdAtAfter") ??
                                                    "",
                                            ),
                                            dayjsUtils.format(
                                                searchParams.get("createdAtBefore") ??
                                                    "",
                                            ),
                                        ],
                                    }}
                                />
                                <Popover
                                    content={
                                        <div className="p-2 space-y-3">
                                            <div className="uppercase text-black-medium font-medium">
                                                Mostrar/Ocultar Columnas
                                            </div>
                                            <div className="px-2">
                                                <Checkbox.Group
                                                    defaultValue={
                                                        INITIAL_CHECKED_VALUES
                                                    }
                                                    onChange={handleShowHideColumns}
                                                    name="columns"
                                                    className="flex flex-col gap-1"
                                                    options={COLUMN_OPTIONS}
                                                />
                                            </div>
                                        </div>
                                    }
                                    trigger={["click"]}
                                    placement="bottomRight"
                                >
                                    <Button
                                        icon={<Settings />}
                                        size="large"
                                        type="text"
                                    />
                                </Popover>
                            </div>
                        </div>
                        <ConfigProvider
                            theme={{
                                components: {
                                    Table: {
                                        headerBg: "#FBFCFD",
                                        borderColor: "#fff",
                                        headerSplitColor: "#fafafa",
                                        headerBorderRadius: 8,
                                        rowHoverBg: "#F6FAFD",
                                        rowSelectedBg: "#F6FAFD",
                                        rowSelectedHoverBg: "#F6FAFD",
                                        footerBg: "#F1F1F1",
                                    },
                                },
                            }}
                        >
                            <Table
                                rowSelection={{
                                    type: "checkbox",
                                    onChange: handleOnSelectChange,
                                    selectedRowKeys,
                                }}
                                columns={
                                    tableColumns ? [...tableColumns, defaultColumn] : []
                                }
                                locale={{
                                    emptyText: (
                                        <>{isLoading ? <Spinner /> : <Empty />}</>
                                    ),
                                }}
                                dataSource={instructors}
                                className="rounded-lg"
                                footer={() => ""}
                                pagination={false}
                            />
                            <div className="flex justify-between">
                                <div className="flex items-center gap-3">
                                    <Button
                                        danger
                                        disabled={!hasSelected}
                                        type="primary"
                                        size="large"
                                        icon={<Trash />}
                                        onClick={handleMultiDelete}
                                    >
                                        Eliminar
                                    </Button>
                                </div>
                                <div>
                                    <Pagination
                                        defaultCurrent={DEFAULT_PAGE}
                                        total={TOTAL_COUNT}
                                        pageSize={PAGE_SIZE}
                                        onChange={handlePageChange}
                                    />
                                </div>
                            </div>
                        </ConfigProvider>
                    </div>
                </div>
            </CmsLayout>
        </>
    );
}
