import { useQuery } from "@tanstack/react-query";
import { listTemplateVariable } from "../services/portals/template-variable";
import { ListTemplateVariableQuery } from "../types/template-variable";

export type UseTemplateVariableQuery = ListTemplateVariableQuery;

type UseTemplateVariablesProps = {
    page?: number;
    pageSize?: number;
    query?: ListTemplateVariableQuery;
    enabled?: boolean;
};

export const useTemplateVariables = ({
    query,
    enabled = true,
    ...rest
}: UseTemplateVariablesProps = {}) => {
    const { data, isLoading, isError, isFetching, refetch } = useQuery({
        queryKey: ["template-variables", query, rest],
        queryFn: () =>
            listTemplateVariable({
                ...query,
                ...rest,
            }),
        enabled,
        refetchOnWindowFocus: false,
    });

    const { count: COUNT, results: templateVariables } = data || {
        count: 0,
        results: [],
    };

    return {
        refetch,
        isLoading: isLoading || isFetching,
        isError,
        templateVariables,
        COUNT,
    };
};
