import Header from "@components/shared/molecules/Header";
import LmsSideBar from "@/features/lms/components/molecules/lms-sidebar";
import { useEffect } from "react";

type LmsLayoutProps = {
    children?: React.ReactNode;
};
export default function LmsLayout({ children }: LmsLayoutProps) {
    useEffect(() => {
        document.title = "LMS - Learning Management System";
    }, []);
    return (
        <div>
            <Header />
            <div className="flex">
                <LmsSideBar />
                <div className="bg-blue-low h-[calc(100vh-140px)] w-full rounded-tl-lg ml-64 p-6 overflow-auto flex justify-center">
                    {children}
                </div>
            </div>
        </div>
    );
}
