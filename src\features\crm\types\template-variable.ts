export type ListTemplateVariableQuery = Partial<{
    search: string;
    templateType: string;
}>;

export type ListTemplateVariableParams = Partial<{
    page: number;
    pageSize: number;
}> &
    ListTemplateVariableQuery;

type AuditBaseType = {
    createdAt: string;
    updatedAt: string;
};

export type TemplateVariable = {
    tvid: string;
    name: string;
    example: string;
    description: string;
    dataType: string;
    dataFormat: string;
} & AuditBaseType;

export type RetrieveTemplateVariable = TemplateVariable;
