export type PermissionAction = "view" | "add" | "change" | "delete";

// Submódulos organizados como objeto
export const CRM_PERMISSIONS_MAP = {
    "crm.dashboard.events": "crm_dashboard_events",
    "crm.dashboard.sales": "crm_dashboard_sales",
    "crm.dashboard.payments": "crm_dashboard_payments",
    "crm.dashboard.contacts": "crm_dashboard_contacts",
    "crm.user": "user", // contacts
    "crm.activity": "activity",
    "crm.order": "order",
    "crm.payment": "payment",
    "crm.event": "event",
    "crm.eventschedule": "eventschedule",
    "crm.template": "template",
    "crm.major": "major",
};

export const CMS_PERMISSIONS_MAP = {
    "cms.instructor": "instructor",
    "cms.testimonial": "testimonial",
    "cms.offering": "offering",
    "cms.blog": "blogpost",
    "cms.blogcategory": "blogcategory",
    "cms.blogtag": "blogtag",
};

export const LMS_PERMISSIONS_MAP = {
    "lms.dashboard": "dashboard",
    "lms.enrollment": "enrollment",
    "lms.program": "offering",
};

// Combinar todos los sub-módulos
export const ALL_PERMISSIONS_MAP = {
    ...CRM_PERMISSIONS_MAP,
    ...CMS_PERMISSIONS_MAP,
    ...LMS_PERMISSIONS_MAP,
};

// Para obtener un array de submódulos:
export const PERMISSIONS_MAP = Object.values(ALL_PERMISSIONS_MAP);
export type PermissionKey = keyof typeof ALL_PERMISSIONS_MAP;
