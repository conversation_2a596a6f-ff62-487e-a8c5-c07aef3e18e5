import React from "react";
import { Card, Empty } from "antd";
import {
    ResponsiveContainer,
    BarChart,
    Bar,
    XAxis,
    YAxis,
    Cell,
    Tooltip,
} from "recharts";

interface HorizontalBarChartCardProps {
    title: string;
    data: Array<Record<string, unknown>>;
    valueKey?: string;
    categoryKey?: string;
    colors?: string[];
    icon?: React.ReactNode;
    formatter?: (value: number) => string;
    height?: number;
}

const getColor = (length: number, index: number, colors: string[]) => {
    if (length <= colors.length) {
        return colors[index];
    }
    return colors[index % colors.length];
};

const HorizontalBarChartCard: React.FC<HorizontalBarChartCardProps> = ({
    title,
    data,
    valueKey = "value",
    categoryKey = "name",
    colors = ["#1890ff", "#faad14"],
    icon,
    formatter,
    height,
}) => {
    // Calcular la altura dinámica basada en la cantidad de elementos
    const chartHeight = height || Math.max(60 * data.length, 250);

    // Componente personalizado para el tooltip
    interface CustomTooltipProps {
        active?: boolean;
        payload?: Array<{
            value: number;
            dataKey: string;
            name: string;
            color: string;
        }>;
        label?: string;
    }

    const CustomTooltip = ({ active, payload }: CustomTooltipProps) => {
        if (active && payload && payload.length) {
            const { value, color } = payload[0];
            return (
                <div className="bg-white-full p-2 border border-gray-200 shadow-md rounded">
                    <p className="font-medium">Cantidad</p>
                    <p style={{ color }}>{formatter ? formatter(value) : value}</p>
                </div>
            );
        }
        return null;
    };

    return (
        <Card
            title={
                <div className="flex items-center gap-2">
                    {icon}
                    <span>{title}</span>
                </div>
            }
            styles={{ body: { padding: 6, overflow: "visible" } }}
            className="shadow-md h-full"
        >
            {data.length ? (
                <div className="max-h-[300px] overflow-y-auto">
                    <div
                        style={{
                            height: chartHeight,
                            width: "100%",
                            overflowY: "auto",
                        }}
                    >
                        <ResponsiveContainer width="100%" height="100%" debounce={50}>
                            <BarChart
                                data={data}
                                layout="vertical"
                                margin={{
                                    left: 0,
                                    top: 0,
                                    bottom: 0,
                                    right: 0,
                                }}
                            >
                                <XAxis hide axisLine={false} type="number" />

                                <YAxis
                                    yAxisId={0}
                                    dataKey={categoryKey}
                                    type="category"
                                    axisLine={false}
                                    tickLine={false}
                                    width={150}
                                    tick={{ fontSize: 11, width: 180 }}
                                    interval={0}
                                />

                                <YAxis
                                    orientation="right"
                                    yAxisId={1}
                                    dataKey={valueKey}
                                    type="category"
                                    axisLine={false}
                                    tickLine={false}
                                    tickFormatter={formatter}
                                />
                                <Tooltip content={<CustomTooltip />} />
                                <Bar dataKey={valueKey} minPointSize={2} barSize={30}>
                                    {data.map((_, idx) => (
                                        <Cell
                                            key={`cell-${idx}`}
                                            fill={getColor(data.length, idx, colors)}
                                        />
                                    ))}
                                </Bar>
                            </BarChart>
                        </ResponsiveContainer>
                    </div>
                </div>
            ) : (
                <div className="grid place-content-center h-[300px]">
                    <Empty />
                </div>
            )}
        </Card>
    );
};

export default HorizontalBarChartCard;
