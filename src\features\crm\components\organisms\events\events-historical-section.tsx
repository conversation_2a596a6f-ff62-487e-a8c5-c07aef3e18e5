import React from "react";
import type { DashboardEventQueryParams } from "@/features/crm/types/dashboard/events";
import { useDashboardEventsHistorical } from "@/features/crm/hooks/use-dashboard-events";
import EventsHistoricalChart from "@/features/crm/components/molecules/events/events-historical-chart";
import { Row, Col, Card, Button } from "antd";
import { useDashboardLaunchedEvents } from "@/features/crm/hooks/use-dashboard-events";
import { Clock } from "lucide-react";
import LaunchedEventsCarousel from "../../molecules/events/launched-events-carousel";
import { useNavigate } from "react-router-dom";

interface EventsHistoricalSectionProps {
    queryParams: DashboardEventQueryParams;
}

const EventsHistoricalSection: React.FC<EventsHistoricalSectionProps> = ({
    queryParams,
}) => {
    const { data: historicalData, isLoading } =
        useDashboardEventsHistorical(queryParams);
    const navigate = useNavigate();
    const { data: launchedEvents } = useDashboardLaunchedEvents(queryParams);

    return (
        <div className="space-y-6">
            {/* Charts Row */}
            <Row gutter={[16, 16]}>
                <Col xs={24} xl={14}>
                    <EventsHistoricalChart
                        data={historicalData}
                        isLoading={isLoading}
                    />
                </Col>
                <Col xs={24} xl={10}>
                    <Card
                        title={
                            <div className="flex items-center gap-2">
                                <Clock size={20} className="text-blue-500" />
                                <span>Eventos Activos</span>
                            </div>
                        }
                        extra={
                            <Button
                                type="link"
                                onClick={() => navigate("/crm/event-schedules")}
                            >
                                Ver todos
                            </Button>
                        }
                        styles={{
                            body: {
                                padding: 0,
                            },
                        }}
                    >
                        <Row gutter={[16, 16]}>
                            <Col xs={24}>
                                <LaunchedEventsCarousel data={launchedEvents} />
                            </Col>
                        </Row>
                    </Card>
                </Col>
            </Row>
        </div>
    );
};

export default EventsHistoricalSection;
