import { BroadcastMessagePipelineState } from "@myTypes/broadcast-message";
import { Sale } from "@myTypes/sales-pipeline";
import { portalsApi } from "@services/portals";

interface GetSalesPipelineParams {
    offeringId?: number | null;
    page?: number;
    pageSize?: number;
}

interface PaginatedSalesPipeline {
    data: Sale[];
    meta: {
        total: number;
        page: number;
        pageSize: number;
        totalPages: number;
    };
}

export const getSoldSales = async ({
    offeringId,
    page,
    pageSize,
}: GetSalesPipelineParams): Promise<PaginatedSalesPipeline> => {
    const response = await portalsApi.get("crm/sales-pipeline/sold", {
        params: {
            offeringId: offeringId ?? undefined,
            page: page ?? undefined,
            pageSize: pageSize ?? undefined,
        },
    });
    return response.data;
};

export const getProspectSales = async ({
    offeringId,
    page,
    pageSize,
}: GetSalesPipelineParams): Promise<PaginatedSalesPipeline> => {
    const response = await portalsApi.get("crm/sales-pipeline/prospect", {
        params: {
            offeringId: offeringId ?? undefined,
            page: page ?? undefined,
            pageSize: pageSize ?? undefined,
        },
    });
    return response.data;
};

export const getInterestedSales = async ({
    offeringId,
    page,
    pageSize,
}: GetSalesPipelineParams): Promise<PaginatedSalesPipeline> => {
    const response = await portalsApi.get("crm/sales-pipeline/interested", {
        params: {
            offeringId: offeringId ?? undefined,
            page: page ?? undefined,
            pageSize: pageSize ?? undefined,
        },
    });
    return response.data;
};

export const getPendingPaymentSales = async ({
    offeringId,
    page,
    pageSize,
}: GetSalesPipelineParams): Promise<PaginatedSalesPipeline> => {
    const response = await portalsApi.get("crm/sales-pipeline/pending-payment", {
        params: {
            offeringId: offeringId ?? undefined,
            page: page ?? undefined,
            pageSize: pageSize ?? undefined,
        },
    });
    return response.data;
};

export const getSalesByPipelineSate = async ({
    offeringId,
    page,
    pageSize,
    pipelineState,
}: GetSalesPipelineParams & {
    pipelineState: BroadcastMessagePipelineState;
}): Promise<PaginatedSalesPipeline> => {
    switch (pipelineState) {
        case BroadcastMessagePipelineState.SOLD:
            return getSoldSales({ offeringId, page, pageSize });
        case BroadcastMessagePipelineState.PROSPECT:
            return getProspectSales({ offeringId, page, pageSize });
        case BroadcastMessagePipelineState.LEAD:
            return getInterestedSales({ offeringId, page, pageSize });
        case BroadcastMessagePipelineState.PENDING_PAYMENT:
            return getPendingPaymentSales({ offeringId, page, pageSize });
        default:
            throw new Error("Invalid pipeline state");
    }
};
