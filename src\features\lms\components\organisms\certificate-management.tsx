import { useState, useMemo } from "react";
import {
    App,
    Button,
    Upload,
    Typography,
    Space,
    Modal,
    Input,
    Form,
    Alert,
} from "antd";
import {
    Award,
    Mail,
    Upload as UploadIcon,
    Calendar,
    FileText,
    FileUp,
    Copy,
    ExternalLink,
} from "lucide-react";
import { EnrollmentRetrieve } from "../../types/enrollment";
import CertificateViewer from "../molecules/certificate-viewer";
import { formatDateTime } from "@lib/helpers";
import ChangeCertificateIssuedDate from "../atoms/change-certificate-issued-date";
import { useMutation } from "@tanstack/react-query";
import { attachCertificateToEnrollment } from "../../services/portals/enrollment";
import UploadCredentialStatus from "../molecules/upload-credential-status";
import queryClient from "@lib/queryClient";

const { Text, Paragraph } = Typography;
const { TextArea } = Input;

interface CertificateManagementProps {
    enrollment: EnrollmentRetrieve;
}

interface EmailFormValues {
    email: string;
    message?: string;
}

export default function CertificateManagement({
    enrollment,
}: CertificateManagementProps) {
    const { message } = App.useApp();
    const [emailModalOpen, setEmailModalOpen] = useState(false);
    const [uploadedFile, setUploadedFile] = useState<File | null>(null);

    const uploadedFileUrl = useMemo(
        () => (uploadedFile ? URL.createObjectURL(uploadedFile) : null),
        [uploadedFile],
    );
    const [form] = Form.useForm<EmailFormValues>();

    const handleGenerateCertificate = () => {
        message.success("Certificado generado exitosamente");
        // Here you would typically call an API to generate the certificate
        // and then update the enrollment data.
    };

    const handleSendEmail = (values: EmailFormValues) => {
        message.success(`Certificado enviado a ${values.email}`);
        setEmailModalOpen(false);
        form.resetFields();
    };

    const {
        data,
        mutate: attachCertificate,
        isPending: isPendingAttaching,
    } = useMutation({
        mutationFn: ({ eid, file }: { eid: string; file: File }) =>
            attachCertificateToEnrollment(eid, file),
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: ["enrollments", enrollment.eid],
            });
            message.success("Certificado cargado, empezando a procesar...");
        },
        onError: () => {
            message.error("Error al adjuntar el certificado a la matrícula");
        },
    });

    const handleUpload = (file: File) => {
        setUploadedFile(file);
        attachCertificate({ eid: enrollment.eid, file });
        return false; // Prevent automatic upload
    };

    const certificateExists: boolean =
        enrollment.certificateIssued && enrollment.certificate !== undefined;
    const certificateUrl = certificateExists
        ? enrollment.certificate?.file.url
        : uploadedFileUrl;

    return (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 h-full">
            {/* Left Column: Management */}
            <div className="space-y-6">
                {/* Certificate Status Overview */}
                <div className="bg-white-full p-5 rounded-lg shadow-sm">
                    <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center gap-3">
                            <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                                <Award size={20} className="text-purple-600" />
                            </div>
                            <div>
                                <p className="text-gray-400 font-semibold text-sm">
                                    ESTADO DEL CERTIFICADO
                                </p>
                            </div>
                        </div>
                        <UploadCredentialStatus
                            certificateExists={certificateExists}
                            taskId={data?.taskId}
                        />
                    </div>

                    {certificateExists || uploadedFile ? (
                        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                            <div className="flex items-start justify-between">
                                <div className="space-y-2">
                                    <Text className="font-medium text-green-900 text-sm">
                                        Certificado de Finalización
                                    </Text>
                                    <div className="flex items-center gap-2 text-green-700">
                                        <Calendar size={12} />
                                        <Text className="text-xs">
                                            {certificateExists
                                                ? `Emitido: ${formatDateTime(enrollment.certificate?.issuedAt)}`
                                                : "Subido recientemente"}
                                        </Text>
                                        <ChangeCertificateIssuedDate
                                            cid={enrollment.certificate?.cid}
                                            initialIssuedAt={
                                                enrollment.certificate?.issuedAt
                                            }
                                        />
                                    </div>
                                </div>
                                <Space>
                                    <Button
                                        icon={<Copy size={10} />}
                                        onClick={() => {
                                            const url =
                                                enrollment.certificate?.verificationUrl;
                                            if (url) {
                                                navigator.clipboard.writeText(url);
                                                message.success(
                                                    "URL de verificación copiada al portapapeles",
                                                );
                                            }
                                        }}
                                        size="small"
                                    >
                                        Copiar Enlace
                                    </Button>
                                    <Button
                                        type="primary"
                                        icon={<ExternalLink size={10} />}
                                        onClick={() => {
                                            const url =
                                                enrollment.certificate?.verificationUrl;
                                            if (url) {
                                                window.open(url, "_blank");
                                            }
                                        }}
                                        size="small"
                                    >
                                        Abrir Verificación
                                    </Button>
                                </Space>
                            </div>
                        </div>
                    ) : (
                        <Alert
                            message="Certificado no disponible"
                            description="El certificado para esta matrícula aún no ha sido generado o subido."
                            type="warning"
                            showIcon
                            className="text-sm"
                        />
                    )}
                </div>

                {/* Actions */}
                <div className="bg-white-full p-5 rounded-lg shadow-sm">
                    <p className="text-gray-400 font-semibold text-sm mb-4">
                        GESTIONAR CERTIFICADO
                    </p>
                    <div className="space-y-4">
                        <div className="border border-gray-200 rounded-lg p-4">
                            <div className="flex items-start gap-4">
                                <FileText size={20} className="text-gray-500 mt-1" />
                                <div className="flex-1">
                                    <p className="font-semibold text-gray-800">
                                        Generar Certificado
                                    </p>
                                    <p className="text-gray-600 text-sm mb-3">
                                        Crear un nuevo certificado PDF basado en la
                                        plantilla del curso.
                                    </p>
                                    <Button
                                        type="primary"
                                        icon={<Award size={14} />}
                                        onClick={handleGenerateCertificate}
                                        disabled
                                    >
                                        Generar PDF
                                    </Button>
                                </div>
                            </div>
                        </div>

                        <div className="border border-gray-200 rounded-lg p-4">
                            <div className="flex items-start gap-4">
                                <FileUp size={20} className="text-gray-500 mt-1" />
                                <div className="flex-1">
                                    <p className="font-semibold text-gray-800">
                                        Subir Manualmente
                                    </p>
                                    <p className="text-gray-600 text-sm mb-3">
                                        Adjuntar un archivo PDF como certificado para
                                        esta matrícula.
                                    </p>
                                    <Upload
                                        accept=".pdf"
                                        maxCount={1}
                                        beforeUpload={handleUpload}
                                        showUploadList={false}
                                    >
                                        <Button
                                            icon={<UploadIcon size={14} />}
                                            disabled={isPendingAttaching}
                                        >
                                            Seleccionar PDF
                                        </Button>
                                    </Upload>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Send via Email */}
                <div className="bg-white-full p-5 rounded-lg shadow-sm">
                    <p className="text-gray-400 font-semibold text-sm mb-4">
                        ENVIAR POR EMAIL
                    </p>
                    <div className="space-y-2">
                        <Paragraph className="text-gray-600 text-sm">
                            Enviar el certificado directamente al estudiante por correo
                            electrónico.
                        </Paragraph>
                        <Button
                            type="default"
                            icon={<Mail size={14} />}
                            onClick={() => setEmailModalOpen(true)}
                            className="w-full"
                            disabled={!certificateUrl || true}
                            size="middle"
                        >
                            Enviar por Email
                        </Button>
                        {!certificateUrl && (
                            <Text className="text-xs text-gray-500 text-center block">
                                Se requiere un certificado para poder enviarlo.
                            </Text>
                        )}
                    </div>
                </div>
            </div>

            {/* Right Column: Preview */}
            <div className="bg-white-full p-5 rounded-lg shadow-sm flex flex-col">
                <p className="text-gray-400 font-semibold text-sm mb-4">
                    PREVISUALIZACIÓN DEL CERTIFICADO
                </p>
                <div className="flex-1 bg-gray-50 rounded-lg min-h-[400px] h-full">
                    {certificateUrl ? (
                        <CertificateViewer
                            fileUrl={certificateUrl}
                            fileName={
                                certificateExists
                                    ? "Certificado de Finalización"
                                    : "Certificado Subido"
                            }
                        />
                    ) : (
                        <div className="flex flex-col items-center justify-center h-full text-center text-gray-500 p-8">
                            <FileText size={48} className="mx-auto mb-4" />
                            <p className="font-semibold">
                                Sin previsualización disponible
                            </p>
                            <p className="text-sm">
                                Genera o sube un certificado para verlo aquí.
                            </p>
                        </div>
                    )}
                </div>
            </div>

            {/* Modals */}
            <Modal
                title="Enviar certificado por email"
                open={emailModalOpen}
                onCancel={() => setEmailModalOpen(false)}
                footer={null}
            >
                <Form form={form} onFinish={handleSendEmail} layout="vertical">
                    <Form.Item
                        name="email"
                        label="Dirección de correo"
                        initialValue={enrollment.user.email}
                        rules={[
                            {
                                required: true,
                                message: "Ingrese una dirección de correo",
                            },
                            { type: "email", message: "Ingrese un correo válido" },
                        ]}
                    >
                        <Input placeholder="<EMAIL>" />
                    </Form.Item>
                    <Form.Item name="message" label="Mensaje personalizado (opcional)">
                        <TextArea
                            rows={3}
                            placeholder="Incluir un mensaje personalizado junto al certificado..."
                        />
                    </Form.Item>
                    <Form.Item className="mb-0">
                        <Space className="w-full justify-end">
                            <Button onClick={() => setEmailModalOpen(false)}>
                                Cancelar
                            </Button>
                            <Button
                                type="primary"
                                htmlType="submit"
                                icon={<Mail size={16} />}
                            >
                                Enviar
                            </Button>
                        </Space>
                    </Form.Item>
                </Form>
            </Modal>
        </div>
    );
}
