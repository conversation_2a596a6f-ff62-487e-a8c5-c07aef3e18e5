import { PaginatedResponse } from "@myTypes/base";
import { Major } from "@/features/crm/types/major";
import { portalsApi } from "@services/portals";

export type ListMajorsQueryParams = {
    page?: number;
    pageSize?: number;
    search?: string;
    ordering?: string;
};

const DEFAULT_PAGE = 1;
const DEFAULT_PAGE_SIZE = 100;

export const getMajors = async (
    query: ListMajorsQueryParams = {},
): Promise<PaginatedResponse<Major>> => {
    const params: Record<string, string | number | undefined> = {
        page: DEFAULT_PAGE,
        pageSize: DEFAULT_PAGE_SIZE,
        ...query,
    };
    // Default alphabetical ordering by name (Django-style ordering param)
    params.ordering = query.ordering ?? "name";
    if (query.search) params.search = query.search;
    const response = await portalsApi.get("crm/majors", { params });
    return response.data;
};

export const retrieveMajor = async (mid: string): Promise<Major> => {
    const response = await portalsApi.get(`crm/majors/${mid}`);
    return response.data;
};

export const createMajor = async (name: string): Promise<Major> => {
    const response = await portalsApi.post("crm/majors", { name });
    return response.data;
};

export const updateMajor = async (
    mid: string,
    payload: { name: string },
): Promise<Major> => {
    const response = await portalsApi.patch(`crm/majors/${mid}`, payload);
    return response.data;
};

export const deleteMajor = async (mid: string): Promise<void> => {
    await portalsApi.delete(`crm/majors/${mid}`);
};
