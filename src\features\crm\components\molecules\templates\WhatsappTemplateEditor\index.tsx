import React, { useState, useEffect, useRef } from "react";
import { Input, Button, Tooltip, Typography, Space } from "antd";
import Plus from "@assets/icons/huge/plus.svg?react";
import Delete from "@assets/icons/huge/delete-stroke.svg?react";
import SelectTemplateVariables from "../../select-template-variable";
import { TemplateVariable } from "@/features/crm/types/template-variable";
import { DefaultOptionType } from "antd/es/select";
import { useTemplateVariables } from "@/features/crm/hooks/use-template-variable";
import { useDebounce } from "@hooks/use-debounce";
import { AlertTriangleIcon, Bold, Italic, Strikethrough } from "lucide-react";

const { TextArea } = Input;
const { Text } = Typography;
interface WhatsappTemplateEditorProps {
    value: string;
    onChange: (value: string) => void;
    onVariableChange?: (variables: SelectedVariable[]) => void;
    maxLength?: number;
    initialVariables?: string[];
    templateType?: string;
}
interface SelectedVariable {
    name: string;
    example: string;
    tvid: string;
}
const WhatsappTemplateEditor = ({
    value,
    onChange,
    onVariableChange,
    templateType,
    maxLength = 1024,
}: WhatsappTemplateEditorProps) => {
    const [cursorPosition, setCursorPosition] = useState(0);
    const [variables, setVariables] = useState<SelectedVariable[]>([]);
    const [selectVariableInputValue, setSelectVariableInputValue] =
        useState<string>("Agregar variable");
    const textAreaRef = useRef<HTMLTextAreaElement>(null);

    // Debounce del valor del texto para detectar variables
    const debouncedValue = useDebounce(value, 500);

    // Obtener todas las variables disponibles
    const { templateVariables } = useTemplateVariables({
        pageSize: 25,
        query: {
            templateType,
        },
    });

    const handleTextAreaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
        onChange(e.target.value);
    };

    // Detectar variables en el texto automáticamente
    useEffect(() => {
        if (!debouncedValue || !templateVariables) return;

        const variableRegex = /\{\{([^}]+)\}\}/g;
        const foundVariables: SelectedVariable[] = [];
        let match;

        while ((match = variableRegex.exec(debouncedValue)) !== null) {
            const variableName = match[1].trim();
            const templateVariable = templateVariables.find(
                (tv) => tv.name === variableName,
            );

            if (templateVariable) {
                // Verificar si ya está en la lista
                const alreadyAdded = foundVariables.some(
                    (v) => v.tvid === templateVariable.tvid,
                );
                if (!alreadyAdded) {
                    foundVariables.push({
                        name: templateVariable.name,
                        example: templateVariable.example,
                        tvid: templateVariable.tvid,
                    });
                }
            }
        }

        setVariables(foundVariables);
    }, [debouncedValue, templateVariables]);

    React.useEffect(() => {
        if (onVariableChange) {
            onVariableChange(variables);
        }
    }, [variables, onVariableChange]);

    const handleVariableSelect = (option: DefaultOptionType | undefined) => {
        if (!option?.info) return;

        const templateVariable: TemplateVariable = option.info;
        const variableName = templateVariable.name;

        const before = value.substring(0, cursorPosition);
        const after = value.substring(cursorPosition);
        const newText = `${before}{{${variableName}}}${after}`;

        if (newText.length <= maxLength) {
            onChange(newText);
        }

        // reset to clear the input
        setSelectVariableInputValue("Agregar variable");
    };

    const handleTextAreaClick = (e: React.MouseEvent<HTMLTextAreaElement>) => {
        const target = e.target as HTMLTextAreaElement;
        setCursorPosition(target.selectionStart);
    };

    // Manejar la selección de texto
    const handleTextAreaSelect = (e: React.SyntheticEvent<HTMLTextAreaElement>) => {
        const target = e.target as HTMLTextAreaElement;
        const start = target.selectionStart;
        const end = target.selectionEnd;

        if (start !== end) {
            setCurrentSelection({ start, end });
        } else {
            setCurrentSelection(null);
        }
        setCursorPosition(start);
    };

    const removeVariable = (tvid: string) => {
        const variableToRemove = variables.find((v) => v.tvid === tvid);
        if (!variableToRemove) return;

        // Remover la variable del texto
        const variablePattern = new RegExp(`\\{\\{${variableToRemove.name}\\}\\}`, "g");
        const newText = value.replace(variablePattern, "");
        onChange(newText);

        // Remover la variable de la lista
        const filteredVariables = variables.filter((v) => v.tvid !== tvid);
        setVariables(filteredVariables);
    };

    // Estado para mantener la selección actual
    const [currentSelection, setCurrentSelection] = useState<{
        start: number;
        end: number;
    } | null>(null);

    // Funciones para aplicar formato de texto
    const applyTextFormat = (formatType: "bold" | "italic" | "strikethrough") => {
        const textArea = textAreaRef.current;
        if (!textArea) return;

        // Usar la selección guardada o la actual
        const start = currentSelection?.start ?? textArea.selectionStart;
        const end = currentSelection?.end ?? textArea.selectionEnd;

        // Solo proceder si hay texto seleccionado
        if (start === end) return;

        const selectedText = value.substring(start, end);
        if (selectedText.length === 0) return;

        let formatChars = "";
        switch (formatType) {
            case "bold":
                formatChars = "*";
                break;
            case "italic":
                formatChars = "_";
                break;
            case "strikethrough":
                formatChars = "~";
                break;
        }

        const formattedText = `${formatChars}${selectedText}${formatChars}`;
        const beforeText = value.substring(0, start);
        const afterText = value.substring(end);
        const newText = beforeText + formattedText + afterText;

        // Verificar que no exceda el límite de caracteres
        if (newText.length <= maxLength) {
            onChange(newText);

            // Limpiar la selección guardada
            setCurrentSelection(null);

            // Usar setTimeout para asegurar que el DOM se actualice antes de manejar la selección
            setTimeout(() => {
                const currentTextArea = textAreaRef.current;
                if (
                    currentTextArea &&
                    typeof currentTextArea.setSelectionRange === "function"
                ) {
                    currentTextArea.focus();
                    // Seleccionar el texto formateado (incluyendo los caracteres de formato)
                    const newStart = start;
                    const newEnd = start + formattedText.length;
                    currentTextArea.setSelectionRange(newStart, newEnd);
                }
            }, 10);
        }
    };

    return (
        <div className="space-y-4">
            <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                    <div className="flex items-center gap-2">
                        {templateType ? (
                            <>
                                <Plus className="w-4 h-4" />
                                <SelectTemplateVariables
                                    value={selectVariableInputValue}
                                    templateType={templateType}
                                    onChange={(value, option) => {
                                        if (value && option) {
                                            handleVariableSelect(
                                                option as DefaultOptionType,
                                            );
                                        }
                                    }}
                                    style={{ width: 200 }}
                                    allowClear
                                />
                            </>
                        ) : (
                            // debes seleccionar un tipo de plantilla para poder agregar variables
                            <div className="flex items-center gap-2 text-state-yellow-full">
                                <AlertTriangleIcon className="w-4 h-4" />
                                <Text className="text-xs text-gray-500">
                                    Debes seleccionar un tipo de plantilla para poder
                                    agregar variables
                                </Text>
                            </div>
                        )}
                    </div>
                </div>
                <Tooltip title="Caracteres disponibles">
                    <Text type="secondary">
                        {value?.length || 0}/{maxLength}
                    </Text>
                </Tooltip>
            </div>

            {/* Toolbar de formato */}
            <div className="flex items-center gap-2 p-2 border rounded-lg bg-gray-50">
                <Text type="secondary" className="text-xs mr-2">
                    Formato:
                </Text>
                <Tooltip title="Negrita (*texto*)">
                    <Button
                        type="text"
                        size="small"
                        icon={<Bold className="w-4 h-4" />}
                        onClick={() => applyTextFormat("bold")}
                        className="flex items-center justify-center"
                    />
                </Tooltip>
                <Tooltip title="Cursiva (_texto_)">
                    <Button
                        type="text"
                        size="small"
                        icon={<Italic className="w-4 h-4" />}
                        onClick={() => applyTextFormat("italic")}
                        className="flex items-center justify-center"
                    />
                </Tooltip>
                <Tooltip title="Tachado (-texto-)">
                    <Button
                        type="text"
                        size="small"
                        icon={<Strikethrough className="w-4 h-4" />}
                        onClick={() => applyTextFormat("strikethrough")}
                        className="flex items-center justify-center"
                    />
                </Tooltip>
            </div>

            <TextArea
                ref={textAreaRef}
                value={value}
                onChange={handleTextAreaChange}
                onClick={handleTextAreaClick}
                onSelect={handleTextAreaSelect}
                placeholder="Texto del mensaje de WhatsApp"
                style={{ resize: "vertical" }}
                rows={12}
                maxLength={maxLength}
                className="font-mono"
            />

            {variables.length > 0 && (
                <div className="space-y-3 p-4 border rounded-lg">
                    <Text strong>Variables seleccionadas</Text>
                    {variables.map((variable) => (
                        <Space key={variable.tvid} className="flex items-center">
                            <Text>{`{{${variable.name}}}`}</Text>
                            <Input
                                placeholder="Ejemplo"
                                value={variable.example}
                                readOnly
                                className="w-64"
                            />
                            <Button
                                type="text"
                                danger
                                icon={<Delete />}
                                onClick={() => removeVariable(variable.tvid)}
                            />
                        </Space>
                    ))}
                </div>
            )}
        </div>
    );
};

export default WhatsappTemplateEditor;
