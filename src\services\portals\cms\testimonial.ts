import {
    CreateTestimonial,
    PartialUpdateTestimonial,
    RetrieveTestimonial,
} from "@myTypes/testimonial";
import { portalsApi } from "@services/portals";

export const DEFAULT_PAGE = 1;
export const DEFAULT_PAGE_SIZE = 10;

export const listTestimonials = async (
    page = DEFAULT_PAGE,
    pageSize = DEFAULT_PAGE_SIZE,
) => {
    const response = await portalsApi.get("cms/testimonials", {
        params: {
            page,
            pageSize,
        },
    });

    return response.data;
};

export const retrieveTestimonial = async (
    tid: string | undefined,
): Promise<RetrieveTestimonial> => {
    if (!tid) {
        throw new Error("No testimonial ID provided");
    }
    const response = await portalsApi.get(`cms/testimonials/${tid}`);
    return response.data;
};

export const updateTestimonial = async (
    tid: string,
    data: PartialUpdateTestimonial,
) => {
    const formData = new FormData();
    const { authorPhotoFile, ...payload } = data;

    Object.entries(payload).forEach(([key, value]) => {
        if (value) {
            formData.append(key, value as string);
        }
    });

    if (authorPhotoFile) {
        formData.append("authorPhotoFile", authorPhotoFile[0] as unknown as Blob);
    }

    const response = await portalsApi.patch(`cms/testimonials/${tid}`, formData, {
        headers: {
            "Content-Type": "multipart/form-data",
        },
    });

    return response.data;
};

export const deleteTestimonial = async (tid: string) => {
    const response = await portalsApi.delete(`cms/testimonials/${tid}`);
    return response.data;
};

export const createTestimonial = async (data: CreateTestimonial) => {
    console.log("data", data);
    const formData = new FormData();
    const { authorPhotoFile, ...payload } = data;

    Object.entries(payload).forEach(([key, value]) => {
        if (value) {
            formData.append(key, value as string);
        }
    });

    if (authorPhotoFile) {
        console.log("authorPhotoFile", authorPhotoFile);
        formData.append("authorPhotoFile", authorPhotoFile[0] as unknown as Blob);
    }
    const response = await portalsApi.post("cms/testimonials", formData, {
        headers: {
            "Content-Type": "multipart/form-data",
        },
    });

    return response.data;
};
