import {
    ALL_PERMISSIONS_MAP,
    type PermissionAction,
    type PermissionKey,
} from "@/core/config/permissions-map";

/**
 * Función genérica para verificar permisos de submódulos
 * @param userPermissions - Array de permisos del usuario
 * @param subModule - Clave del submódulo
 * @param action - Acción a verificar (view, add, change, delete, etc.)
 * @returns boolean
 */
export function hasPermission(
    userPermissions: string[] = [],
    subModule: PermissionKey,
    action: PermissionAction = "view",
): boolean {
    const permission = ALL_PERMISSIONS_MAP[subModule] || "";
    return userPermissions.some((userPermission) =>
        userPermission.endsWith(`${action}_${permission}`),
    );
}

/**
 * Función específica para verificar permisos de vista (más común)
 * @param userPermissions - Array de permisos del usuario
 * @param subModule - Clave del submódulo
 * @returns boolean
 */
export function canView(
    userPermissions: string[] = [],
    subModule: PermissionKey,
): boolean {
    return hasPermission(userPermissions, subModule, "view");
}

/**
 * Función para verificar si puede crear
 */
export function canAdd(
    userPermissions: string[] = [],
    subModule: PermissionKey,
): boolean {
    return hasPermission(userPermissions, subModule, "add");
}

/**
 * Función para verificar si puede editar
 */
export function canChange(
    userPermissions: string[] = [],
    subModule: PermissionKey,
): boolean {
    return hasPermission(userPermissions, subModule, "change");
}

/**
 * Función para verificar si puede eliminar
 */
export function canDelete(
    userPermissions: string[] = [],
    subModule: PermissionKey,
): boolean {
    return hasPermission(userPermissions, subModule, "delete");
}

/**
 * Función para verificar múltiples permisos a la vez
 */
export function hasAnyPermission(
    userPermissions: string[] = [],
    subModule: PermissionKey,
    actions: PermissionAction[] = ["view"],
): boolean {
    return actions.some((action) => hasPermission(userPermissions, subModule, action));
}

/**
 * Función para verificar todos los permisos
 */
export function hasAllPermissions(
    userPermissions: string[] = [],
    subModule: PermissionKey,
    actions: PermissionAction[] = ["view"],
): boolean {
    return actions.every((action) => hasPermission(userPermissions, subModule, action));
}
