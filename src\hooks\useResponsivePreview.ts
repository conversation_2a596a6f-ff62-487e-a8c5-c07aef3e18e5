import { type ViewportSize } from "@components/shared/molecules/ResponsivePreview";
import { VIEWPORT_CONFIGS } from "@components/shared/molecules/ResponsivePreview/config";
import { useState, useCallback } from "react";

interface UseResponsivePreviewOptions {
    defaultViewport?: ViewportSize;
    onViewportChange?: (viewport: ViewportSize) => void;
}

export function useResponsivePreview({
    defaultViewport = "desktop",
    onViewportChange,
}: UseResponsivePreviewOptions = {}) {
    const [currentViewport, setCurrentViewport] =
        useState<ViewportSize>(defaultViewport);

    const changeViewport = useCallback(
        (viewport: ViewportSize) => {
            setCurrentViewport(viewport);
            onViewportChange?.(viewport);
        },
        [onViewportChange],
    );

    const getCurrentConfig = useCallback(() => {
        return VIEWPORT_CONFIGS[currentViewport];
    }, [currentViewport]);

    const getViewportClasses = useCallback(() => {
        const config = getCurrentConfig();

        switch (currentViewport) {
            case "desktop":
                return "w-full max-w-none";
            case "tablet":
                return `w-[${config.width}px] max-w-[${config.width}px]`;
            case "mobile":
                return `w-[${config.width}px] max-w-[${config.width}px]`;
            default:
                return "w-full";
        }
    }, [currentViewport, getCurrentConfig]);

    const getViewportStyles = useCallback(() => {
        const config = getCurrentConfig();

        return {
            maxWidth: currentViewport === "desktop" ? "100%" : `${config.width}px`,
            minHeight: currentViewport === "mobile" ? "400px" : "auto",
            transition: "all 0.3s ease-in-out",
        };
    }, [currentViewport, getCurrentConfig]);

    const isViewport = useCallback(
        (viewport: ViewportSize) => {
            return currentViewport === viewport;
        },
        [currentViewport],
    );

    return {
        currentViewport,
        changeViewport,
        getCurrentConfig,
        getViewportClasses,
        getViewportStyles,
        isViewport,
        viewportConfigs: VIEWPORT_CONFIGS,
    };
}

export default useResponsivePreview;
