import { Config<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Popconfirm } from "antd";
import type { TableProps } from "antd";
import {
    ActivityList,
    ActivityStatus,
    ResponsibleUser,
    OrderSummary,
} from "../../types/activity";
import { useMemo, useCallback } from "react";
import { formatDateTime } from "@lib/helpers";
import ActivityStatusCell from "../atoms/activity-status-cell";
import ActivityResponsibleCell from "../atoms/activity-responsible-cell";
import ActivityOrderCell from "../atoms/activity-order-cell";
import ActivityDeadlineCell from "../atoms/activity-deadline-cell";
import { Link } from "react-router-dom";
import { Edit3, Trash2 } from "lucide-react";
import { useDeleteActivity } from "../../hooks/use-activity";

const { Text } = Typography;

interface ActivitiesTableProps {
    initialData: ActivityList[];
}

const ActivitiesTable = ({ initialData }: ActivitiesTableProps) => {
    const deleteActivityMutation = useDeleteActivity();

    const handleDeleteActivity = useCallback(
        (aid: string) => {
            deleteActivityMutation.mutate(aid);
        },
        [deleteActivityMutation],
    );

    const INITIAL_COLUMNS: TableProps<ActivityList>["columns"] = useMemo(
        () => [
            {
                title: "ID",
                dataIndex: "aid",
                key: "aid",
                width: 80,
                render: (aid: string) => (
                    <Tooltip title="Ver detalles de Actividad">
                        <Link
                            to={`/crm/activities/${aid}`}
                            className="font-semibold text-blue-full hover:underline"
                        >
                            {aid.slice(0, 6)}
                        </Link>
                    </Tooltip>
                ),
            },
            {
                title: "TÍTULO",
                dataIndex: "title",
                key: "title",
                width: 200,
                render: (title: string | null, record: ActivityList) => (
                    <div className="flex flex-col">
                        <Text className="font-medium">{title || "Sin título"}</Text>
                        {record.description && (
                            <Text type="secondary" className="text-xs" ellipsis>
                                {record.description}
                            </Text>
                        )}
                    </div>
                ),
            },
            {
                title: "ESTADO",
                dataIndex: "status",
                key: "status",
                width: 140,
                render: (status: ActivityStatus) => (
                    <div className="flex justify-center">
                        <ActivityStatusCell status={status} />
                    </div>
                ),
            },
            {
                title: "RESPONSABLE",
                dataIndex: "responsible",
                key: "responsible",
                width: 200,
                render: (responsible: ResponsibleUser | null) => (
                    <ActivityResponsibleCell responsible={responsible} />
                ),
            },
            {
                title: "ORDEN",
                dataIndex: "order",
                key: "order",
                width: 150,
                render: (order: OrderSummary | null) => (
                    <ActivityOrderCell order={order} />
                ),
            },
            {
                title: "FECHA LÍMITE",
                dataIndex: "deadline",
                key: "deadline",
                width: 150,
                render: (deadline: string | null) => (
                    <ActivityDeadlineCell deadline={deadline} />
                ),
            },
            {
                title: "FECHA DE CREACIÓN",
                dataIndex: "createdAt",
                key: "createdAt",
                width: 160,
                render: (createdAt: string) => {
                    const formattedDate = formatDateTime(createdAt);
                    return (
                        <Text type="secondary" className="text-xs">
                            {formattedDate}
                        </Text>
                    );
                },
            },
            {
                title: "ACCIONES",
                key: "actions",
                width: 100,
                fixed: "right",
                render: (_, record: ActivityList) => (
                    <div className="flex items-center gap-2">
                        <Tooltip title="Editar actividad">
                            <Link to={`/crm/activities/${record.aid}`}>
                                <Button
                                    type="text"
                                    size="small"
                                    icon={<Edit3 size={16} />}
                                    className="text-blue-500 hover:text-blue-600"
                                />
                            </Link>
                        </Tooltip>
                        <Tooltip title="Eliminar actividad">
                            <Popconfirm
                                title="¿Estás seguro de eliminar esta actividad?"
                                description="Esta acción no se puede deshacer."
                                onConfirm={() => handleDeleteActivity(record.aid)}
                                okText="Sí, eliminar"
                                cancelText="Cancelar"
                                okButtonProps={{ danger: true }}
                            >
                                <Button
                                    type="text"
                                    size="small"
                                    icon={<Trash2 size={16} />}
                                    className="text-red-500 hover:text-red-600"
                                    loading={deleteActivityMutation.isPending}
                                />
                            </Popconfirm>
                        </Tooltip>
                    </div>
                ),
            },
        ],
        [deleteActivityMutation, handleDeleteActivity],
    );

    const columns = useMemo(() => INITIAL_COLUMNS, [INITIAL_COLUMNS]);

    return (
        <ConfigProvider
            theme={{
                components: {
                    Table: {
                        headerBg: "#FBFCFD",
                        borderColor: "#fff",
                        headerSplitColor: "#fafafa",
                        headerBorderRadius: 8,
                        rowHoverBg: "#F6FAFD",
                        rowSelectedBg: "#F6FAFD",
                        rowSelectedHoverBg: "#F6FAFD",
                        footerBg: "#F1F1F1",
                    },
                },
            }}
        >
            <Table<ActivityList>
                className="rounded-lg"
                footer={() => ""}
                pagination={false}
                columns={columns}
                dataSource={initialData}
                scroll={{ x: 1400 }}
                size="middle"
            />
        </ConfigProvider>
    );
};

export default ActivitiesTable;
