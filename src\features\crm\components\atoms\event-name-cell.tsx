import { Event } from "../../types/event";
import { <PERSON> } from "react-router-dom";
import { PenLine } from "lucide-react";

type EventNameCellProps = {
    event: Event;
};

export default function EventNameCell({ event }: EventNameCellProps) {
    const { eid, name, description } = event;

    return (
        <div className="px-1 py-1 rounded-md hover:bg-gray-50 transition-colors">
            <div className="flex items-center">
                <Link
                    to={`/crm/events/${eid}`}
                    className="flex items-center text-blue-600 hover:text-blue-800"
                >
                    <PenLine
                        size={16}
                        className="mr-1.5 text-blue-500"
                        strokeWidth={2}
                    />
                    <span className="text-sm font-medium">{name}</span>
                </Link>
            </div>

            {description && (
                <p className="text-xs text-gray-500 mt-1 line-clamp-2">{description}</p>
            )}
        </div>
    );
}
