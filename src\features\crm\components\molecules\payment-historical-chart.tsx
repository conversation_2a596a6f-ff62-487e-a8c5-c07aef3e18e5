import { useState } from "react";
import { <PERSON>, <PERSON>kel<PERSON>, Button, Switch } from "antd";
import { BarChart2, TrendingUp } from "lucide-react";
import {
    ResponsiveContainer,
    ComposedChart,
    BarChart,
    Bar,
    Line,
    XAxis,
    YAxis,
    CartesianGrid,
    <PERSON>lt<PERSON>,
    Legend,
} from "recharts";

import {
    useDashboardPaymentsHistorical,
    createDashboardPaymentsHistoricalQueryParams,
} from "../../hooks/use-dashboard-payments";
import type {
    PeriodFilter,
    DashboardPaymentHistoricalPeriodData,
} from "../../types/dashboard/payment";
import { useSearchParams } from "react-router-dom";

interface PaymentHistoricalChartProps {
    className?: string;
}

export default function PaymentHistoricalChart({
    className,
}: PaymentHistoricalChartProps) {
    const [searchParams] = useSearchParams();
    const [selectedPeriod, setSelectedPeriod] = useState<PeriodFilter>("weekly"); // Default to weekly
    const [showCumulative, setShowCumulative] = useState(false);

    // Create query parameters for historical data
    const historicalQuery = createDashboardPaymentsHistoricalQueryParams(
        searchParams,
        selectedPeriod,
    );

    // Fetch historical data
    const { data: historicalData, isLoading } =
        useDashboardPaymentsHistorical(historicalQuery);

    // Transform data for chart - show either discrete or cumulative based on toggle
    const chartData =
        historicalData?.data?.map((item: DashboardPaymentHistoricalPeriodData) => ({
            period: item.shortPeriod,
            fullPeriod: item.period,
            value: showCumulative ? item.cumulative.total : item.discrete.total,
            valuePen: showCumulative ? item.cumulative.pen : item.discrete.pen,
            valueUsd: showCumulative ? item.cumulative.usd : item.discrete.usd,
            startDate: item.startDate,
            endDate: item.endDate,
        })) || [];

    const formatCurrency = (value: number) => `S/. ${value.toLocaleString()}`;

    interface TooltipProps {
        active?: boolean;
        payload?: Array<{
            payload: {
                fullPeriod: string;
                startDate: string;
                endDate: string;
                value: number;
                valuePen: number;
                valueUsd: number;
            };
            dataKey: string;
            value: number;
        }>;
    }

    const CustomTooltip = ({ active, payload }: TooltipProps) => {
        if (active && payload && payload.length) {
            const data = payload[0].payload;
            return (
                <div
                    style={{
                        backgroundColor: "white",
                        padding: "12px",
                        border: "1px solid #d9d9d9",
                        borderRadius: "8px",
                        boxShadow:
                            "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
                        minWidth: "200px",
                        zIndex: 1000,
                    }}
                >
                    <p
                        style={{
                            fontWeight: 600,
                            color: "#262626",
                            marginBottom: "8px",
                            fontSize: "14px",
                        }}
                    >
                        {data.fullPeriod}
                    </p>
                    <p
                        style={{
                            fontSize: "12px",
                            color: "#595959",
                            marginBottom: "8px",
                        }}
                    >
                        {data.startDate === data.endDate
                            ? `Fecha: ${data.startDate}`
                            : `Período: ${data.startDate} - ${data.endDate}`}
                    </p>

                    <div style={{ marginBottom: "8px" }}>
                        <p
                            style={{
                                fontSize: "12px",
                                fontWeight: 500,
                                marginBottom: "4px",
                                color: showCumulative ? "#fa8c16" : "#1890ff",
                            }}
                        >
                            {showCumulative
                                ? "Acumulativo:"
                                : "Discreto (período actual):"}
                        </p>
                        <div style={{ marginLeft: "8px" }}>
                            <p style={{ fontSize: "12px", marginBottom: "2px" }}>
                                <span style={{ color: "#595959" }}>Total: </span>
                                <span style={{ fontWeight: 500, color: "#262626" }}>
                                    {formatCurrency(data.value)}
                                </span>
                            </p>
                            <p style={{ fontSize: "12px", marginBottom: "2px" }}>
                                <span style={{ color: "#52c41a" }}>PEN: </span>
                                <span style={{ fontWeight: 500, color: "#262626" }}>
                                    S/. {data.valuePen.toLocaleString()}
                                </span>
                            </p>
                            <p style={{ fontSize: "12px" }}>
                                <span style={{ color: "#722ed1" }}>USD: </span>
                                <span style={{ fontWeight: 500, color: "#262626" }}>
                                    $ {data.valueUsd.toLocaleString()}
                                </span>
                            </p>
                        </div>
                    </div>
                </div>
            );
        }
        return null;
    };

    return (
        <Card
            title={
                <div className="flex items-center justify-between">
                    <div className="flex items-center">
                        {showCumulative ? (
                            <TrendingUp className="mr-2 h-5 w-5 text-orange-500" />
                        ) : (
                            <BarChart2 className="mr-2 h-5 w-5 text-blue-500" />
                        )}
                        <span>Histórico de Pagos</span>
                    </div>
                    <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-2">
                            <span className="text-sm text-gray-600">Acumulativo:</span>
                            <Switch
                                size="small"
                                checked={showCumulative}
                                onChange={setShowCumulative}
                                checkedChildren="Sí"
                                unCheckedChildren="No"
                            />
                        </div>
                        <Button.Group size="small">
                            <Button
                                type={
                                    selectedPeriod === "daily" ? "primary" : "default"
                                }
                                onClick={() => setSelectedPeriod("daily")}
                            >
                                Diario
                            </Button>
                            <Button
                                type={
                                    selectedPeriod === "weekly" ? "primary" : "default"
                                }
                                onClick={() => setSelectedPeriod("weekly")}
                            >
                                Semanal
                            </Button>
                            <Button
                                type={
                                    selectedPeriod === "monthly" ? "primary" : "default"
                                }
                                onClick={() => setSelectedPeriod("monthly")}
                            >
                                Mensual
                            </Button>
                        </Button.Group>
                    </div>
                </div>
            }
            className={className}
        >
            {isLoading ? (
                <Skeleton active paragraph={{ rows: 8 }} />
            ) : (
                <div>
                    {/* Chart */}
                    <div style={{ width: "100%", height: 400 }}>
                        <ResponsiveContainer>
                            {showCumulative ? (
                                <ComposedChart
                                    data={chartData}
                                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                                >
                                    <CartesianGrid
                                        strokeDasharray="3 3"
                                        stroke="#f0f0f0"
                                    />
                                    <XAxis
                                        dataKey="period"
                                        tick={{ fontSize: 12 }}
                                        stroke="#666"
                                    />
                                    <YAxis
                                        tick={{ fontSize: 12 }}
                                        stroke="#666"
                                        tickFormatter={formatCurrency}
                                    />
                                    <Tooltip content={<CustomTooltip />} />
                                    <Legend />
                                    <Line
                                        type="monotone"
                                        dataKey="value"
                                        stroke="#fa8c16"
                                        strokeWidth={3}
                                        dot={{ fill: "#fa8c16", strokeWidth: 2, r: 4 }}
                                        name="Acumulativo"
                                    />
                                </ComposedChart>
                            ) : (
                                <BarChart
                                    data={chartData}
                                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                                >
                                    <CartesianGrid
                                        strokeDasharray="3 3"
                                        stroke="#f0f0f0"
                                    />
                                    <XAxis
                                        dataKey="period"
                                        tick={{ fontSize: 12 }}
                                        stroke="#666"
                                    />
                                    <YAxis
                                        tick={{ fontSize: 12 }}
                                        stroke="#666"
                                        tickFormatter={formatCurrency}
                                    />
                                    <Tooltip content={<CustomTooltip />} />
                                    <Legend />
                                    <Bar
                                        dataKey="value"
                                        fill="#1890ff"
                                        name="Discreto"
                                        radius={[4, 4, 0, 0]}
                                    />
                                </BarChart>
                            )}
                        </ResponsiveContainer>
                    </div>

                    {chartData.length === 0 && !isLoading && (
                        <div className="text-center py-8 text-gray-500">
                            No hay datos disponibles para el período seleccionado
                        </div>
                    )}
                </div>
            )}
        </Card>
    );
}
