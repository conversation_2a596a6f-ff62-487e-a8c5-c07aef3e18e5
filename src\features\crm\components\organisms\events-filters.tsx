import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Select, Radio } from "antd";
import { EventTypeLabels, EventModalityLabels } from "@/features/crm/types/event";
import { useSearchParams } from "react-router-dom";
import { useCallback, useEffect, useState } from "react";
import dayjs, { Dayjs } from "dayjs";
import "dayjs/locale/es";
import SelectOfferings from "../molecules/select-offerings";
import SelectInstructor from "../molecules/select-instructor";

dayjs.locale("es");

interface EventsFiltersProps {
    isOpen: boolean;
    onClose: () => void;
    onClearFilters?: () => void;
}

export default function EventsFilters({
    isOpen,
    onClose,
    onClearFilters,
}: EventsFiltersProps) {
    const [searchParams, setSearchParams] = useSearchParams();

    // Local filter states
    const [selectedOfferings, setSelectedOfferings] = useState<string[]>([]);
    const [selectedTypes, setSelectedTypes] = useState<string[]>([]);
    const [selectedModality, setSelectedModality] = useState<string>("all");
    const [selectedInstructor, setSelectedInstructor] = useState<string | null>(null);
    const [dateRange, setDateRange] = useState<[Dayjs, Dayjs] | null>(null);

    // Initialize filters from URL
    useEffect(() => {
        const offerings = searchParams.get("offering");
        const types = searchParams.get("type");
        const modalities = searchParams.get("modality");
        const instructor = searchParams.get("instructor");
        const createdAtAfter = searchParams.get("createdAtAfter");
        const createdAtBefore = searchParams.get("createdAtBefore");

        if (offerings) {
            setSelectedOfferings(offerings.split(","));
        } else {
            setSelectedOfferings([]);
        }

        if (types) {
            setSelectedTypes(types.split(","));
        } else {
            setSelectedTypes([]);
        }

        if (modalities) {
            setSelectedModality(modalities);
        } else {
            setSelectedModality("all");
        }

        if (instructor) {
            setSelectedInstructor(instructor);
        } else {
            setSelectedInstructor(null);
        }

        if (createdAtAfter && createdAtBefore) {
            setDateRange([dayjs(createdAtAfter), dayjs(createdAtBefore)]);
        } else {
            setDateRange(null);
        }
    }, [searchParams]);

    const handleApplyFilters = useCallback(() => {
        setSearchParams((prev) => {
            // Clear previous filters
            prev.delete("offering");
            prev.delete("type");
            prev.delete("modality");
            prev.delete("instructor");
            prev.delete("createdAtAfter");
            prev.delete("createdAtBefore");

            // Apply offering filters
            if (selectedOfferings.length > 0) {
                prev.set("offering", selectedOfferings.join(","));
            }

            // Apply type filters
            if (selectedTypes.length > 0) {
                prev.set("type", selectedTypes.join(","));
            }

            // Apply modality filters
            if (selectedModality && selectedModality !== "all") {
                prev.set("modality", selectedModality);
            }

            // Apply instructor filters
            if (selectedInstructor) {
                prev.set("instructor", selectedInstructor);
            }

            // Apply date range filters
            if (dateRange && dateRange[0] && dateRange[1]) {
                prev.set("createdAtAfter", dateRange[0].format("YYYY-MM-DD"));
                prev.set("createdAtBefore", dateRange[1].format("YYYY-MM-DD"));
            }

            // Reset to page 1
            prev.set("page", "1");

            return prev;
        });
        onClose();
    }, [
        selectedOfferings,
        selectedTypes,
        selectedModality,
        dateRange,
        selectedInstructor,
        setSearchParams,
        onClose,
    ]);

    const handleClearFilters = useCallback(() => {
        setSelectedOfferings([]);
        setSelectedTypes([]);
        setSelectedModality("all");
        setSelectedInstructor(null);
        setDateRange(null);

        setSearchParams((prev) => {
            prev.delete("offering");
            prev.delete("type");
            prev.delete("modality");
            prev.delete("instructor");
            prev.delete("createdAtAfter");
            prev.delete("createdAtBefore");
            prev.set("page", "1");
            return prev;
        });

        onClearFilters?.();
        onClose();
    }, [setSearchParams, onClearFilters, onClose]);

    return (
        <Drawer
            title="Aplicar filtros"
            placement="right"
            closable={true}
            onClose={onClose}
            open={isOpen}
            width={480}
        >
            <div className="space-y-4">
                <div>
                    <h4 className="font-medium mb-2">Programa</h4>
                    <SelectOfferings
                        mode="multiple"
                        style={{ width: "100%" }}
                        placeholder="Seleccionar programas"
                        value={selectedOfferings}
                        onChange={(value) => {
                            if (Array.isArray(value)) {
                                setSelectedOfferings(value);
                            } else if (value) {
                                setSelectedOfferings([value]);
                            } else {
                                setSelectedOfferings([]);
                            }
                        }}
                        allowClear
                    />
                </div>

                <div>
                    <h4 className="font-medium mb-2">Tipo</h4>
                    <Select
                        mode="multiple"
                        style={{ width: "100%" }}
                        placeholder="Seleccionar tipos"
                        value={selectedTypes}
                        onChange={setSelectedTypes}
                        allowClear
                        showSearch={false}
                    >
                        {Object.entries(EventTypeLabels).map(([value, label]) => (
                            <Select.Option key={value} value={value}>
                                {label}
                            </Select.Option>
                        ))}
                    </Select>
                </div>

                <div>
                    <h4 className="font-medium mb-2">Modalidad</h4>
                    <Radio.Group
                        value={selectedModality}
                        onChange={(e) => setSelectedModality(e.target.value)}
                        className="w-full"
                    >
                        <div className="space-y-2">
                            <Radio value="all">Todos</Radio>
                            {Object.entries(EventModalityLabels).map(
                                ([value, label]) => (
                                    <Radio key={value} value={value}>
                                        {label}
                                    </Radio>
                                ),
                            )}
                        </div>
                    </Radio.Group>
                </div>

                <div>
                    <h4 className="font-medium mb-2">Instructor</h4>
                    <SelectInstructor
                        className="w-full"
                        placeholder="Seleccionar instructores"
                        value={selectedInstructor || undefined}
                        onChange={(value) => {
                            setSelectedInstructor(value);
                        }}
                    />
                </div>

                <div>
                    <h4 className="font-medium mb-2">Fecha de creación</h4>
                    <div className="space-y-3">
                        <div>
                            <label className="text-sm text-gray-600 mb-1 block">
                                Seleccionar rango
                            </label>
                            <DatePicker.RangePicker
                                style={{ width: "100%" }}
                                placeholder={["Fecha inicio", "Fecha fin"]}
                                value={dateRange}
                                onChange={(dates) =>
                                    setDateRange(dates as [Dayjs, Dayjs] | null)
                                }
                                presets={[
                                    {
                                        label: "Hoy",
                                        value: [
                                            dayjs().startOf("day"),
                                            dayjs().endOf("day"),
                                        ],
                                    },
                                    {
                                        label: "Esta semana",
                                        value: [
                                            dayjs().startOf("week"),
                                            dayjs().endOf("week"),
                                        ],
                                    },
                                    {
                                        label: "Este mes",
                                        value: [
                                            dayjs().startOf("month"),
                                            dayjs().endOf("month"),
                                        ],
                                    },
                                    {
                                        label: "Último mes",
                                        value: [
                                            dayjs()
                                                .subtract(1, "month")
                                                .startOf("month"),
                                            dayjs().subtract(1, "month").endOf("month"),
                                        ],
                                    },
                                    {
                                        label: "Este año",
                                        value: [
                                            dayjs().startOf("year"),
                                            dayjs().endOf("year"),
                                        ],
                                    },
                                ]}
                            />
                        </div>
                    </div>
                </div>

                <div className="pt-4 space-y-2">
                    <Button type="primary" block onClick={handleApplyFilters}>
                        Aplicar filtros
                    </Button>
                    <Button block onClick={handleClearFilters}>
                        Limpiar filtros
                    </Button>
                </div>
            </div>
        </Drawer>
    );
}
