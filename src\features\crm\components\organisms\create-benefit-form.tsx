import { Button, Form, Input } from "antd";
import { CreateBenefitFormValues } from "@/features/crm/types/benefit";
import { useCreateBenefit } from "../../hooks/use-benefit";

type Props = {
    handleCloseModal: () => void;
};

export default function CreateBenefitForm({ handleCloseModal }: Props) {
    const [addOrderForm] = Form.useForm();

    const handleBenefitFormOnFinish = (values: CreateBenefitFormValues) => {
        createBenefit(values);
    };

    const { mutate: createBenefit } = useCreateBenefit({ onSuccess: handleCloseModal });

    return (
        <Form
            name="CreateBenefitForm"
            layout="vertical"
            form={addOrderForm}
            onFinish={handleBenefitFormOnFinish}
        >
            <Form.Item<CreateBenefitFormValues>
                name="name"
                label={<span className="font-semibold">Nombre</span>}
                rules={[
                    {
                        required: true,
                    },
                ]}
            >
                <Input placeholder="Nombre" type="text" />
            </Form.Item>
            <Form.Item<CreateBenefitFormValues>
                name="description"
                label={<span className="font-semibold">Descripción</span>}
            >
                <Input.TextArea
                    placeholder="Descripción"
                    autoSize={{ minRows: 3, maxRows: 5 }}
                />
            </Form.Item>

            <div className="grid grid-cols-2 gap-2 items-end">
                <Button
                    onClick={() => handleCloseModal()}
                    className="h-fit"
                    size="large"
                >
                    Cancelar
                </Button>
                <Button
                    type="primary"
                    htmlType="submit"
                    className="h-fit"
                    size="large"
                    block
                >
                    Guardar
                </Button>
            </div>
        </Form>
    );
}
