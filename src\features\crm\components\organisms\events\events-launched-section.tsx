import React from "react";
import { <PERSON>, <PERSON>, <PERSON>, Button } from "antd";
import LaunchedEventsCarousel from "@/features/crm/components/molecules/events/launched-events-carousel";
import { useDashboardLaunchedEvents } from "@/features/crm/hooks/use-dashboard-events";
import type { DashboardEventQueryParams } from "@/features/crm/types/dashboard/events";
import { useNavigate } from "react-router-dom";
import { Clock } from "lucide-react";

interface EventsLaunchedSectionProps {
    queryParams: DashboardEventQueryParams;
}

const EventsLaunchedSection: React.FC<EventsLaunchedSectionProps> = ({
    queryParams,
}) => {
    const navigate = useNavigate();
    const { data: launchedEvents } = useDashboardLaunchedEvents(queryParams);

    return (
        <Card
            title={
                <div className="flex items-center gap-2">
                    <Clock size={20} className="text-blue-500" />
                    <span>Eventos Activos</span>
                </div>
            }
            extra={
                <Button type="link" onClick={() => navigate("/crm/event-schedules")}>
                    Ver todos
                </Button>
            }
        >
            <Row gutter={[16, 16]}>
                <Col xs={24}>
                    <LaunchedEventsCarousel data={launchedEvents} />
                </Col>
            </Row>
        </Card>
    );
};

export default EventsLaunchedSection;
