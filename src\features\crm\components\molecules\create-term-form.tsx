import { Form, Input, Button } from "antd";
import type { FormInstance } from "antd";
import { useMutation } from "@tanstack/react-query";
import { createTerm } from "@/features/crm/services/portals/term";

type ErrorData = { name?: unknown; detail?: unknown };
type APIError = { response?: { data?: ErrorData } };

export type CreateTermFormProps = {
    form: FormInstance;
    onClose: () => void;
    onCreated: () => void;
    notify: (type: "success" | "error", message: string, description?: string) => void;
};

export default function CreateTermForm({
    form,
    onClose,
    onCreated,
    notify,
}: CreateTermFormProps) {
    const { mutate, isPending } = useMutation({
        mutationFn: createTerm,
        onSuccess: () => {
            notify("success", "Ciclo creado", "El ciclo ha sido creado exitosamente");
            onCreated();
            onClose();
        },
        onError: (err: unknown) => {
            const res = (err as APIError).response?.data ?? {};
            const nameVal = res.name;
            const fieldMsg = Array.isArray(nameVal) ? nameVal[0] : nameVal;
            const detail = res.detail;
            if (fieldMsg) {
                form.setFields([{ name: "name", errors: [String(fieldMsg)] }]);
            }
            const friendly =
                fieldMsg ||
                detail ||
                "No se pudo crear el ciclo. Verifica el nombre e inténtalo nuevamente.";
            notify("error", "Error al crear el ciclo", friendly);
        },
    });

    return (
        <Form
            form={form}
            layout="vertical"
            onFinish={(v: { name: string }) => mutate(v.name)}
        >
            <Form.Item
                name="name"
                label="Nombre del ciclo"
                rules={[{ required: true, message: "Ingrese el nombre" }]}
            >
                <Input placeholder="Ej: Ciclo 1" disabled={isPending} />
            </Form.Item>
            <div className="flex justify-end gap-2">
                <Button onClick={onClose} disabled={isPending}>
                    Cancelar
                </Button>
                <Button type="primary" htmlType="submit" loading={isPending}>
                    Guardar
                </Button>
            </div>
        </Form>
    );
}
