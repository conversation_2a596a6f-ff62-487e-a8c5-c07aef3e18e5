import React from "react";
import { But<PERSON>, Dropdown } from "antd";
import { Send, Setting<PERSON>, Eye } from "lucide-react";
import { type BlogPost, BlogStatus } from "@myTypes/blog";

interface BlogEditorMobileHeaderProps {
    blog: BlogPost | undefined;
    onSettingsClick: () => void;
    onPublishClick: () => void;
}

const BlogEditorMobileHeader: React.FC<BlogEditorMobileHeaderProps> = ({
    blog: data,
    onSettingsClick,
    onPublishClick,
}) => {
    return (
        <div className="flex items-center justify-end w-full px-4 py-2">
            <div className="flex items-center space-x-2">
                {data?.status === BlogStatus.DRAFT && (
                    <Button
                        type="primary"
                        icon={<Send size={16} />}
                        className="rounded-full"
                        onClick={onPublishClick}
                    >
                        Publicar
                    </Button>
                )}

                <Dropdown
                    menu={{
                        items: [
                            {
                                key: "settings",
                                label: "Configuración del blog",
                                icon: <Settings size={16} />,
                                onClick: onSettingsClick,
                            },
                            {
                                key: "preview",
                                label: "Vista previa",
                                icon: <Eye size={16} />,
                                onClick: () => {
                                    window.open(
                                        `/cms/blog/${data?.bid}/preview`,
                                        "_blank",
                                    );
                                },
                            },
                        ],
                    }}
                    placement="bottomRight"
                    trigger={["click"]}
                >
                    <Button
                        type="text"
                        className="flex items-center justify-center"
                        icon={<Settings size={18} />}
                    />
                </Dropdown>
            </div>
        </div>
    );
};

export default BlogEditorMobileHeader;
