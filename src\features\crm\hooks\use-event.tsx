import { useMutation, useQuery } from "@tanstack/react-query";
import { AxiosError } from "axios";
import {
    Event,
    CreateEventFormBody,
    ListEventQueryParams,
} from "@/features/crm/types/event";
import { createEvent, listEvents } from "@/features/crm/services/portals/event";
import { App } from "antd";
import queryClient from "@lib/queryClient";

type UseEventProps = {
    queryParams?: ListEventQueryParams;
};

export const useEvents = ({ queryParams }: UseEventProps = {}) => {
    const { data, isLoading, isError } = useQuery({
        queryKey: ["events", queryParams],
        queryFn: () => listEvents(queryParams),
        refetchOnWindowFocus: false,
    });

    const { count, results: events } = data || {
        count: 0,
        results: [],
    };

    return {
        isLoading,
        isError,
        events,
        count,
    };
};

type UseCreateEventProps = {
    onSuccess?: () => void;
    onError?: () => void;
};
export const useCreateEvent = ({ onSuccess, onError }: UseCreateEventProps = {}) => {
    const { message, notification } = App.useApp();

    return useMutation<Event, AxiosError, CreateEventFormBody>({
        mutationFn: (newEvent: CreateEventFormBody) => createEvent(newEvent),
        onSuccess: () => {
            message.success({
                content: "Evento creado exitosamente",
                duration: 2,
            });
            queryClient.invalidateQueries({ queryKey: ["events"] });
            onSuccess?.();
        },
        onError: () => {
            notification.error({
                message: "Error al crear el evento",
                description: "Ha ocurrido un error al intentar crear el evento",
            });
            onError?.();
        },
    });
};
