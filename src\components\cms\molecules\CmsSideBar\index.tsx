import { Link, useLocation } from "react-router-dom";
import { Menu } from "antd";

import ContactBook from "@assets/icons/huge/contact-book.svg?react";
import Megaphone from "@assets/icons/huge/megaphone.svg?react";
import Program from "@assets/icons/huge/program.svg?react";
import Paper from "@assets/icons/huge/paper.svg?react";
import DiscountTag from "@assets/icons/huge/discount-tag.svg?react";
import GeometricShapes from "@assets/icons/huge/geometric-shapes.svg?react";

import {
    useMenuViewFilter,
    MenuItemWithPermission,
} from "@hooks/use-permission";

const menuItemsWithPermissions: MenuItemWithPermission[] = [
    {
        key: "general",
        label: (
            <span className="text-black-medium font-semibold text-xs uppercase">
                general
            </span>
        ),
        type: "group",
        children: [
            {
                key: "dashboard",
                label: (
                    <Link to="/cms" className="font-medium text-sm">
                        Dashboard
                    </Link>
                ),
                icon: <ContactBook />,
            },
        ],
    },
    {
        key: "PRINCIPAL",
        label: (
            <span className="text-black-medium font-semibold text-xs uppercase">
                PRINCIPAL
            </span>
        ),
        type: "group",
        children: [
            {
                key: "offering",
                label: (
                    <Link to="/cms/offering" className="font-medium text-sm">
                        Productos
                    </Link>
                ),
                icon: <Program />,
                requiredPermission: "cms.offering",
            },
            {
                key: "instructors",
                label: (
                    <Link to="/cms/instructor" className="font-medium text-sm">
                        Instructores
                    </Link>
                ),
                icon: <ContactBook />,
                requiredPermission: "cms.instructor",
            },
            {
                key: "testimonials",
                label: (
                    <Link to="/cms/testimonial" className="font-medium text-sm">
                        Testimonios
                    </Link>
                ),
                icon: <Megaphone />,
                requiredPermission: "cms.testimonial",
            },
            {
                type: "divider",
            },
        ],
    },
    {
        key: "blogs-group",
        label: (
            <span className="text-black-medium font-semibold text-xs uppercase">
                Gestión de Blogs
            </span>
        ),
        type: "group",
        children: [
            {
                key: "blog",
                label: (
                    <Link to="/cms/blog" className="font-medium text-sm">
                        Blog
                    </Link>
                ),
                type: "item",
                icon: <Paper />,
                requiredPermission: "cms.blog",
            },
            {
                key: "categories-and-tags",
                label: "Categorías y Etiquetas",
                icon: <DiscountTag />,
                children: [
                    {
                        key: "categories",
                        label: (
                            <Link
                                to="/cms/blog/categories"
                                className="font-medium text-sm"
                            >
                                Categorías
                            </Link>
                        ),
                        icon: <GeometricShapes />,
                        requiredPermission: "cms.blogcategory",
                    },
                    {
                        key: "tags",
                        label: (
                            <Link to="/cms/blog/tags" className="font-medium text-sm">
                                Etiquetas
                            </Link>
                        ),
                        icon: <DiscountTag />,
                        requiredPermission: "cms.blogtag",
                    },
                ],
            },
        ],
    },
];

const getBaseRoute = (path: string): string => {
    const parts = path.split("/").filter(Boolean);
    if (parts.length >= 2) {
        return `/${parts[0]}/${parts[1]}`; // Retorna /cms/[resource]
    }
    return path;
};

export default function CmsSideBar() {
    const location = useLocation();
    const baseRoute = getBaseRoute(location.pathname);

    // Filtrar elementos del menú basándose en permisos de vista
    const filteredItems = useMenuViewFilter(menuItemsWithPermissions);

    const PATHS: Record<string, string> = {
        "/cms": "dashboard",
        "/cms/instructor": "instructors",
        "/cms/testimonial": "testimonials",
        "/cms/offering": "offering",
        "cms/blog": "blog",
    };

    const selectedKey = PATHS[baseRoute];

    return (
        <Menu
            style={{
                width: 256,
                overflowY: "scroll",
                scrollbarWidth: "none",
                border: 0,
            }}
            defaultSelectedKeys={[selectedKey]}
            defaultOpenKeys={[]}
            mode="inline"
            items={filteredItems}
            className="hidden md:block min-w-64 sticky left-0 top-0"
        />
    );
}
