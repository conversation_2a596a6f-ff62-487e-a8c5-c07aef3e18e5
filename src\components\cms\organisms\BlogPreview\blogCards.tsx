import { getTextColor } from "@lib/helpers";
import { BlogTag as BlogTagType, BlogPost } from "@myTypes/blog";
import { File } from "@myTypes/file";
import { Instructor } from "@myTypes/instructor";
import { Avatar, Switch } from "antd";
import { Link } from "react-router-dom";
import { useState } from "react";
import ResponsivePreview from "@components/shared/molecules/ResponsivePreview";
import clsx from "clsx";
import { useResponsivePreviewContext } from "@components/shared/molecules/ResponsivePreview/config";

type BlogCardProps = {
    location?: "cover" | "gallery";
    category?: string;
    tags?: BlogTagType[];
    title?: string;
    description?: string;
    link?: string;
    img?: File | undefined;
    readtime?: string;
    authors?: Instructor[] | undefined;
};

type BlogTagProps = {
    content: string;
    background?: string;
};

export function BlogTag({ content, background }: BlogTagProps) {
    return (
        <span
            className={"px-4 text-xs text-[#616161] py-1 rounded-full"}
            style={{
                backgroundColor: background || "#E7F890",
                color: getTextColor(background || "#E7F890"),
            }}
        >
            {content}
        </span>
    );
}

export function BlogCard({
    location = "gallery",
    category,
    tags,
    title,
    description,
    link,
    img,
    readtime,
    authors,
}: BlogCardProps) {
    const { isDesktop, isTablet } = useResponsivePreviewContext();
    const galleryContainerClass = isDesktop ? "max-w-[380px] mx-auto" : "";

    {
        /* Cover container classes */
    }
    const coverContainerClass = isDesktop
        ? "grid-cols-6 min-w-[900px]"
        : isTablet
          ? "grid-cols-1 min-w-[500px]"
          : "grid-cols-1";

    const coverImageContainerClass = isDesktop ? "h-full min-h-[380px]" : "h-[380px]";

    if (location === "gallery") {
        return (
            <div className={clsx("space-y-3", galleryContainerClass)}>
                <header className="relative h-[300px] w-full rounded-lg overflow-hidden">
                    <img
                        src={img?.url || ""}
                        width={img?.width || 0}
                        height={img?.height || 0}
                        className="object-cover w-full h-full"
                        sizes="(max-width: 480px) 100vw, 480px"
                        alt={img?.description || title}
                        loading="lazy"
                    />
                    <div className="absolute top-4 left-4 bg-[#eeeded] px-2 py-1 rounded-lg">
                        <span className="text-black text-sm">{category}</span>
                    </div>
                </header>
                <div className="space-y-3">
                    <p className="text-green-full">{readtime}</p>
                    <h3 className="text-xl font-medium text-black-full">{title}</h3>
                    <div className="flex flex-wrap gap-3">
                        {tags?.map((tag) => (
                            <BlogTag
                                key={tag.btid}
                                content={tag.name}
                                background={tag.badgeColor}
                            />
                        ))}
                    </div>
                    <p className="text-sm text-black-full line-clamp-3">
                        {description}
                    </p>
                    <div className="flex flex-wrap gap-x-2 gap-y-1">
                        {authors?.map((author) => (
                            <div className="flex items-center gap-3" key={author.iid}>
                                <Avatar
                                    src={author.profilePhoto?.url || ""}
                                    alt={author.fullName}
                                    className="rounded-full object-cover object-top"
                                    size="large"
                                    style={{
                                        width: 38,
                                        height: 38,
                                    }}
                                >
                                    {author.fullName.slice(0, 1)}
                                </Avatar>

                                <p className="text-[#7c7c7c]">{author.fullName}</p>
                            </div>
                        ))}
                    </div>
                </div>
                <footer className="flex justify-end pr-8">
                    <Link className="font-medium text-[#848484]" to={link || "#"}>
                        Leer más
                    </Link>
                </footer>
            </div>
        );
    } else {
        return (
            <div
                className={clsx(
                    "grid rounded-lg gap-x-4 w-full max-w-[900px] mx-auto",
                    coverContainerClass,
                )}
            >
                <div className="col-span-3 place-content-center h-full w-full">
                    <div
                        className={clsx(
                            "relative w-full rounded-lg",
                            coverImageContainerClass,
                        )}
                    >
                        <img
                            className="h-full w-full object-cover aspect-square rounded-lg"
                            src={img?.url || ""}
                            sizes="(max-width: 480px) 100vw, 480px"
                            alt={img?.description || title}
                            loading="eager"
                        />
                        <div className="absolute bottom-4 left-4 bg-[#eeeded] px-2 py-1 rounded-lg">
                            <span className="text-black text-sm">{category}</span>
                        </div>
                    </div>
                </div>
                <div className="flex flex-col gap-3 col-span-3 w-full">
                    <div className="flex gap-4">
                        <p className="text-[#B4AD0D]">Última publicación</p>
                        <p className="text-[#7DCAFF]">{readtime}</p>
                    </div>
                    <h3 className="text-3xl font-medium text-black-full">{title}</h3>
                    <div className="flex flex-wrap gap-3">
                        {tags?.map((tag) => (
                            <BlogTag
                                key={tag.btid}
                                content={tag.name}
                                background={tag.badgeColor}
                            />
                        ))}
                    </div>
                    <p className="text-sm text-black-full line-clamp-6">
                        {description}
                    </p>
                    {authors?.map((author) => (
                        <div className="flex itAvatarems-center gap-3" key={author.iid}>
                            <Avatar
                                src={author.profilePhoto?.url || ""}
                                alt={author.fullName}
                                className="rounded-full object-cover object-top"
                                size="large"
                                style={{
                                    width: 38,
                                    height: 38,
                                }}
                            >
                                {author.fullName.slice(0, 1)}
                            </Avatar>
                            <p className="text-[#7c7c7c]">{author.fullName}</p>
                        </div>
                    ))}
                    <footer className="flex justify-end pr-8">
                        <Link className="font-medium text-[#848484]" to={link || "#"}>
                            Leer más
                        </Link>
                    </footer>
                </div>
            </div>
        );
    }
}

type BlogCardPreviewProps = {
    blog?: BlogPost;
    thumbnail?: File;
    coverImage?: File;
    previewClassName?: string;
    containerClassName?: string;
};

// Componente interno que usa el contexto responsive
function ResponsiveBlogCardContent({
    blog,
    thumbnail,
    coverImage,
}: BlogCardPreviewProps) {
    const [viewMode, setViewMode] = useState<"cover" | "gallery">("gallery");
    const { isMobile } = useResponsivePreviewContext();

    // Datos de ejemplo para la preview
    const previewData = {
        category: blog?.categories?.[0]?.name || "Categoría",
        tags: blog?.tags || [],
        title: blog?.title || "Título del blog",
        description: blog?.summary || "Descripción del blog...",
        link: "#",
        img: thumbnail || coverImage,
        readtime: `${blog?.readingTime || 5} min de lectura`,
        authors: blog?.authors || [],
    };

    return (
        <div className={"space-y-4"}>
            {/* Toggle para cambiar entre modos */}
            <div>
                <span className={"font-medium text-gray-700"}>Tipo de tarjeta:</span>
                <div className="flex items-center gap-3">
                    <span
                        className={`${viewMode === "gallery" ? "font-medium text-blue-600" : "text-gray-500"}`}
                    >
                        Galería
                    </span>
                    <Switch
                        checked={viewMode === "cover"}
                        onChange={(checked) =>
                            setViewMode(checked ? "cover" : "gallery")
                        }
                        size={isMobile ? "small" : "default"}
                    />
                    <span
                        className={`${viewMode === "cover" ? "font-medium text-blue-600" : "text-gray-500"}`}
                    >
                        Portada
                    </span>
                </div>
            </div>

            {/* Preview de la tarjeta con clases responsivas */}
            <div className={"bg-white rounded-lg"}>
                <BlogCard
                    location={viewMode}
                    category={previewData.category}
                    tags={previewData.tags}
                    title={previewData.title}
                    description={previewData.description}
                    link={previewData.link}
                    img={previewData.img}
                    readtime={previewData.readtime}
                    authors={previewData.authors}
                />
            </div>
        </div>
    );
}

export function BlogCardPreview({ blog, thumbnail, coverImage }: BlogCardPreviewProps) {
    return (
        <ResponsivePreview
            defaultViewport="desktop"
            showDimensions={true}
            className="space-y-4"
            toolbarClassName="border-gray-200"
            previewClassName="p-4"
        >
            <ResponsiveBlogCardContent
                blog={blog}
                thumbnail={thumbnail}
                coverImage={coverImage}
            />
        </ResponsivePreview>
    );
}
