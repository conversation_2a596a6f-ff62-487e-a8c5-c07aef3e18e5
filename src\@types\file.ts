/**
 * File type definition
 * Basado en el serializer FileSerializer del backend
 */
export type File = {
    fid: string;
    name: string;
    description?: string;
    isUsed: boolean;
    url: string;
    width?: number;
    height?: number;
    contentType?: string;

    // Campos adicionales que pueden estar presentes en la respuesta
    bucketName?: string;
    objectName?: string;
    isPrivate?: boolean;
    format?: string;
    size?: number;
    createdAt?: Date;
    updatedAt?: Date;
};

/**
 * File upload options
 * Basado en el serializer FileUploadSerializer del backend
 */
export type FileUploadBody = {
    file: Blob;
    width?: number;
    height?: number;
    description?: string;
    isUsed?: boolean;
    output_format?: "WEBP" | "JPEG" | "PNG";
};

/**
 * File update options
 * Basado en el serializer FileUpdateSerializer del backend
 */
export type FileUpdateBody = Partial<FileUploadBody>;

/**
 * File upload response type
 */
export type FileUploadResponse = {
    file: File;
};
