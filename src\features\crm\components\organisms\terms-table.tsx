import { But<PERSON>, Dropdown, Table, Tooltip } from "antd";
import { Link } from "react-router-dom";
import type { TableProps } from "antd";
import { MoreVertical, Scissors } from "lucide-react";
import EditStroke from "@assets/icons/huge/edit-stroke.svg?react";
import DeleteStroke from "@assets/icons/huge/delete-stroke.svg?react";
import type { Term } from "@/features/crm/types/term";

type TermsTableProps = {
    data: Term[];
    loading?: boolean;
    onEdit?: (term: Term) => void;
    onDelete?: (term: Term) => void;
};

export default function TermsTable({
    data,
    loading,
    onEdit,
    onDelete,
}: TermsTableProps) {
    const baseColumns: TableProps<Term>["columns"] = [
        {
            title: "ID",
            dataIndex: "tid",
            key: "tid",
            width: 120,
            render: (tid: string) => (
                <Link to={`/crm/terms/${tid}`} className="text-blue-full font-semibold">
                    {tid?.slice(-6)}
                </Link>
            ),
        },
        {
            title: "NOMBRE",
            dataIndex: "name",
            key: "name",
        },
    ];

    const actionColumn: TableProps<Term>["columns"] = [
        {
            title: <Scissors />,
            key: "actions",
            width: 80,
            render: (_: unknown, record: Term) => (
                <Dropdown
                    trigger={["click"]}
                    menu={{
                        items: [
                            {
                                key: "edit",
                                label: (
                                    <div className="flex items-center gap-2 text-blue-full">
                                        <EditStroke className="w-5 h-5" /> Editar
                                    </div>
                                ),
                            },
                            {
                                key: "delete",
                                label: (
                                    <div className="flex items-center gap-2 text-state-red-full">
                                        <DeleteStroke className="w-5 h-5" /> Eliminar
                                    </div>
                                ),
                            },
                        ],
                        onClick: ({ key }: { key: string }) => {
                            if (key === "edit" && onEdit) onEdit(record);
                            if (key === "delete" && onDelete) onDelete(record);
                        },
                    }}
                    placement="bottomRight"
                >
                    <Tooltip title="Acciones">
                        <Button
                            icon={<MoreVertical className="w-5 h-5" />}
                            type="text"
                            size="small"
                        />
                    </Tooltip>
                </Dropdown>
            ),
        },
    ];

    return (
        <Table
            className="rounded-lg shadow-sm"
            loading={loading}
            pagination={false}
            dataSource={data}
            rowKey="tid"
            columns={[...baseColumns, ...(onEdit || onDelete ? actionColumn : [])]}
            scroll={{ x: "max-content" }}
        />
    );
}
