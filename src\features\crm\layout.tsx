import Header from "@components/shared/molecules/Header";
import CrmSideBar from "@/features/crm/components/molecules/crm-sidebar";
import { useEffect } from "react";

type CrmLayoutProps = {
    children?: React.ReactNode;
};
export default function CrmLayout({ children }: CrmLayoutProps) {
    useEffect(() => {
        document.title = "CRM - Customer Resources Managment";
    }, []);
    return (
        <div>
            <Header />
            <div className="flex">
                <CrmSideBar />
                <div className="bg-blue-low h-[calc(100vh-140px)] w-full rounded-tl-lg ml-64 p-6 overflow-auto flex justify-center">
                    {children}
                </div>
            </div>
        </div>
    );
}
