import { <PERSON><PERSON>, <PERSON>, Tooltip, Typography } from "antd";
const { Text } = Typography;

interface ContactCardProps {
    name: string;
    phone: string;
    onSend: (phone: string) => void;
}
export default function ContactCard({ name, phone, onSend }: ContactCardProps) {
    return (
        <Card className="mb-3" style={{ borderRadius: "8px" }}>
            <div className="flex justify-between items-center">
                <div>
                    <Text strong>{name}</Text>
                    <div className="flex items-center mt-1">
                        {/* <PhoneOutlined className="mr-2" style={{ color: "#8C8C8C" }} /> */}
                        <Text type="secondary">{phone}</Text>
                    </div>
                </div>
                <Tooltip title="Enviar mensaje">
                    <Button
                        type="text"
                        // icon={<SendOutlined />}
                        onClick={() => onSend(phone)}
                    />
                </Tooltip>
            </div>
        </Card>
    );
}
