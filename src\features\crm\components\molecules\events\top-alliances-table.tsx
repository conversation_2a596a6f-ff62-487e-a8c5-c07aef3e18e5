import React from "react";
import { Card, Table, Progress, Empty, Tooltip } from "antd";
import { Handshake, HandshakeIcon, InfoIcon, UserPlus2Icon, Users } from "lucide-react";
import type { TopAlliance } from "@/features/crm/types/dashboard/events";
import type { ColumnsType } from "antd/es/table";

interface TopAlliancesTableProps {
    data?: TopAlliance[];
    isLoading?: boolean;
}

const TopAlliancesTable: React.FC<TopAlliancesTableProps> = ({
    data = [],
    isLoading = false,
}) => {
    const columns: ColumnsType<TopAlliance> = [
        {
            title: "Alianza",
            dataIndex: "allianceName",
            key: "allianceName",
            width: "25%",
            render: (text: string) => (
                <span className="font-medium text-gray-800">{text}</span>
            ),
        },
        {
            title: () => {
                return (
                    <div className="flex items-center justify-center gap-2">
                        <HandshakeIcon size={16} className="text-blue-500" />
                        <span>Cantidad de Eventos</span>
                        <Tooltip title="Cantidad de eventos en los que la alianza ha participado.">
                            <InfoIcon size={14} />
                        </Tooltip>
                    </div>
                );
            },
            dataIndex: "associatedEventsCount",
            key: "associatedEventsCount",
            width: "15%",
            align: "center",
            render: (count: number) => (
                <span className="text-blue-600 font-semibold">{count}</span>
            ),
        },
        {
            title: () => {
                return (
                    <div className="flex items-center justify-center gap-2">
                        <UserPlus2Icon size={16} className="text-green-600" />
                        <span>Contactos Generados</span>
                        <Tooltip title="Cantidad de personas únicas que la alianza ha generado como contacto.">
                            <InfoIcon size={14} />
                        </Tooltip>
                    </div>
                );
            },
            dataIndex: "uniqueEnrollments",
            key: "uniqueEnrollments",
            width: "15%",
            align: "center",
            render: (count: number) => (
                <span className="text-green-600 font-semibold">{count}</span>
            ),
        },
        {
            title: () => {
                return (
                    <div className="flex items-center justify-center gap-2">
                        <Users size={16} className="text-blue-full" />
                        <span>Total de Inscritos</span>
                        <Tooltip title="Cantidad de inscripciones que ha generado la alianza en los eventos.">
                            <InfoIcon size={14} />
                        </Tooltip>
                    </div>
                );
            },
            dataIndex: "totalEnrollments",
            key: "totalEnrollments",
            width: "15%",
            align: "center",
            render: (count: number) => (
                <span className="text-blue-full font-semibold">{count}</span>
            ),
        },
        // vamos a unir a la columna de porcentaje en una sola
        {
            title: "Participación",
            dataIndex: ["globalParticipationPercentage", "participationPercentage"],
            key: "participationPercentage",
            width: "15%",
            align: "start",
            render: (_, record: TopAlliance) => (
                <div className="flex flex-col">
                    <div>
                        <span>Vs Otras Alianzas</span>
                        <Progress
                            percent={record.participationPercentage}
                            size="small"
                            strokeColor="#52c41a"
                            showInfo={true}
                            format={(percent) => `${percent}%`}
                        />
                    </div>
                    <div>
                        <span>Vs Total de Inscritos</span>
                        <Progress
                            percent={record.globalParticipationPercentage}
                            size="small"
                            strokeColor="#1890ff"
                            showInfo={true}
                            format={(percent) => `${percent}%`}
                        />
                    </div>
                </div>
            ),
        },
    ];

    return (
        <Card
            title={
                <div className="flex items-center gap-2">
                    <Handshake size={20} className="text-blue-500" />
                    <span>Top Alianzas</span>
                </div>
            }
            className="shadow-md"
        >
            {data.length && !isLoading ? (
                <Table
                    columns={columns}
                    dataSource={data}
                    rowKey="alliance_name"
                    pagination={false}
                    size="small"
                    scroll={{ x: 800 }}
                    className="w-full"
                />
            ) : (
                <div className="py-8">
                    <Empty description="No hay datos de alianzas disponibles" />
                </div>
            )}
        </Card>
    );
};

export default TopAlliancesTable;
