import { use<PERSON>ara<PERSON>, use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { Typography, App, Breadcrumb, Tabs, TabsProps } from "antd";
import { useEffect } from "react";
import CrmLayout from "@/features/crm/layout";
import Spinner from "@components/shared/atoms/Spinner";
import WelcomeBar from "@components/shared/molecules/WelcomeBar";
import { Event } from "@/features/crm/types/event";
import { getEvent } from "@/features/crm/services/portals/event";
import GeneralEventDetail from "@/features/crm/components/organisms/GeneralEventDetail";
import EventScheduleTab from "@/features/crm/components/organisms/event-schedule-tab";

const { Text } = Typography;

export default function EventsDetailPage() {
    const { eid } = useParams<{ eid: string }>();
    const navigate = useNavigate();
    const { notification } = App.useApp();

    const {
        data: event,
        isLoading,
        error,
    } = useQuery({
        queryKey: ["event", eid],
        queryFn: async () => {
            const response = await getEvent(eid as string);
            return response as Event;
        },
        enabled: !!eid,
    });

    useEffect(() => {
        if (error) {
            notification.error({
                message: "Error",
                description: "Error al cargar el evento",
            });
            navigate("/crm/events", { replace: true });
        }
    }, [error, notification, navigate]);

    const tabItems: TabsProps["items"] = [
        {
            key: "general",
            label: "General",
            children: event && <GeneralEventDetail event={event} />,
        },
        {
            key: "schedules",
            label: "Programaciones",
            children: event && <EventScheduleTab event={event} />,
        },
        {
            key: "enrollments",
            label: "Inscripciones",
            children: <></>,
            disabled: true,
        },
    ];

    if (isLoading) {
        return (
            <CrmLayout>
                <Spinner />
            </CrmLayout>
        );
    }

    if (!event) {
        return (
            <CrmLayout>
                <div className="flex items-center justify-center h-full">
                    <Text type="danger">Error al cargar el evento</Text>
                </div>
            </CrmLayout>
        );
    }

    return (
        <CrmLayout>
            <div className="max-w-7xl w-full h-full space-y-5">
                <div className="flex justify-between items-center">
                    <WelcomeBar helperText="Edita aquí los detalles de un Evento." />
                </div>
                <div className="p-5 bg-white-full rounded-lg space-y-5 shadow-sm">
                    <div className="flex items-center justify-between">
                        <Breadcrumb
                            separator=">"
                            items={[
                                {
                                    title: (
                                        <Link to="/crm/events" className="text-base">
                                            Eventos
                                        </Link>
                                    ),
                                },
                                {
                                    title: (
                                        <Link
                                            to={`/crm/events/${event.eid}`}
                                            className="text-base"
                                        >
                                            {event.name}
                                        </Link>
                                    ),
                                },
                                {
                                    title: (
                                        <Text className="text-base">Ver & Editar</Text>
                                    ),
                                },
                            ]}
                        />
                    </div>
                </div>
                <Tabs items={tabItems} />
            </div>
        </CrmLayout>
    );
}
