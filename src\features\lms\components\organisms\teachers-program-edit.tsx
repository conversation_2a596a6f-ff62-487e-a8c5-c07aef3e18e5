import { useState } from "react";
import {
    <PERSON><PERSON>,
    <PERSON>,
    Typo<PERSON>,
    Avatar,
    List,
    Modal,
    Form,
    Input,
    message,
    Tag,
    Popconfirm,
    Empty,
    Row,
    Col,
} from "antd";
import {
    UserPlus,
    Edit3,
    Trash2,
    GraduationCap,
    Mail,
    Phone,
    CloudUpload,
} from "lucide-react";
import ImgCrop from "antd-img-crop";
import <PERSON><PERSON> from "antd/es/upload/Dragger";

const { Title, Text } = Typography;
const { TextArea } = Input;

interface Teacher {
    id: string;
    fullName: string;
    professionalTitle: string;
    email?: string;
    phone?: string;
    avatar?: string;
    bio?: string;
    specialties?: string[];
}

// interface TeachersProgramEditProps {
//     oid: string;
//     data: Program;
//     onRefetch: () => void;
// }

interface TeacherFormValues {
    fullName: string;
    professionalTitle: string;
    email?: string;
    phone?: string;
    bio?: string;
    specialties?: string;
}

const PROFILE_IMAGE_WIDTH = 276;
const PROFILE_IMAGE_HEIGHT = 386;

// Mock data for demonstration
const mockTeachers: Teacher[] = [
    {
        id: "1",
        fullName: "<PERSON>. <PERSON>",
        professionalTitle: "Doctora en Ciencias de la Computación",
        email: "<EMAIL>",
        phone: "+51 999 123 456",
        avatar: undefined,
        bio: "Especialista en inteligencia artificial y machine learning con más de 15 años de experiencia en investigación y docencia.",
        specialties: ["Inteligencia Artificial", "Machine Learning", "Data Science"],
    },
    {
        id: "2",
        fullName: "Ing. Carlos Alberto Mendoza",
        professionalTitle: "Ingeniero de Software Senior",
        email: "<EMAIL>",
        phone: "+51 999 654 321",
        avatar: undefined,
        bio: "Desarrollador full-stack con experiencia en tecnologías web modernas y arquitecturas de microservicios.",
        specialties: ["Desarrollo Web", "React", "Node.js", "Microservicios"],
    },
];

export default function TeachersProgramEdit() {
    const [teachers, setTeachers] = useState<Teacher[]>(mockTeachers);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [editingTeacher, setEditingTeacher] = useState<Teacher | null>(null);
    const [form] = Form.useForm<TeacherFormValues>();

    const handleAddTeacher = () => {
        setEditingTeacher(null);
        form.resetFields();
        setIsModalOpen(true);
    };

    const handleEditTeacher = (teacher: Teacher) => {
        setEditingTeacher(teacher);
        form.setFieldsValue({
            fullName: teacher.fullName,
            professionalTitle: teacher.professionalTitle,
            email: teacher.email,
            phone: teacher.phone,
            bio: teacher.bio,
            specialties: teacher.specialties?.join(", "),
        });
        setIsModalOpen(true);
    };

    const handleDeleteTeacher = (teacherId: string) => {
        setTeachers(teachers.filter((teacher) => teacher.id !== teacherId));
        message.success("Docente eliminado del programa");
    };

    const handleSubmit = (values: TeacherFormValues) => {
        // TODO: Implement save functionality
        console.log("Saving teachers:", values);
    };

    return (
        <div className="space-y-6">
            <Card className="shadow-sm">
                <div className="flex justify-between items-center mb-6">
                    <div>
                        <Title level={4} className="mb-1">
                            Docentes del Programa
                        </Title>
                        <Text type="secondary">
                            Gestiona los docentes asignados a este programa
                        </Text>
                    </div>
                    <Button
                        type="primary"
                        icon={<UserPlus size={16} />}
                        onClick={handleAddTeacher}
                        className="bg-blue-full hover:bg-blue-600"
                    >
                        Agregar Docente
                    </Button>
                </div>

                {teachers.length === 0 ? (
                    <Empty
                        description="No hay docentes asignados"
                        image={Empty.PRESENTED_IMAGE_SIMPLE}
                    >
                        <Button
                            type="primary"
                            icon={<UserPlus size={16} />}
                            onClick={handleAddTeacher}
                        >
                            Agregar Primer Docente
                        </Button>
                    </Empty>
                ) : (
                    <List
                        dataSource={teachers}
                        className="divide-y"
                        renderItem={(teacher) => (
                            <List.Item
                                key={teacher.id}
                                className="rounded-lg p-4 mb-4 "
                                actions={[
                                    <Button
                                        type="text"
                                        icon={<Edit3 size={16} />}
                                        onClick={() => handleEditTeacher(teacher)}
                                        className="text-blue-600 hover:text-blue-800"
                                    >
                                        Editar
                                    </Button>,
                                    <Popconfirm
                                        title="¿Remover docente?"
                                        description="El docente será removido de este programa"
                                        onConfirm={() =>
                                            handleDeleteTeacher(teacher.id)
                                        }
                                        okText="Remover"
                                        cancelText="Cancelar"
                                    >
                                        <Button
                                            type="text"
                                            danger
                                            icon={<Trash2 size={16} />}
                                        >
                                            Remover
                                        </Button>
                                    </Popconfirm>,
                                ]}
                            >
                                <List.Item.Meta
                                    avatar={
                                        <Avatar
                                            size={64}
                                            src={teacher.avatar}
                                            className="border-2 border-white shadow-md"
                                        >
                                            {teacher.fullName
                                                .split(" ")[0]
                                                .toUpperCase()}
                                        </Avatar>
                                    }
                                    title={
                                        <div className="space-y-1">
                                            <div className="flex items-center gap-2">
                                                <Text strong className="text-lg">
                                                    {teacher.fullName}
                                                </Text>
                                                <GraduationCap
                                                    size={16}
                                                    className="text-blue-500"
                                                />
                                            </div>
                                            <Text type="secondary" className="text-sm">
                                                {teacher.professionalTitle}
                                            </Text>
                                        </div>
                                    }
                                    description={
                                        <div className="space-y-3 mt-2">
                                            {teacher.bio && (
                                                <Text className="text-sm text-gray-600">
                                                    {teacher.bio}
                                                </Text>
                                            )}

                                            <Row gutter={16} className="flex flex-col">
                                                {teacher.email && (
                                                    <Col xs={24} sm={12}>
                                                        <div className="flex items-center gap-2">
                                                            <Mail
                                                                size={14}
                                                                className="text-gray-500"
                                                            />
                                                            <Text className="text-sm">
                                                                {teacher.email}
                                                            </Text>
                                                        </div>
                                                    </Col>
                                                )}
                                                {teacher.phone && (
                                                    <Col xs={24} sm={12}>
                                                        <div className="flex items-center gap-2">
                                                            <Phone
                                                                size={14}
                                                                className="text-gray-500"
                                                            />
                                                            <Text className="text-sm">
                                                                {teacher.phone}
                                                            </Text>
                                                        </div>
                                                    </Col>
                                                )}
                                            </Row>

                                            {teacher.specialties &&
                                                teacher.specialties.length > 0 && (
                                                    <div>
                                                        <Text
                                                            strong
                                                            className="text-sm text-gray-700"
                                                        >
                                                            Especialidades:
                                                        </Text>
                                                        <div className="flex flex-wrap gap-1 mt-1">
                                                            {teacher.specialties.map(
                                                                (specialty, index) => (
                                                                    <Tag
                                                                        key={index}
                                                                        color="blue"
                                                                        className="text-xs"
                                                                    >
                                                                        {specialty}
                                                                    </Tag>
                                                                ),
                                                            )}
                                                        </div>
                                                    </div>
                                                )}
                                        </div>
                                    }
                                />
                            </List.Item>
                        )}
                    />
                )}
            </Card>

            {/* Teacher Modal */}
            <Modal
                title={
                    <div className="flex items-center gap-2">
                        <UserPlus size={20} />
                        {editingTeacher ? "Editar Docente" : "Agregar Docente"}
                    </div>
                }
                open={isModalOpen}
                onCancel={() => setIsModalOpen(false)}
                footer={null}
                width={600}
            >
                <Form
                    form={form}
                    onFinish={handleSubmit}
                    layout="vertical"
                    className="mt-4"
                >
                    <Row gutter={16}>
                        <Col xs={24} md={12}>
                            <Form.Item
                                name="fullName"
                                label="Nombre Completo"
                                rules={[
                                    {
                                        required: true,
                                        message: "El nombre es requerido",
                                    },
                                ]}
                            >
                                <Input placeholder="Ej: Dr. María Elena Rodríguez" />
                            </Form.Item>
                        </Col>
                        <Col xs={24} md={12}>
                            <Form.Item
                                name="professionalTitle"
                                label="Título Profesional"
                                rules={[
                                    {
                                        required: true,
                                        message: "El título es requerido",
                                    },
                                ]}
                            >
                                <Input placeholder="Ej: Doctora en Ciencias de la Computación" />
                            </Form.Item>
                        </Col>
                    </Row>

                    <Row gutter={16}>
                        <Col xs={24} md={12}>
                            <Form.Item
                                name="email"
                                label="Correo Electrónico"
                                rules={[
                                    {
                                        type: "email",
                                        message: "Ingresa un email válido",
                                    },
                                ]}
                            >
                                <Input placeholder="<EMAIL>" />
                            </Form.Item>
                        </Col>
                        <Col xs={24} md={12}>
                            <Form.Item name="phone" label="Teléfono">
                                <Input placeholder="+51 999 123 456" />
                            </Form.Item>
                        </Col>
                    </Row>

                    <Form.Item name="bio" label="Biografía">
                        <TextArea
                            rows={3}
                            placeholder="Breve descripción de la experiencia y especialidades del docente"
                        />
                    </Form.Item>

                    <Form.Item
                        name="specialties"
                        label="Especialidades"
                        help="Separa las especialidades con comas"
                    >
                        <Input placeholder="Ej: Inteligencia Artificial, Machine Learning, Data Science" />
                    </Form.Item>

                    <Form.Item
                        name="profilePhotoFile"
                        label={
                            <span className="font-semibold text-base">
                                Foto de Perfil
                            </span>
                        }
                        rules={[
                            {
                                required: true,
                                message: "Por favor ingrese una imagen de perfil",
                            },
                        ]}
                        valuePropName="fileList"
                        getValueFromEvent={(e) => {
                            if (Array.isArray(e)) {
                                return e;
                            }
                            return e && e.fileList;
                        }}
                    >
                        <ImgCrop
                            aspect={PROFILE_IMAGE_WIDTH / PROFILE_IMAGE_HEIGHT}
                            showGrid={true}
                            modalTitle="Editar imagen de perfil"
                            modalOk="Guardar"
                            modalCancel="Cancelar"
                        >
                            <Dragger maxCount={1} multiple={false} listType="picture">
                                <div className="flex flex-col justify-center items-center">
                                    <CloudUpload />
                                    <Text className="font-medium text-black-full">
                                        Arrastre una imagen de perfil o haga click aquí
                                    </Text>
                                    <Text className="text-xs text-black-medium">
                                        Este campo admite formatos de imagen. Solo una
                                        imagen
                                    </Text>
                                </div>
                            </Dragger>
                        </ImgCrop>
                    </Form.Item>

                    <div className="flex justify-end gap-3 pt-4 border-t">
                        <Button onClick={() => setIsModalOpen(false)}>Cancelar</Button>
                        <Button type="primary" htmlType="submit">
                            {editingTeacher ? "Actualizar" : "Agregar"} Docente
                        </Button>
                    </div>
                </Form>
            </Modal>
        </div>
    );
}
