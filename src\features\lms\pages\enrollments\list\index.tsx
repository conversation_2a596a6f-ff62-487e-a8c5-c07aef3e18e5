import { useMemo, useState } from "react";
import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    Drawer,
    Input,
    Modal,
    Pagination,
    Typography,
    Select,
} from "antd";
import LmsLayout from "@/features/lms/layout";
import { Plus, Users, UserCheck, UserX, SlidersHorizontal } from "lucide-react";
import WelcomeBar from "@components/shared/molecules/WelcomeBar";
import { useSearchParams, useNavigate } from "react-router-dom";
import { useEnrollments } from "../../../hooks/useEnrollments";
import { useDeleteEnrollment } from "../../../hooks/useDeleteEnrollment";
import {
    EnrollmentQueryParams,
    DEFAULT_PAGE,
    DEFAULT_PAGE_SIZE,
} from "../../../types/enrollment";
import EnrollmentsTable from "../../../components/organisms/enrollments-table";
import CreateEnrollmentForm from "../../../components/organisms/create-enrollment-form";

const { Search } = Input;
const { Text } = Typography;
const { Option } = Select;

const STATUS_FILTER_TAGS = [
    {
        value: "all",
        label: "Todos",
        icon: "Users",
    },
    {
        value: "active",
        label: "Activos",
        icon: "UserCheck",
    },
    {
        value: "inactive",
        label: "Inactivos",
        icon: "UserX",
    },
    {
        value: "certified",
        label: "Certificados",
        icon: "UserCheck",
    },
];

export default function EnrollmentsListPage() {
    const [searchParams, setSearchParams] = useSearchParams();
    const [modalOpen, setModalOpen] = useState(false);
    const [isFilterDrawerOpen, setIsFilterDrawerOpen] = useState(false);
    const navigate = useNavigate();

    const mainFilterTag = searchParams.get("mainFilterTag") || "all";
    const page = searchParams.get("page")
        ? Number(searchParams.get("page"))
        : DEFAULT_PAGE;
    const pageSize = searchParams.get("pageSize")
        ? Number(searchParams.get("pageSize"))
        : DEFAULT_PAGE_SIZE;
    const search = searchParams.get("search");
    const offering = searchParams.get("offering") || "";

    const [filterOffering, setFilterOffering] = useState<string>(offering);

    const queryParams: EnrollmentQueryParams = useMemo(
        () => ({
            page: Number(page),
            pageSize: Number(pageSize),
            ...(search ? { search } : {}),
            ...(mainFilterTag === "active" ? { isActive: true } : {}),
            ...(mainFilterTag === "inactive" ? { isActive: false } : {}),
            ...(mainFilterTag === "certified" ? { certificateIssued: true } : {}),
            ...(offering ? { offering } : {}),
        }),
        [page, pageSize, mainFilterTag, search, offering],
    );

    const {
        isLoading,
        enrollments,
        count: TOTAL_ENROLLMENTS,
    } = useEnrollments(queryParams);
    const { mutate: deleteMutate } = useDeleteEnrollment();

    const onSearch = (value: string) => {
        setSearchParams((prev) => {
            if (value.trim()) {
                prev.set("search", value.trim());
            } else {
                prev.delete("search");
            }
            prev.set("page", "1");
            return prev;
        });
    };

    const handleCloseModal = () => {
        setModalOpen(false);
    };

    const handleSetPage = (value: number, pageSize: number) => {
        setSearchParams((prev) => {
            prev.set("page", value.toString());
            prev.set("pageSize", pageSize.toString());
            return prev;
        });
    };

    const handleSetMainFilterTag = (value: string) => {
        setSearchParams((prev) => {
            prev.set("mainFilterTag", value);
            prev.set("page", "1");
            return prev;
        });
    };

    const applyFilters = () => {
        setSearchParams((prev) => {
            if (filterOffering) {
                prev.set("offering", filterOffering);
            } else {
                prev.delete("offering");
            }
            prev.set("page", "1");
            return prev;
        });
        setIsFilterDrawerOpen(false);
    };

    const clearFilters = () => {
        setFilterOffering("");
        setSearchParams((prev) => {
            prev.delete("offering");
            prev.delete("mainFilterTag");
            prev.delete("search");
            prev.set("page", "1");
            return prev;
        });
        setIsFilterDrawerOpen(false);
    };

    const handleEditEnrollment = (enrollment: (typeof enrollments)[0]) => {
        navigate(`/lms/enrollments/${enrollment.eid}`);
    };

    const handleDeleteEnrollment = (enrollment: (typeof enrollments)[0]) => {
        deleteMutate(enrollment.eid);
    };

    return (
        <LmsLayout>
            <div className="w-full h-full space-y-5">
                <div className="flex justify-between items-center">
                    <WelcomeBar helperText="Gestiona aquí las matrículas de los estudiantes" />
                    <div className="flex gap-3">
                        <Button
                            type="primary"
                            size="large"
                            style={{ fontSize: 16 }}
                            icon={<Plus />}
                            onClick={() => {
                                setModalOpen(true);
                            }}
                        >
                            Agregar
                        </Button>
                        <Modal
                            centered
                            open={modalOpen}
                            onCancel={handleCloseModal}
                            footer={false}
                            title={
                                <div className="w-full flex justify-center text-2xl py-4">
                                    Crear Nueva Matrícula
                                </div>
                            }
                        >
                            <CreateEnrollmentForm
                                onSuccess={handleCloseModal}
                                onCancel={handleCloseModal}
                            />
                        </Modal>
                    </div>
                </div>

                <div className="p-5 bg-white-full-full rounded-lg space-y-5">
                    <div className="flex flex-col lg:flex-row justify-between items-center">
                        <Text className="text-black text-2xl font-semibold">
                            Matrículas{" "}
                            <Badge
                                count={TOTAL_ENROLLMENTS}
                                color="blue"
                                size="default"
                            />
                        </Text>

                        <div className="flex items-center gap-3">
                            <Search
                                size="large"
                                placeholder="Buscar por nombre, email o producto"
                                onSearch={onSearch}
                                enterButton
                                allowClear
                                className="max-w-screen-sm"
                                defaultValue={search || ""}
                            />

                            <div className="relative">
                                <Button
                                    icon={<SlidersHorizontal size={16} />}
                                    onClick={() => setIsFilterDrawerOpen(true)}
                                >
                                    Filtros
                                </Button>
                                {offering && (
                                    <Badge
                                        count={1}
                                        size="small"
                                        className="absolute -top-1 -right-1"
                                    />
                                )}
                            </div>
                        </div>
                    </div>

                    <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg shadow-sm">
                        {STATUS_FILTER_TAGS.map((tag) => {
                            let TagIcon;
                            switch (tag.icon) {
                                case "Users":
                                    TagIcon = Users;
                                    break;
                                case "UserCheck":
                                    TagIcon = UserCheck;
                                    break;
                                case "UserX":
                                    TagIcon = UserX;
                                    break;
                                default:
                                    TagIcon = Users;
                            }

                            return (
                                <button
                                    key={tag.value}
                                    onClick={() => handleSetMainFilterTag(tag.value)}
                                    className={`px-4 py-2 text-sm font-medium rounded-full transition-all duration-200 flex items-center gap-1.5 ${
                                        mainFilterTag === tag.value
                                            ? "bg-blue-500 text-white-full shadow-md"
                                            : "bg-white-full text-gray-600 hover:bg-gray-100"
                                    }`}
                                >
                                    <TagIcon size={14} />
                                    {tag.label}
                                </button>
                            );
                        })}
                    </div>

                    {offering && (
                        <div className="flex flex-wrap items-center gap-2 p-3 bg-blue-50 rounded-lg border border-blue-200">
                            <Text type="secondary" className="text-sm font-medium">
                                Filtros activos:
                            </Text>
                            <Badge
                                count="Producto"
                                color="blue"
                                style={{ backgroundColor: "#1890ff" }}
                            />
                            <Button
                                type="link"
                                size="small"
                                onClick={clearFilters}
                                className="text-blue-600 hover:text-blue-800 p-0 h-auto"
                            >
                                Limpiar todos
                            </Button>
                        </div>
                    )}

                    <EnrollmentsTable
                        enrollments={enrollments}
                        loading={isLoading}
                        onEdit={handleEditEnrollment}
                        onDelete={handleDeleteEnrollment}
                    />

                    <div className="flex justify-between">
                        <div>
                            <Text className="text-sm text-gray-500">
                                Mostrando {enrollments.length} de {TOTAL_ENROLLMENTS}{" "}
                                matrículas
                            </Text>
                        </div>
                        <Pagination
                            showSizeChanger
                            total={TOTAL_ENROLLMENTS}
                            pageSize={pageSize}
                            current={page}
                            onChange={(page, pageSize) => {
                                handleSetPage(page, pageSize);
                            }}
                        />
                    </div>
                </div>

                <Drawer
                    title="Aplicar filtros"
                    placement="right"
                    closable={true}
                    onClose={() => setIsFilterDrawerOpen(false)}
                    open={isFilterDrawerOpen}
                    width={400}
                    footer={
                        <div className="flex gap-2">
                            <Button
                                type="primary"
                                onClick={applyFilters}
                                className="flex-1"
                            >
                                Aplicar filtros
                            </Button>
                            <Button onClick={clearFilters} className="flex-1">
                                Limpiar
                            </Button>
                        </div>
                    }
                >
                    <div className="space-y-4">
                        <div>
                            <Text className="font-medium mb-2 block">Producto</Text>
                            <Select
                                placeholder="Seleccionar producto"
                                value={filterOffering || undefined}
                                onChange={setFilterOffering}
                                className="w-full"
                                allowClear
                                showSearch
                                filterOption={(input, option) =>
                                    option?.label
                                        ?.toString()
                                        .toLowerCase()
                                        .includes(input.toLowerCase()) ?? false
                                }
                            >
                                <Option value="course-1">
                                    Curso de Programación Básica
                                </Option>
                                <Option value="course-2">
                                    Diplomado en Data Science
                                </Option>
                                <Option value="course-3">Especialización en IA</Option>
                                <Option value="course-4">Workshop de DevOps</Option>
                            </Select>
                        </div>
                    </div>
                </Drawer>
            </div>
        </LmsLayout>
    );
}
