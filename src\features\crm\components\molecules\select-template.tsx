import { useTemplates, UseTemplatesQuery } from "@/features/crm/hooks/use-template";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Select, SelectProps } from "antd";
import { ExternalLink, Plus } from "lucide-react";
import { useState } from "react";
import { Link } from "react-router-dom";
import { useDebounce } from "@hooks/use-debounce";
import { Template, TemplateStatus } from "@/features/crm/types/template";
import CreateTemplateForm from "../organisms/create-template-form";

interface SelectTemplateProps extends Omit<SelectProps, "options"> {
    value?: string;
    onChange?: (value: string) => void;
    selectedTemplate?: Template;
}

export default function SelectTemplate({
    value,
    onChange,
    selectedTemplate,
    ...restProps
}: SelectTemplateProps) {
    const [query, setQuery] = useState<UseTemplatesQuery | null>(null);
    const debouncedQuery = useDebounce(query, 1000);
    const [modalOpen, setModalOpen] = useState(false);

    const { templates, isLoading, COUNT } = useTemplates({
        page: 1,
        pageSize: 20,
        query: {
            search: debouncedQuery?.search,
        },
    });

    // Crear opciones de Templates, solo mostrar las aprobadas
    const templateOptions: SelectProps["options"] =
        templates
            .filter((template) => template.status === TemplateStatus.APPROVED)
            .map((template) => ({
                value: template.tid,
                label: template.name,
                info: template,
            })) || [];

    const options = [...templateOptions];
    if (value && selectedTemplate) {
        const isValueInOptions = templateOptions.some(
            (option) => option.value === value,
        );

        if (!isValueInOptions) {
            options.unshift({
                value: value,
                label: selectedTemplate.name,
                info: selectedTemplate,
            });
        }
    }

    const handleSearch = (value: string) => {
        setQuery({ search: value });
    };

    const handleClear = () => {
        setQuery(null);
    };

    const templateRefLink = (template: Template) => {
        if (template.extReference) {
            return `https://crm.tokechat.net/flows/${template.extReference}`;
        }
        return `/crm/templates/${template.tid}`;
    };

    return (
        <div className="flex items-center gap-2">
            <Modal
                title={
                    <div className="text-lg font-semibold text-center">
                        Crear plantilla
                    </div>
                }
                footer={false}
                open={modalOpen}
                centered
                onCancel={() => {
                    setModalOpen(false);
                }}
            >
                <CreateTemplateForm closeModal={() => setModalOpen(false)} />
            </Modal>
            <Select
                {...restProps}
                value={value}
                onChange={onChange}
                onSearch={handleSearch}
                onClear={handleClear}
                options={options}
                placeholder="Buscar por nombre"
                optionRender={(option) => (
                    <div className="flex justify-between items-center">
                        <div className="flex flex-col text-wrap">
                            <span>{option.data.label}</span>
                        </div>

                        <Link
                            to={templateRefLink(option.data.info)}
                            title="View Template"
                            target="_blank"
                        >
                            <ExternalLink size={14} />
                        </Link>
                    </div>
                )}
                loading={isLoading}
                filterOption={false}
                allowClear
                showSearch
                dropdownRender={(menu) => (
                    <>
                        {menu}
                        <Divider className="my-1" />
                        {query !== null &&
                        query.search !== "" &&
                        COUNT > templateOptions.length ? (
                            // Es posible que no se encuentre en la lista de no aprobadas
                            <div className="flex justify-between items-center px-2">
                                <div className="text-xs text-gray-700 font-medium">
                                    <span>
                                        Es posible que la plantilla no esté aprobada{" "}
                                    </span>
                                    <Link
                                        to={`/crm/templates?search=${query?.search}`}
                                        className="text-blue-medium flex items-center gap-1 whitespace-nowrap"
                                    >
                                        Verifícalo aquí
                                        <ExternalLink size={14} />
                                    </Link>
                                </div>
                            </div>
                        ) : (
                            // No se encontró, incluso en la lista de no aprobadas
                            <div className="flex justify-between items-center px-2">
                                <p className="text-sm text-gray-700 font-medium">
                                    ¿No encuentras la plantilla?
                                </p>
                                <Button
                                    size="small"
                                    type="primary"
                                    icon={<Plus size={12} />}
                                    onClick={() => setModalOpen(true)}
                                >
                                    Agregar
                                </Button>
                            </div>
                        )}
                    </>
                )}
            />
        </div>
    );
}
