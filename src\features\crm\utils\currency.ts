import { OrderCurrency } from "../types/order";

/**
 * Determina la moneda según el número de teléfono del cliente.
 * Si el teléfono empieza con "+51" o "51", se asume que es Perú y la moneda es PEN (Soles).
 * De lo contrario, se asume que es un cliente extranjero y la moneda es USD (Dólares).
 */
export const getCurrencyByPhoneNumber = (phoneNumber?: string): OrderCurrency => {
    if (!phoneNumber) return OrderCurrency.PEN; // valor por defecto

    // Eliminar cualquier espacio en blanco
    const cleanPhoneNumber = phoneNumber.replace(/\s/g, "");

    // Comprobar si el número comienza con +51 o 51
    if (cleanPhoneNumber.startsWith("+51") || cleanPhoneNumber.startsWith("51")) {
        return OrderCurrency.PEN;
    }

    // Para cualquier otro prefijo, devolver USD
    return OrderCurrency.USD;
};
