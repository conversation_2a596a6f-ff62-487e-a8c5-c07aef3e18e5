import { Config<PERSON>rovider, Table, Tooltip, Typography } from "antd";
import type { TableProps } from "antd";
import { usePayments } from "../../hooks/use-payment";
import { PaymentListItem, PaymentOrder } from "../../types/payment";
import { useMemo } from "react";
import { formatDateTime } from "@lib/helpers";
import PaymentOrderCell from "../atoms/payment-order-cell";
import { Link } from "react-router-dom";
import PaymentStatusCell from "../atoms/payment-status-cell";
import PaymentAmountCell from "../atoms/payment-amount-cell";
import { XOctagon, CheckCircle } from "lucide-react";

const { Text } = Typography;

const INITIAL_COLUMNS: TableProps<PaymentListItem>["columns"] = [
    {
        title: "ID",
        dataIndex: "pid",
        key: "pid",
        render: (pid: string) => (
            <Tooltip title="Ver detalles de Pago">
                <Link
                    to={`/crm/payments/${pid}`}
                    className="font-semibold text-blue-full"
                >
                    {pid.slice(0, 6)}
                </Link>
            </Tooltip>
        ),
    },
    {
        title: "ORDEN/VENTA",
        dataIndex: "order",
        key: "order",
        render: (order: PaymentOrder) => <PaymentOrderCell order={order} />,
    },
    {
        title: "ESTADO DE PAGO",
        dataIndex: "isPaid",
        render: (isPaid: boolean, record: PaymentListItem) => (
            <PaymentStatusCell isPaid={isPaid} payment={record} />
        ),
    },
    {
        title: "ESTADO",
        dataIndex: "isLost",
        key: "isLost",
        render: (isLost: boolean) => (
            <div className="flex items-center justify-center">
                {isLost ? (
                    <Tooltip title="Pago perdido">
                        <XOctagon size={18} className="text-red-600" strokeWidth={2} />
                    </Tooltip>
                ) : (
                    <Tooltip title="Pago activo">
                        <CheckCircle
                            size={18}
                            className="text-green-600"
                            strokeWidth={2}
                        />
                    </Tooltip>
                )}
            </div>
        ),
    },
    {
        title: "TIPO",
        dataIndex: "isFirstPayment",
        key: "isFirstPayment",
        render: (isFirstPayment: boolean) => (
            <div className="flex items-center">
                {isFirstPayment ? (
                    <span className="text-xs px-2 py-1 rounded-full bg-blue-100 text-blue-800 border border-blue-300 font-medium">
                        Primer Pago
                    </span>
                ) : (
                    <span className="text-xs px-2 py-1 rounded-full bg-gray-100 text-gray-600 border border-gray-300 font-medium">
                        Seguimiento
                    </span>
                )}
            </div>
        ),
    },
    {
        title: "MONTO",
        dataIndex: "amount",
        render: (amount: number, record: PaymentListItem) => (
            <PaymentAmountCell amount={amount} payment={record} />
        ),
    },
    {
        title: "ÚLTIMA ACTUALIZACIÓN",
        dataIndex: "updatedAt",
        key: "updatedAt",
        render: (updatedAt: string) => {
            const formattedDate = formatDateTime(updatedAt);
            return <Text>{formattedDate}</Text>;
        },
    },
    {
        title: "FECHA DE CREACIÓN",
        dataIndex: "createdAt",
        key: "createdAt",
        render: (createdAt: string) => {
            const formattedDate = formatDateTime(createdAt);
            return <Text>{formattedDate}</Text>;
        },
    },
];

type PaymentsTableProps = {
    initialData?: PaymentListItem[];
};

export default function PaymentsTable({ initialData }: PaymentsTableProps) {
    const { payments } = usePayments({ enabled: !initialData });

    const columns = useMemo(() => INITIAL_COLUMNS, []);
    return (
        <>
            <ConfigProvider
                theme={{
                    components: {
                        Table: {
                            headerBg: "#FBFCFD",
                            borderColor: "#fff",
                            headerSplitColor: "#fafafa",
                            headerBorderRadius: 8,
                            rowHoverBg: "#F6FAFD",
                            rowSelectedBg: "#F6FAFD",
                            rowSelectedHoverBg: "#F6FAFD",
                            footerBg: "#F1F1F1",
                        },
                    },
                }}
            >
                <Table
                    className="rounded-lg"
                    footer={() => ""}
                    pagination={false}
                    columns={columns}
                    dataSource={initialData || payments}
                />
            </ConfigProvider>
        </>
    );
}
