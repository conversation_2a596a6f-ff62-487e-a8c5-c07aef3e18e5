import React from "react";
import { Card } from "antd";
import {
    RadarChart,
    PolarGrid,
    PolarAngleAxis,
    PolarRadiusAxis,
    Radar,
    ResponsiveContainer,
    Legend,
    Tooltip,
} from "recharts";

type LegendItem = {
    name: string;
    color: string;
};

interface RadarChartCardProps {
    title: string;
    data: Array<Record<string, unknown>>;
    dataKeys: string[];
    angleDataKey: string;
    colors?: string[];
    icon?: React.ReactNode;
    formatter?: (value: number) => string;
    legend?: LegendItem[];
}

const RadarChartCard: React.FC<RadarChartCardProps> = ({
    title,
    data,
    dataKeys,
    angleDataKey,
    colors = ["#1890ff", "#faad14", "#52c41a", "#eb2f96"],
    icon,
    formatter,
    legend,
}) => {
    interface CustomTooltipProps {
        active?: boolean;
        payload?: Array<{
            value: number;
            dataKey: string;
            name: string;
            color: string;
        }>;
        label?: string;
    }

    const CustomTooltip = ({ active, payload, label }: CustomTooltipProps) => {
        if (active && payload && payload.length) {
            return (
                <div className="bg-white-full p-2 border border-gray-200 shadow-md rounded">
                    <p className="font-medium">{`${label}`}</p>
                    {payload.map((entry, index) => (
                        <p key={index} style={{ color: entry.color }}>
                            {entry.name}:{" "}
                            {formatter ? formatter(entry.value) : entry.value}
                        </p>
                    ))}
                </div>
            );
        }
        return null;
    };

    // Determinar el nombre a mostrar para cada dataKey
    const getDataKeyName = (index: number) => {
        if (legend && legend[index]) {
            return legend[index].name;
        }
    };

    return (
        <Card
            title={
                <div className="flex items-center gap-2">
                    {icon}
                    <span>{title}</span>
                </div>
            }
            className="shadow-md h-full"
        >
            <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                    <RadarChart
                        data={data}
                        margin={{ top: 20, right: 30, bottom: 20, left: 30 }}
                    >
                        <PolarGrid />
                        <PolarAngleAxis dataKey={angleDataKey} />
                        <PolarRadiusAxis
                            angle={90}
                            domain={[0, "auto"]}
                            tickFormatter={(value) =>
                                formatter ? formatter(value) : value
                            }
                        />
                        <Tooltip content={<CustomTooltip />} />

                        {dataKeys.map((dataKey, index) => (
                            <Radar
                                key={dataKey}
                                name={getDataKeyName(index)}
                                dataKey={dataKey}
                                stroke={colors[index % colors.length]}
                                fill={colors[index % colors.length]}
                                fillOpacity={0.2}
                                strokeWidth={2}
                            />
                        ))}

                        <Legend />
                    </RadarChart>
                </ResponsiveContainer>
            </div>
        </Card>
    );
};

export default RadarChartCard;
