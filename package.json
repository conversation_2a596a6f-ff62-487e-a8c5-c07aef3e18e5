{"name": "CEU ERP Portals", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite --host --port 3000", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "prettier:check": "prettier --check ./src", "prettier:write": "prettier --write ./src", "watch": "tsc -w"}, "dependencies": {"@atlaskit/pragmatic-drag-and-drop": "^1.5.2", "@faker-js/faker": "^9.8.0", "@reduxjs/toolkit": "^2.2.7", "@tanstack/react-query": "5.51.21", "@tanstack/react-query-devtools": "5.51.21", "@yoopta/action-menu-list": "^4.9.7", "@yoopta/blockquote": "^4.9.7", "@yoopta/divider": "^4.9.7", "@yoopta/editor": "^4.9.7", "@yoopta/headings": "^4.9.7", "@yoopta/image": "^4.9.7", "@yoopta/link": "^4.9.7", "@yoopta/link-tool": "^4.9.7", "@yoopta/lists": "^4.9.7", "@yoopta/marks": "^4.9.7", "@yoopta/paragraph": "^4.9.7", "@yoopta/toolbar": "^4.9.7", "antd": "^5.17.3", "antd-img-crop": "^4.22.0", "antd-phone-input": "^0.3.13", "axios": "^1.7.2", "clsx": "^2.1.1", "dayjs": "^1.11.13", "humps": "^2.0.1", "lucide-react": "^0.474.0", "object-hash": "^3.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-pdf": "10.1.0", "react-phone-hooks": "^0.1.14", "react-redux": "^9.1.2", "react-router-dom": "^6.25.1", "recharts": "^2.15.3", "slate-react": "^0.102.0", "vite-plugin-svgr": "^4.2.0", "zod": "^3.24.2", "zustand": "^5.0.1"}, "devDependencies": {"@tanstack/eslint-plugin-query": "^5.51.15", "@types/humps": "^2.0.6", "@types/node": "^20.14.12", "@types/object-hash": "^3.0.6", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "postcss": "^8.4.38", "prettier": "^3.2.5", "tailwindcss": "^3.4.3", "typescript": "^5.2.2", "vite": "^5.2.0"}, "packageManager": "pnpm@9.15.0+sha512.76e2379760a4328ec4415815bcd6628dee727af3779aaa4c914e3944156c4299921a89f976381ee107d41f12cfa4b66681ca9c718f0668fa0831ed4c6d8ba56c"}