import { z } from "zod";
import { DEFAULT_DATE_FORMAT } from "@lib/dayjs-helpers";
import type { FilterPanelItem } from "@myTypes/filter";

/** Query filter schema for API */
export const blogTagQueryFilterSchema = z
    .object({
        search: z.string(),
        // Only for filters of type "rangepicker"
        createdAtBefore: z.string(),
        createdAtAfter: z.string(),
    })
    .partial();

export const blogTagPanelFilterSchema = z
    .object({
        createdAt: z.array(z.any().optional(), z.any().optional()), // [after, before]
    })
    .partial();

/** Filter schema type for filter panel form */
export type BlogTagQueryFilterSchema = z.infer<typeof blogTagQueryFilterSchema>;
export type BlogTagPanelFilterSchema = z.infer<typeof blogTagPanelFilterSchema>;

export const filters: FilterPanelItem<BlogTagPanelFilterSchema>[] = [
    {
        name: "createdAt",
        label: "Fecha de creación",
        type: "rangepicker",
        datepickerprops: {
            format: DEFAULT_DATE_FORMAT,
            className: "w-full",
        },
    },
];
