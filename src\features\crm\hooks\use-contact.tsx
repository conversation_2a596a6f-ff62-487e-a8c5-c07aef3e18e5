import { getContacts, retrieveContact } from "@/features/crm/services/portals/contact";
import { useQuery } from "@tanstack/react-query";

export type UseContactsQuery = Partial<{
    isStaff: boolean;
    search: string;
}>;

type UseContactsProps = {
    page?: number;
    pageSize?: number;
    query?: UseContactsQuery;
};

export const useContacts = ({ page, pageSize, query }: UseContactsProps) => {
    const { data, isLoading, isError, isFetching } = useQuery({
        queryKey: ["contacts", query],
        queryFn: () =>
            getContacts({
                page,
                pageSize,
                ...query,
            }),
        refetchOnWindowFocus: false,
    });

    const { count: COUNT, results: contacts } = data || {
        count: 0,
        results: [],
    };

    return {
        isLoading: isLoading || isFetching,
        isError,
        contacts,
        COUNT,
    };
};

export const useContact = (contactId: string) => {
    const {
        data: contact,
        isLoading,
        isError,
    } = useQuery({
        queryKey: ["contact", contactId],
        queryFn: () => retrieveContact(contactId),
        enabled: !!contactId,
        refetchOnWindowFocus: false,
    });

    return {
        contact,
        isLoading,
        isError,
    };
};
