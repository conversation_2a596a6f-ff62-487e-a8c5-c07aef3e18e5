import { portalsApi } from "@services/portals";
import type {
    DashboardContactData,
    DashboardContactQueryParams,
} from "@/features/crm/types/dashboard/contact";

export const getContactDashboardData = async (
    query: DashboardContactQueryParams = {},
): Promise<DashboardContactData> => {
    const response = await portalsApi.get("crm/dashboard/contacts", {
        params: {
            ...query,
        },
    });
    return response.data;
};

export const invalidateContactDashboardCache = () => {
    return portalsApi.post("crm/dashboard/contacts/invalidate-cache");
};
