import Header from "@components/shared/molecules/Header";
import { <PERSON> } from "react-router-dom";
import { useAuthStore } from "@store/authStore";
import { modulePermissions, hasModuleAccess } from "@/core/config/permissions";

export default function HomePage() {
    const { user } = useAuthStore((state) => state);

    // Function to check if user has access to a module
    const hasAccess = (moduleGroups: string[]) => {
        return hasModuleAccess(moduleGroups, user?.groups);
    };
    return (
        <>
            <Header />
            <div className="w-full flex justify-center">
                <div className="w-full max-w-screen-lg">
                    <h1 className="text-2xl text-gray-700 font-semibold text-center mt-10">
                        Bienvenido al sistema de gestión de CEU Centro de
                        Especialización
                    </h1>
                    <p className="text-center">
                        Seleccione la aplicación que desea usar
                    </p>
                    <div className="flex gap-4">
                        {hasAccess(modulePermissions.erp) && (
                            <Link to="/erp">
                                <div className="rounded-md border border-blue-full p-6 text-xl font-semibold bg-blue-100">
                                    ERP
                                </div>
                            </Link>
                        )}
                        {hasAccess(modulePermissions.crm) && (
                            <Link to="/crm">
                                <div className="rounded-md border border-blue-full p-6 text-xl font-semibold bg-blue-100">
                                    CRM
                                </div>
                            </Link>
                        )}
                        {hasAccess(modulePermissions.cms) && (
                            <Link to="/cms">
                                <div className="rounded-md border border-blue-full p-6 text-xl font-semibold bg-blue-100">
                                    CMS
                                </div>
                            </Link>
                        )}
                        {hasAccess(modulePermissions.lms) && (
                            <Link to="/lms">
                                <div className="rounded-md border border-blue-full p-6 text-xl font-semibold bg-blue-100">
                                    LMS
                                </div>
                            </Link>
                        )}
                    </div>
                </div>
            </div>
        </>
    );
}
