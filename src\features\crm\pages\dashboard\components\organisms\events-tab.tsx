import { useSearchParams } from "react-router-dom";
import dayjs from "dayjs";
import "dayjs/locale/es";

// Components
import EventFilterCard from "@/features/crm/components/molecules/events/event-filter-card";
import EventsSummarySection from "@/features/crm/components/organisms/events/events-summary-section";
import EventsAnalyticsSection from "@/features/crm/components/organisms/events/events-analytics-section";
import EventsSegmentationSection from "@/features/crm/components/organisms/events/events-segmentation-section";

// Hooks and types
import { createDashboardEventsQueryParams } from "@/features/crm/hooks/use-dashboard-events";
import EventsTopAlliancesSection from "@/features/crm/components/organisms/events/events-top-alliances-section";
import EventsHistoricalSection from "@/features/crm/components/organisms/events/events-historical-section";

dayjs.locale("es");

export default function EventsDashboardTab() {
    const [searchParams] = useSearchParams();

    // Create reusable query parameters
    const queryParams = createDashboardEventsQueryParams(searchParams);

    return (
        <div className="space-y-8">
            {/* Filters */}
            <EventFilterCard />

            {/* Summary Section */}
            <EventsSummarySection queryParams={queryParams} />

            {/* Historical Section */}
            <EventsHistoricalSection queryParams={queryParams} />

            {/* Analytics Section */}
            <EventsAnalyticsSection queryParams={queryParams} />

            {/* Segmentation Section */}
            <EventsSegmentationSection queryParams={queryParams} />

            {/* Top Alliances and Active Events Section */}
            <EventsTopAlliancesSection queryParams={queryParams} />
        </div>
    );
}
