import AdminSideBar from "@components/admin/molecules/AdminSideBar";
import Header from "@components/shared/molecules/Header";

type AdminLayoutProps = {
    children: React.ReactNode;
};

export default function AdminLayout({ children }: AdminLayoutProps) {
    return (
        <div>
            <Header />
            <div className="flex h-[calc(100vh-64px)]">
                <AdminSideBar />
                <div className="bg-blue-low w-full rounded-tl-lg p-2 md:p-6 overflow-auto flex justify-center">
                    {children}
                </div>
            </div>
        </div>
    );
}
