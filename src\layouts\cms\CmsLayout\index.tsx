import CmsSideBar from "@components/cms/molecules/CmsSideBar";
import Header from "@components/shared/molecules/Header";
import { useEffect } from "react";

type CmsLayoutProps = {
    children: React.ReactNode;
};

export default function CmsLayout({ children }: CmsLayoutProps) {
    useEffect(() => {
        document.title = "CMS - Content Managment System";
    }, []);
    return (
        <>
            <Header />
            <div className="flex h-[calc(100vh-64px)] w-full">
                <CmsSideBar />
                <div className="bg-blue-low w-full lg:rounded-tl-lg p-2 lg:p-6 overflow-auto flex justify-center">
                    {children}
                </div>
            </div>
        </>
    );
}
