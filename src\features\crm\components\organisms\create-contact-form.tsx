import { useState } from "react";
import { useMutation } from "@tanstack/react-query";
import PhoneInput from "antd-phone-input";
import { useNavigate } from "react-router-dom";
import { ContactCreateForm, ContactCreateRequest } from "@/features/crm/types/contact";
import { createContact } from "@/features/crm/services/portals/contact";
import { App, Button, Divider, Form, Input, Switch, Typography } from "antd";
import queryClient from "@lib/queryClient";
import { phoneNumberToString } from "@lib/phone-number";
import { AxiosError } from "axios";

const { Text } = Typography;

type CreateContactFormProps = {
    handleCloseModal: () => void;
};

export default function CreateContactForm({
    handleCloseModal,
}: CreateContactFormProps) {
    const navigate = useNavigate();
    const [addForm] = Form.useForm();
    const [completeProfile, setCompleteProfile] = useState<boolean>(false);
    const { message, notification } = App.useApp();

    const { mutate, isPending } = useMutation({
        mutationFn: async (values: ContactCreateRequest) => {
            return createContact(values);
        },
        onSuccess: (data) => {
            message.success("Contacto creado exitosamente");
            if (completeProfile) {
                navigate(`/crm/contacts/${data.uid}`);
            }
            queryClient.invalidateQueries({ queryKey: ["contacts"] });
            handleCloseModal();
        },
        onError: (error: AxiosError) => {
            const errorMessage =
                (error.response?.data as { phone_number?: string })?.phone_number ||
                "Error al crear el contacto";
            notification.error({
                message: "Error al crear el contacto",
                description: errorMessage,
            });
        },
    });

    function handleFormFinish(values: ContactCreateForm) {
        const { phoneNumber: fullPhoneNumber, ...restValues } = values;

        // Parse phone number
        let finalPhoneNumber = "";
        try {
            finalPhoneNumber = phoneNumberToString(fullPhoneNumber);
        } catch (error) {
            console.error("Error parsing phone number:", error);
            message.error("Error al parsear el número de teléfono");
            return;
        }
        const finalData = {
            ...restValues,
            phoneNumber: finalPhoneNumber,
            educationalInstitutionId: values.educationalInstitution,
        };
        mutate(finalData);
    }

    return (
        <Form
            name="instructor"
            layout="vertical"
            form={addForm}
            onFinish={handleFormFinish}
        >
            <div className="grid grid-cols-2 gap-4">
                <Form.Item<ContactCreateForm>
                    name="firstName"
                    label={<span className="font-semibold">Nombres</span>}
                >
                    <Input placeholder="Ej. Gerardo" className="py-1" />
                </Form.Item>
                <Form.Item<ContactCreateForm>
                    name="lastName"
                    label={<span className="font-semibold">Apellidos</span>}
                >
                    <Input placeholder="Ej. Inti Lobato" className="py-1" />
                </Form.Item>
            </div>
            <Form.Item<ContactCreateForm>
                name="phoneNumber"
                label={<span className="font-semibold">WhatsApp</span>}
                rules={[
                    {
                        required: true,
                        message: "Por favor ingrese un número de WhatsApp",
                    },
                ]}
            >
                <PhoneInput
                    preferredCountries={["PE", "BO"]}
                    className="py-1"
                    enableSearch
                />
            </Form.Item>
            <Form.Item<ContactCreateForm>
                name="email"
                label={<span className="font-semibold">Correo Electrónico</span>}
            >
                <Input placeholder="Ej. <EMAIL>" className="py-1" />
            </Form.Item>
            <Divider className="my-4" />
            <div className="flex justify-end py-2 gap-2 items-center">
                <Text type="secondary">Completar perfil</Text>
                <Switch
                    size="small"
                    value={completeProfile}
                    onChange={setCompleteProfile}
                />
            </div>
            <div className="grid grid-cols-2 gap-2 items-end">
                <Button
                    onClick={() => handleCloseModal()}
                    className="h-fit"
                    size="large"
                    disabled={isPending}
                >
                    Cancelar
                </Button>
                <Button
                    type="primary"
                    htmlType="submit"
                    className="h-fit"
                    size="large"
                    block
                    loading={isPending}
                >
                    Guardar
                </Button>
            </div>
        </Form>
    );
}
