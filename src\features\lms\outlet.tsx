import { useAuthStore } from "@store/authStore";
import { Navigate, Outlet } from "react-router-dom";
import { modulePermissions, hasModuleAccess } from "@/core/config/permissions";

export default function LmsOutlet() {
    const { isAuthenticated, user, isHydrated } = useAuthStore((state) => state);

    if (!isAuthenticated && isHydrated) {
        return <Navigate to="/login" />;
    }

    // Validate that user has at least one of the allowed groups for CRM
    if (
        isHydrated &&
        user &&
        !hasModuleAccess(modulePermissions.lms, user.groups ? user.groups : undefined)
    ) {
        return <Navigate to="/" replace />;
    }

    return <Outlet />;
}
