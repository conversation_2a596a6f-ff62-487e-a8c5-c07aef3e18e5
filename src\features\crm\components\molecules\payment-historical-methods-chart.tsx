import { useState } from "react";
import { <PERSON>, <PERSON>kel<PERSON>, <PERSON><PERSON>, Switch } from "antd";
import { CreditCard, TrendingUp } from "lucide-react";
import {
    ResponsiveContainer,
    Composed<PERSON>hart,
    Bar<PERSON>hart,
    Bar,
    Line,
    XAxis,
    YAxis,
    CartesianGrid,
    <PERSON><PERSON><PERSON>,
    <PERSON>,
} from "recharts";

import {
    useDashboardPaymentsHistoricalMethods,
    createDashboardPaymentsHistoricalQueryParams,
} from "../../hooks/use-dashboard-payments";
import type { PeriodFilter } from "../../types/dashboard/payment";
import { useSearchParams } from "react-router-dom";

interface PaymentHistoricalMethodsChartProps {
    className?: string;
}

export default function PaymentHistoricalMethodsChart({
    className,
}: PaymentHistoricalMethodsChartProps) {
    const [searchParams] = useSearchParams();
    const [selectedPeriod, setSelectedPeriod] = useState<PeriodFilter>("weekly"); // Default to weekly
    const [showCumulative, setShowCumulative] = useState(false);

    // Create query parameters for historical methods data
    const historicalQuery = createDashboardPaymentsHistoricalQueryParams(
        searchParams,
        selectedPeriod,
    );

    // Fetch historical methods data
    const { data: historicalData, isLoading } =
        useDashboardPaymentsHistoricalMethods(historicalQuery);

    // Transform data for chart - show top 5 payment methods
    const chartData =
        historicalData?.data?.map((period) => {
            const periodData: Record<string, string | number> = {
                period: period.shortPeriod,
                fullPeriod: period.period,
                startDate: period.startDate,
                endDate: period.endDate,
            };

            // Get top 5 payment methods by total amount and add their data
            const sortedMethods = period.paymentMethods
                .filter(
                    (method) =>
                        (showCumulative
                            ? method.cumulative.total
                            : method.discrete.total) > 0,
                )
                .sort((a, b) =>
                    showCumulative
                        ? b.cumulative.total - a.cumulative.total
                        : b.discrete.total - a.discrete.total,
                )
                .slice(0, 5);

            sortedMethods.forEach((method) => {
                const value = showCumulative
                    ? method.cumulative.total
                    : method.discrete.total;
                periodData[method.paymentMethod.name] = value;
            });

            return periodData;
        }) || [];

    // Get unique payment methods for the chart (top 5 most used)
    const allMethods =
        historicalData?.data?.flatMap((period) => period.paymentMethods) || [];
    const methodTotals = allMethods.reduce(
        (acc, method) => {
            const name = method.paymentMethod.name;
            const value = showCumulative
                ? method.cumulative.total
                : method.discrete.total;
            acc[name] = (acc[name] || 0) + value;
            return acc;
        },
        {} as Record<string, number>,
    );

    const topMethods = Object.entries(methodTotals)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 5)
        .map(([name]) => name);

    const colors = ["#1890ff", "#52c41a", "#fa8c16", "#722ed1", "#eb2f96"];

    const formatCurrency = (value: number) => `S/. ${value.toLocaleString()}`;

    interface TooltipProps {
        active?: boolean;
        payload?: Array<{
            payload: Record<string, string | number>;
            dataKey: string;
            value: number;
            color: string;
        }>;
    }

    const CustomTooltip = ({ active, payload }: TooltipProps) => {
        if (active && payload && payload.length) {
            const data = payload[0].payload;
            return (
                <div
                    style={{
                        backgroundColor: "white",
                        padding: "12px",
                        border: "1px solid #d9d9d9",
                        borderRadius: "8px",
                        boxShadow:
                            "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
                        minWidth: "200px",
                        zIndex: 1000,
                    }}
                >
                    <p
                        style={{
                            fontWeight: 600,
                            color: "#262626",
                            marginBottom: "8px",
                            fontSize: "14px",
                        }}
                    >
                        {data.fullPeriod}
                    </p>
                    <p
                        style={{
                            fontSize: "12px",
                            color: "#595959",
                            marginBottom: "8px",
                        }}
                    >
                        {data.startDate === data.endDate
                            ? `Fecha: ${data.startDate}`
                            : `Período: ${data.startDate} - ${data.endDate}`}
                    </p>

                    <div style={{ marginBottom: "8px" }}>
                        <p
                            style={{
                                fontSize: "12px",
                                fontWeight: 500,
                                marginBottom: "4px",
                                color: showCumulative ? "#fa8c16" : "#1890ff",
                            }}
                        >
                            {showCumulative
                                ? "Acumulativo:"
                                : "Discreto (período actual):"}
                        </p>
                        <div style={{ marginLeft: "8px" }}>
                            {payload.map((entry, index) => (
                                <p
                                    key={index}
                                    style={{ fontSize: "12px", marginBottom: "2px" }}
                                >
                                    <span style={{ color: entry.color }}>
                                        {entry.dataKey}:
                                    </span>
                                    <span
                                        style={{
                                            fontWeight: 500,
                                            color: "#262626",
                                            marginLeft: "4px",
                                        }}
                                    >
                                        {formatCurrency(entry.value)}
                                    </span>
                                </p>
                            ))}
                        </div>
                    </div>
                </div>
            );
        }
        return null;
    };

    return (
        <Card
            title={
                <div className="flex items-center justify-between">
                    <div className="flex items-center">
                        {showCumulative ? (
                            <TrendingUp className="mr-2 h-5 w-5 text-orange-500" />
                        ) : (
                            <CreditCard className="mr-2 h-5 w-5 text-blue-500" />
                        )}
                        <span>Histórico por Métodos de Pago</span>
                    </div>
                    <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-2">
                            <span className="text-sm text-gray-600">Acumulativo:</span>
                            <Switch
                                size="small"
                                checked={showCumulative}
                                onChange={setShowCumulative}
                                checkedChildren="Sí"
                                unCheckedChildren="No"
                            />
                        </div>
                        <Button.Group size="small">
                            <Button
                                type={
                                    selectedPeriod === "daily" ? "primary" : "default"
                                }
                                onClick={() => setSelectedPeriod("daily")}
                            >
                                Diario
                            </Button>
                            <Button
                                type={
                                    selectedPeriod === "weekly" ? "primary" : "default"
                                }
                                onClick={() => setSelectedPeriod("weekly")}
                            >
                                Semanal
                            </Button>
                            <Button
                                type={
                                    selectedPeriod === "monthly" ? "primary" : "default"
                                }
                                onClick={() => setSelectedPeriod("monthly")}
                            >
                                Mensual
                            </Button>
                        </Button.Group>
                    </div>
                </div>
            }
            className={className}
        >
            {isLoading ? (
                <Skeleton active paragraph={{ rows: 8 }} />
            ) : (
                <div>
                    {/* Chart */}
                    <div style={{ width: "100%", height: 400 }}>
                        <ResponsiveContainer>
                            {showCumulative ? (
                                <ComposedChart
                                    data={chartData}
                                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                                >
                                    <CartesianGrid
                                        strokeDasharray="3 3"
                                        stroke="#f0f0f0"
                                    />
                                    <XAxis
                                        dataKey="period"
                                        tick={{ fontSize: 12 }}
                                        stroke="#666"
                                    />
                                    <YAxis
                                        tick={{ fontSize: 12 }}
                                        stroke="#666"
                                        tickFormatter={formatCurrency}
                                    />
                                    <Tooltip content={<CustomTooltip />} />
                                    <Legend />
                                    {topMethods.map((method, index) => (
                                        <Line
                                            key={method}
                                            type="monotone"
                                            dataKey={method}
                                            stroke={colors[index % colors.length]}
                                            strokeWidth={2}
                                            dot={{
                                                fill: colors[index % colors.length],
                                                strokeWidth: 2,
                                                r: 3,
                                            }}
                                            name={method}
                                        />
                                    ))}
                                </ComposedChart>
                            ) : (
                                <BarChart
                                    data={chartData}
                                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                                >
                                    <CartesianGrid
                                        strokeDasharray="3 3"
                                        stroke="#f0f0f0"
                                    />
                                    <XAxis
                                        dataKey="period"
                                        tick={{ fontSize: 12 }}
                                        stroke="#666"
                                    />
                                    <YAxis
                                        tick={{ fontSize: 12 }}
                                        stroke="#666"
                                        tickFormatter={formatCurrency}
                                    />
                                    <Tooltip content={<CustomTooltip />} />
                                    <Legend />
                                    {topMethods.map((method, index) => (
                                        <Bar
                                            key={method}
                                            dataKey={method}
                                            fill={colors[index % colors.length]}
                                            name={method}
                                            radius={[2, 2, 0, 0]}
                                        />
                                    ))}
                                </BarChart>
                            )}
                        </ResponsiveContainer>
                    </div>

                    {chartData.length === 0 && !isLoading && (
                        <div className="text-center py-8 text-gray-500">
                            No hay datos disponibles para el período seleccionado
                        </div>
                    )}
                </div>
            )}
        </Card>
    );
}
