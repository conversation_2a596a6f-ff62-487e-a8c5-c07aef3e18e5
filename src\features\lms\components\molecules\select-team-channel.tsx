import { Avatar, Select, SelectProps } from "antd";
import { useState } from "react";
import { useDebounce } from "@hooks/use-debounce";
import { useTeamChannels } from "../../hooks/use-team-channel";
import type { TeamChannel, UseTeamChannelsQuery } from "../../types/team-channel";

interface SelectTeamChannelProps extends Omit<SelectProps, "options"> {
    value?: string;
    onChange?: (value: string) => void;
    defaultTeamChannel?: TeamChannel;
    isLoading?: boolean;
}

export default function SelectTeamChannel({
    value,
    onChange,
    defaultTeamChannel,
    isLoading,
    ...restProps
}: SelectTeamChannelProps) {
    const [query, setQuery] = useState<UseTeamChannelsQuery | null>(null);
    const debouncedQuery = useDebounce(query, 1000);

    const { teamChannels, isLoading: isLoadingTeamChannels } = useTeamChannels({
        queryParams: {
            page: 1,
            pageSize: 25,
            search: debouncedQuery?.search,
        },
    });

    const buildTeamChannelOptionLabel = (tc: TeamChannel) => {
        return (
            <div className="flex items-center gap-3 py-1">
                <div className="relative flex-shrink-0">
                    <Avatar
                        src={tc.pictureUrl}
                        alt={tc.subject}
                        size={40}
                        className="!rounded-full border-2 border-gray-100"
                        style={{
                            minWidth: "40px",
                            minHeight: "40px",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                        }}
                    >
                        <span className="text-sm font-medium text-gray-600">
                            {tc.subject[0]?.toUpperCase()}
                        </span>
                    </Avatar>
                </div>
                <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between gap-2">
                        <span className="font-medium text-gray-900 truncate">
                            {tc.subject}
                        </span>
                        <div className="flex-shrink-0">
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200">
                                {tc.size} {tc.size === 1 ? "miembro" : "miembros"}
                            </span>
                        </div>
                    </div>
                    {tc.description && (
                        <p className="text-xs text-gray-500 mt-1 truncate">
                            {tc.description}
                        </p>
                    )}
                </div>
            </div>
        );
    };

    // Crear opciones de contactos
    const contactsOptions: SelectProps["options"] = teamChannels?.map((tc) => ({
        value: tc.id,
        label: buildTeamChannelOptionLabel(tc),
        data: {
            info: tc,
        },
    }));

    const options = [...contactsOptions];
    if (value && defaultTeamChannel) {
        const isValueInOptions = contactsOptions.some(
            (option) => option.value === value,
        );
        if (!isValueInOptions) {
            options.unshift({
                value: value,
                label: buildTeamChannelOptionLabel(defaultTeamChannel),
                data: {
                    info: defaultTeamChannel,
                },
            });
        }
    }

    const handleSearch = (value: string) => {
        setQuery({ search: value });
    };

    const handleClear = () => {
        setQuery(null);
    };

    return (
        <>
            <Select
                {...restProps}
                value={value}
                onChange={onChange}
                onSearch={handleSearch}
                onClear={handleClear}
                options={options}
                placeholder="Buscar por nombre de grupo"
                loading={isLoadingTeamChannels || isLoading}
                filterOption={false}
                labelRender={(option) => {
                    if (isLoading) {
                        return "Cargando...";
                    }
                    return option.label;
                }}
                allowClear
                showSearch
            />
        </>
    );
}
