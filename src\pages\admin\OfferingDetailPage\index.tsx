import { Link, useNavigate, useParams } from "react-router-dom";
import { <PERSON><PERSON><PERSON><PERSON>b, Button, Image, Modal, Tag, Typography } from "antd";
import AdminLayout from "@layouts/admin/AdminLayout";

const { Text } = Typography;

import Edit from "@assets/icons/general/edit-stroke.svg?react";
import Trash from "@assets/icons/huge/trash-white.svg?react";
import { useMutation, useQuery } from "@tanstack/react-query";
import { deleteOffering, retrieveOffering } from "@services/portals/cms/offering";
import { useEffect } from "react";
import { AxiosError } from "axios";
import Spinner from "@components/shared/atoms/Spinner";
import WelcomeBar from "@components/shared/molecules/WelcomeBar";
import FormLabel from "@/features/crm/components/atoms/FormLabel";
import {
    OfferingModality,
    OfferingModalityLabel,
    OfferingStage,
    OfferingStageLabel,
    OfferingType,
    OfferingTypeLabel,
    OfferingFormat,
    OfferingFormatLabel,
} from "@myTypes/offering";
import { useApiError } from "@hooks/use-api-error";

export default function OfferingDetailPage() {
    const navigate = useNavigate();
    const { oid } = useParams<{ oid: string }>();

    const [modal, contextHolder] = Modal.useModal();

    const {
        isLoading,
        isError,
        data,
        error: fetchError,
    } = useQuery({
        queryKey: ["offering", oid],
        queryFn: () => retrieveOffering(oid as string),
        enabled: oid !== undefined,
    });

    const { handleError: handleDeleteError } = useApiError({
        title: "Error al eliminar el producto",
        genericMessage: "No se pudo eliminar el producto",
    });

    const deleteMutation = useMutation({
        mutationFn: () => deleteOffering(oid as string),
        onSuccess: () => {
            navigate("/admin/offering", { replace: true });
        },
        onError: (error: AxiosError) => {
            handleDeleteError(error);
        },
    });

    useEffect(() => {
        if (isError) {
            const error = fetchError as AxiosError;
            if (error.response?.status === 404) {
                navigate("/admin/offering");
            }
        }
    }, [isError, fetchError, navigate]);

    const handleOnEdit = () => {
        navigate(`/admin/offering/${oid}/edit`);
    };

    const handleOnDelete = () => {
        modal.confirm({
            title: "¿Estás seguro de eliminar el presente registro?",
            content: "Esta acción no se puede deshacer",
            okText: "Eliminar",
            okButtonProps: { danger: true },
            cancelText: "Cancelar",
            onOk: () => deleteMutation.mutate(),
        });
    };

    return (
        <>
            {contextHolder}
            <AdminLayout>
                <div className="w-full h-full space-y-5 max-w-7xl">
                    <div className="flex justify-between items-center">
                        <WelcomeBar helperText="Gestiona aquí el detalle de la oferta académica" />
                        <div className="flex gap-3">
                            <Button
                                type="primary"
                                size="large"
                                style={{ fontSize: 16 }}
                                icon={<Edit />}
                                disabled={isError}
                                onClick={handleOnEdit}
                            >
                                Editar
                            </Button>
                            <Button
                                type="primary"
                                size="large"
                                style={{ fontSize: 16 }}
                                icon={<Trash />}
                                danger
                                disabled={isError}
                                onClick={handleOnDelete}
                            >
                                Eliminar
                            </Button>
                        </div>
                    </div>
                    {isLoading ? (
                        <Spinner />
                    ) : (
                        <>
                            <div className="p-5 bg-white-full rounded-lg space-y-5">
                                <div className="flex items-center">
                                    <Breadcrumb
                                        separator=">"
                                        items={[
                                            {
                                                title: (
                                                    <Link
                                                        to="/admin/offering"
                                                        className="text-base"
                                                    >
                                                        Productos
                                                    </Link>
                                                ),
                                            },
                                            {
                                                title: (
                                                    <Link
                                                        to={`/admin/offering/${oid}`}
                                                        className="text-base text-blue-medium"
                                                    >
                                                        {data?.name}
                                                    </Link>
                                                ),
                                            },
                                            {
                                                title: (
                                                    <Text className="text-base">
                                                        Detalle
                                                    </Text>
                                                ),
                                            },
                                        ]}
                                    />
                                </div>
                            </div>
                            <div className="p-5 bg-white-full rounded-lg lg:flex lg:justify-between space-y-5">
                                <div className="space-y-2 flex-1">
                                    <p className="text-gray-400 font-semibold text-sm">
                                        INFORMACIÓN GENERAL
                                    </p>
                                    <div className="space-y-4">
                                        <div>
                                            <FormLabel>Nombre del producto</FormLabel>
                                            <p className="text-black-full">
                                                {data?.name}
                                            </p>
                                        </div>
                                        <div>
                                            <FormLabel>Fechas</FormLabel>
                                            <p className="text-black-full">
                                                {data?.startDate} - {data?.endDate}
                                            </p>
                                        </div>
                                        <div>
                                            <FormLabel>Descripción</FormLabel>
                                            <p className="text-black-full">
                                                {data?.description || "Sin descripción"}
                                            </p>
                                        </div>
                                        <div>
                                            <FormLabel>Duración y Horario</FormLabel>
                                            <p className="text-black-full">
                                                {data?.duration} | {data?.frequency} |{" "}
                                                {data?.schedule}
                                            </p>
                                        </div>
                                        <div>
                                            <FormLabel>Horas Académicas</FormLabel>
                                            <p className="text-black-full">
                                                {data?.hours} horas
                                            </p>
                                        </div>
                                        <div>
                                            <FormLabel>
                                                Precio, Descuento y Precio final
                                            </FormLabel>
                                            <p className="text-black-full">
                                                S/ {data?.basePrice} | {data?.discount}%
                                                descuento | S/ {data?.finalPrice}
                                            </p>
                                        </div>
                                        <div className="flex gap-4">
                                            <div>
                                                <FormLabel>Tipo de producto</FormLabel>
                                                <Tag color="blue">
                                                    {
                                                        OfferingTypeLabel[
                                                            data?.type as OfferingType
                                                        ]
                                                    }
                                                </Tag>
                                            </div>
                                            <div>
                                                <FormLabel>Modalidad</FormLabel>
                                                <Tag
                                                    color={
                                                        data?.modality ===
                                                        OfferingModality.IN_PERSON
                                                            ? "green"
                                                            : "blue"
                                                    }
                                                >
                                                    {
                                                        OfferingModalityLabel[
                                                            data?.modality as OfferingModality
                                                        ]
                                                    }
                                                </Tag>
                                            </div>
                                            <div>
                                                <FormLabel>Formato</FormLabel>
                                                <Tag color="purple">
                                                    {
                                                        OfferingFormatLabel[
                                                            data?.format as OfferingFormat
                                                        ]
                                                    }
                                                </Tag>
                                            </div>
                                        </div>
                                        <div>
                                            <FormLabel>Estado</FormLabel>
                                            <Tag
                                                color={
                                                    data?.stage ===
                                                    OfferingStage.PLANNING
                                                        ? "green"
                                                        : "red"
                                                }
                                            >
                                                {
                                                    OfferingStageLabel[
                                                        data?.stage as OfferingStage
                                                    ]
                                                }
                                            </Tag>
                                        </div>
                                    </div>
                                </div>
                                <div className="space-y-2 lg:text-end lg:pl-4 lg:border-l-2 border-gray-100">
                                    <p className="text-gray-400 font-semibold text-sm">
                                        CONTENIDO MULTIMEDIA
                                    </p>
                                    <div>
                                        <FormLabel>Thumbnail</FormLabel>
                                        {data?.thumbnail ? (
                                            <Image
                                                src={data.thumbnail.url}
                                                alt={data.thumbnail.name}
                                            />
                                        ) : (
                                            <p className="text-gray-400">Sin imagen</p>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </>
                    )}
                </div>
            </AdminLayout>
        </>
    );
}
