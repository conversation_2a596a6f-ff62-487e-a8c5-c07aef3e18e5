import { useMutation, useQuery } from "@tanstack/react-query";
import {
    createTemplate,
    deleteTemplate,
    deleteTemplates,
    listTemplate,
    retrieveTemplate,
    testTemplate,
    updateTemplate,
} from "../services/portals/template";
import type { AxiosError } from "axios";
import { useApiError } from "@hooks/use-api-error";
import { App } from "antd";
import queryClient from "@lib/queryClient";
import {
    CreateTemplateFormValues,
    PartialUpdateTemplateBody,
    Template,
    TestTemplateBody,
} from "../types/template";
import { useNavigate } from "react-router-dom";

export type UseTemplatesQuery = Partial<{
    search: string;
}>;

type UseTemplatesProps = {
    page?: number;
    pageSize?: number;
    query?: UseTemplatesQuery;
    enabled?: boolean;
};

export const useTemplates = ({ query, ...rest }: UseTemplatesProps = {}) => {
    const { data, isLoading, isError, isFetching, refetch } = useQuery({
        queryKey: ["templates", { query, ...rest }],
        queryFn: () =>
            listTemplate({
                ...query,
                ...rest,
            }),
        refetchOnWindowFocus: false,
    });

    const { count: COUNT, results: templates } = data || {
        count: 0,
        results: [],
    };

    return {
        refetch,
        isLoading: isLoading || isFetching,
        isError,
        templates,
        COUNT,
    };
};

export const useTemplate = (tid: string) => {
    const { data, isLoading, isError, refetch } = useQuery({
        queryKey: ["template", tid],
        queryFn: () => retrieveTemplate(tid as string),
        enabled: !!tid,
    });

    return {
        isLoading,
        isError,
        template: data,
        refetch,
    };
};

type UseCreateTemplateProps = {
    onCreateTemplateSuccess?: () => void;
    onCreateTemplateError?: (error: AxiosError) => void;
};

export const useCreateTemplate = ({
    onCreateTemplateSuccess,
    onCreateTemplateError,
}: UseCreateTemplateProps = {}) => {
    const { message } = App.useApp();
    const { handleError } = useApiError({
        title: "Error al crear la plantilla",
    });

    const navigate = useNavigate();

    return useMutation<Template, AxiosError, CreateTemplateFormValues>({
        mutationFn: (newTemplate) => createTemplate(newTemplate),
        onSuccess: (result, variables) => {
            message.success({
                content: "Pago creado exitosamente",
                duration: 2,
            });
            queryClient.invalidateQueries({
                queryKey: ["templates"],
            });

            if (variables.completeInfo) {
                navigate(`/crm/templates/${result.tid}`);
            }

            onCreateTemplateSuccess?.();
        },
        onError: (error: AxiosError) => {
            handleError(error);
            onCreateTemplateError?.(error);
        },
    });
};

export const useUpdateTemplate = () => {
    const { message } = App.useApp();

    const { handleError } = useApiError({
        title: "Error al actualizar la plantilla",
    });

    return useMutation<
        void,
        AxiosError,
        { tid: string; data: PartialUpdateTemplateBody }
    >({
        mutationFn: ({ tid, data }) => {
            return updateTemplate(tid as string, data);
        },
        onSuccess: (_, variables) => {
            message.success("Plantilla actualizada correctamente");
            queryClient.invalidateQueries({ queryKey: ["template", variables.tid] });
        },
        onError: (error: AxiosError) => {
            handleError(error);
        },
    });
};

type UseTestTemplateProps = {
    onSuccess?: () => void;
    onError?: (error: AxiosError) => void;
};

export const useTestTemplate = ({ onSuccess, onError }: UseTestTemplateProps = {}) => {
    const { message } = App.useApp();

    const { handleError } = useApiError({
        title: "Error al enviar el mensaje de prueba",
    });

    return useMutation<void, AxiosError, { tid: string; data: TestTemplateBody }>({
        mutationFn: ({ tid, data }) => {
            return testTemplate(tid as string, data);
        },
        onSuccess: () => {
            message.success("Mensaje de prueba enviado correctamente");
            onSuccess?.();
        },
        onError: (error: AxiosError) => {
            handleError(error);
            onError?.(error);
        },
    });
};

// Delete templates hooks

type useDeleteTemplateProps = {
    onSuccess?: () => void;
    onError?: (error: AxiosError) => void;
};

export const useDeleteTemplate = ({
    onSuccess,
    onError,
}: useDeleteTemplateProps = {}) => {
    const { message } = App.useApp();
    const { handleError } = useApiError({
        title: "Error al eliminar la plantilla",
    });

    return useMutation<void, AxiosError, { tid: string }>({
        mutationFn: ({ tid }) => deleteTemplate(tid),
        onSuccess: (_, variables) => {
            queryClient.invalidateQueries({ queryKey: ["templates"] });
            queryClient.invalidateQueries({ queryKey: ["template", variables.tid] });
            message.success({
                content: "Plantilla eliminada correctamente",
                duration: 2,
            });
            onSuccess?.();
        },
        onError: (error: AxiosError) => {
            handleError(error);
            onError?.(error);
        },
    });
};

type UseBulkDeleteTemplatesProps = {
    onSuccess?: () => void;
    onError?: (error: AxiosError) => void;
};

export const useBulkDeleteTemplates = ({
    onSuccess,
    onError,
}: UseBulkDeleteTemplatesProps = {}) => {
    const { message } = App.useApp();
    const { handleError } = useApiError({
        title: "Error al eliminar las plantillas",
    });

    return useMutation<void, AxiosError, { tids: string[] }>({
        mutationFn: ({ tids }) => deleteTemplates(tids),
        onSuccess: () => {
            message.success("Plantillas eliminadas correctamente");
            queryClient.invalidateQueries({ queryKey: ["templates"] });
            onSuccess?.();
        },
        onError: (error: AxiosError) => {
            handleError(error);
            onError?.(error);
        },
    });
};
