import { useEffect } from "react";
import { useParams, useNavigate, Link, useSearchParams } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { Breadcrumb, Tabs, Typography } from "antd";
import LmsLayout from "@/features/lms/layout";
import WelcomeBar from "@components/shared/molecules/WelcomeBar";
import Spinner from "@components/shared/atoms/Spinner";
import { retrieveEnrollment } from "../../../services/portals/enrollment";
import GeneralEnrollmentDetail from "@/features/lms/components/organisms/general-enrollment-detail";
import CertificateManagement from "@/features/lms/components/organisms/certificate-management";

const { Text } = Typography;

type EnrollmentParams = {
    eid: string;
};

export default function EnrollmentDetailPage() {
    const { eid } = useParams<EnrollmentParams>();
    const navigate = useNavigate();
    const [searchParams, setSearchParams] = useSearchParams();

    const {
        data: enrollment,
        isLoading,
        isError,
    } = useQuery({
        queryKey: ["enrollments", eid],
        queryFn: async () => retrieveEnrollment(eid as string),
        enabled: !!eid,
        refetchOnWindowFocus: false,
        retry: false,
    });

    useEffect(() => {
        if (isError) {
            navigate("/lms/enrollments");
        }
    }, [isError, navigate]);

    const activeTab = searchParams.get("tab") || "general";

    const tabItems = [
        {
            key: "general",
            label: "General",
            children: enrollment && <GeneralEnrollmentDetail enrollment={enrollment} />,
        },
        {
            key: "certificate",
            label: "Certificado",
            children: enrollment && <CertificateManagement enrollment={enrollment} />,
        },
    ];

    return (
        <LmsLayout>
            <div className="max-w-7xl w-full h-full space-y-5">
                <div className="flex justify-between items-center">
                    <WelcomeBar helperText="Ver y editar detalles de la matrícula" />
                </div>

                {isLoading ? (
                    <Spinner />
                ) : (
                    <>
                        <div className="p-5 bg-white-full rounded-lg space-y-5 shadow-sm">
                            <div className="flex items-center">
                                <Breadcrumb
                                    separator=">"
                                    items={[
                                        {
                                            title: (
                                                <Link
                                                    to="/lms/enrollments"
                                                    className="text-base"
                                                >
                                                    Matrículas
                                                </Link>
                                            ),
                                        },
                                        {
                                            title: (
                                                <Link
                                                    to={`/lms/enrollments/${enrollment?.eid}`}
                                                    className="text-base"
                                                >
                                                    {enrollment?.user?.fullName ||
                                                        (enrollment?.user
                                                            ? `${enrollment.user.firstName} ${enrollment.user.lastName}`
                                                            : null) ||
                                                        enrollment?.eid?.slice(-6) ||
                                                        "Matrícula"}
                                                </Link>
                                            ),
                                        },
                                        {
                                            title: (
                                                <Text className="text-base">
                                                    Ver y Editar
                                                </Text>
                                            ),
                                        },
                                    ]}
                                />
                            </div>
                        </div>
                        <Tabs
                            items={tabItems}
                            activeKey={activeTab}
                            onChange={(key) => setSearchParams({ tab: key })}
                        />
                    </>
                )}
            </div>
        </LmsLayout>
    );
}
