import { Tag, Typography } from "antd";
import { <PERSON> } from "react-router-dom";
import { OrderSummary } from "../../types/activity";

const { Text } = Typography;

interface ActivityOrderCellProps {
    order: OrderSummary | null;
}

const ActivityOrderCell = ({ order }: ActivityOrderCellProps) => {
    if (!order) {
        return <Text type="secondary">Sin orden</Text>;
    }

    const getStageColor = (stage: string) => {
        switch (stage.toLowerCase()) {
            case "lead":
                return "blue";
            case "proposal":
                return "orange";
            case "negotiation":
                return "gold";
            case "closed_won":
                return "green";
            case "closed_lost":
                return "red";
            default:
                return "default";
        }
    };

    return (
        <div className="flex flex-col gap-1">
            {order.owner}
            <Link
                to={`/crm/orders/${order.oid}`}
                className="font-semibold text-blue-full hover:text-blue-600"
            >
                #{order.oid.slice(-6)}
            </Link>
            <Tag color={getStageColor(order.stage)} className="w-fit">
                {order.stage.replace("_", " ").toUpperCase()}
            </Tag>
        </div>
    );
};

export default ActivityOrderCell;
