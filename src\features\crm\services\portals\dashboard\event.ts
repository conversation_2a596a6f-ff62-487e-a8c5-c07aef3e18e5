import { portalsApi } from "@services/portals";
import type {
    DashboardEventQueryParams,
    DashboardEventSummaryData,
    DashboardEventAnalyticsData,
    DashboardEventSegmentationData,
    DashboardEventsLaunchedData,
    DashboardEventsHistoricalData,
} from "@/features/crm/types/dashboard/events";

// Summary endpoint - Most important, loads first
export const getEventDashboardSummary = async (
    query: DashboardEventQueryParams = {},
): Promise<DashboardEventSummaryData> => {
    const response = await portalsApi.get("crm/dashboard/events/summary", {
        params: {
            ...query,
        },
    });
    return response.data;
};

// Analytics endpoint
export const getEventDashboardAnalytics = async (
    query: DashboardEventQueryParams = {},
): Promise<DashboardEventAnalyticsData> => {
    const response = await portalsApi.get("crm/dashboard/events/analytics", {
        params: {
            ...query,
        },
    });
    return response.data;
};

// Segmentation endpoint
export const getEventDashboardSegmentation = async (
    query: DashboardEventQueryParams = {},
): Promise<DashboardEventSegmentationData> => {
    const response = await portalsApi.get("crm/dashboard/events/segmentation", {
        params: {
            ...query,
        },
    });
    return response.data;
};

// lauched Events endpoint
export const getEventDashboardLaunched = async (
    query: DashboardEventQueryParams = {},
): Promise<DashboardEventsLaunchedData> => {
    const response = await portalsApi.get("crm/dashboard/events/launched", {
        params: {
            ...query,
        },
    });
    return response.data;
};

// historical data
export const getEventDashboardHistorical = async (
    query: DashboardEventQueryParams = {},
): Promise<DashboardEventsHistoricalData> => {
    const response = await portalsApi.get("crm/dashboard/events/historical", {
        params: {
            ...query,
        },
    });
    return response.data;
};

export const invalidateEventDashboardCache = () => {
    return portalsApi.post("crm/dashboard/events/invalidate-cache");
};
