import { Link, useParams, useSearchParams } from "react-router-dom";
import { Breadcrumb, Typography, Tabs } from "antd";
import type { TabsProps } from "antd";

const { Text } = Typography;

import Spinner from "@components/shared/atoms/Spinner";
import LmsLayout from "@/features/lms/layout";
import WelcomeBar from "@components/shared/molecules/WelcomeBar";
import { useProgram } from "@/features/lms/hooks/use-program";

import CurriculumProgramEdit from "@/features/lms/components/organisms/curriculum-program-edit";
import TeachersProgramEdit from "@/features/lms/components/organisms/teachers-program-edit";
import IntegrationsProgramEdit from "@/features/lms/components/organisms/program-integrations-edit";
import GeneralProgramDetail from "@/features/lms/components/organisms/general-program-detail";

export default function ProgramDetailPage() {
    const { oid } = useParams<{ oid: string }>();
    const [searchParams, setSearchParams] = useSearchParams();

    const { program, isLoading, refetch } = useProgram(oid);

    const handleTabChange = (key: string) => {
        setSearchParams({ tab: key });
    };

    const tabItems: TabsProps["items"] = [
        {
            key: "general",
            label: "General",
            children: program && oid && (
                <GeneralProgramDetail oid={oid} data={program} onRefetch={refetch} />
            ),
        },
        {
            key: "curriculum",
            label: "Currícula",
            children: program && oid && (
                <CurriculumProgramEdit oid={oid} data={program} onRefetch={refetch} />
            ),
        },
        {
            key: "teachers",
            label: "Docentes",
            children: program && oid && <TeachersProgramEdit />,
        },
        {
            key: "integrations",
            label: "Integraciones",
            children: program && oid && <IntegrationsProgramEdit data={program} />,
        },
    ];

    const defaultTab = searchParams.get("tab") || "general";

    return (
        <>
            <LmsLayout>
                <div className="max-w-7xl w-full h-full space-y-5">
                    <div className="flex justify-between items-center">
                        <WelcomeBar helperText="Edita aquí los detalles de los programas del sistema de aprendizaje." />
                    </div>

                    {isLoading ? (
                        <Spinner />
                    ) : (
                        <>
                            <div className="p-5 bg-white-full rounded-lg space-y-5 shadow-sm">
                                <div className="flex items-center">
                                    <Breadcrumb
                                        separator=">"
                                        items={[
                                            {
                                                title: (
                                                    <Link
                                                        to="/lms/programs"
                                                        className="text-base"
                                                    >
                                                        Programas
                                                    </Link>
                                                ),
                                            },
                                            {
                                                title: (
                                                    <Link
                                                        to={`/lms/programs/${oid}`}
                                                        className="text-base"
                                                    >
                                                        {program?.name}
                                                    </Link>
                                                ),
                                            },
                                            {
                                                title: (
                                                    <Text className="text-base">
                                                        Detalle
                                                    </Text>
                                                ),
                                            },
                                        ]}
                                    />
                                </div>
                            </div>
                            <div className="space-y-5 pb-8">
                                <Tabs
                                    items={tabItems}
                                    onChange={handleTabChange}
                                    activeKey={defaultTab}
                                />
                            </div>
                        </>
                    )}
                </div>
            </LmsLayout>
        </>
    );
}
