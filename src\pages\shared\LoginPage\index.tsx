import { useEffect, useRef } from "react";
import { Button, Form, Input, notification, Typography } from "antd";
import { Link, useNavigate } from "react-router-dom";

const { Text } = Typography;

import AuthLayout from "@layouts/shared/AuthLayout";
import CeuWhite from "@assets/logos/ceu-white.svg?react";
import { AxiosError } from "axios";
import { openErrorNotification } from "@lib/notification";
import { useMutation } from "@tanstack/react-query";
import { login } from "@services/portals/shared/auth";
import { useAuthStore } from "@store/authStore";

type FieldType = {
    username: string;
    password: string;
};

export default function LoginPage() {
    const { login: authLogin, isAuthenticated } = useAuthStore((state) => state);
    const navigate = useNavigate();
    const shouldRedirect = useRef(false);

    const [api, contextHolder] = notification.useNotification();

    useEffect(() => {
        if (isAuthenticated && shouldRedirect.current) {
            navigate("/");
            shouldRedirect.current = false;
        }
    }, [isAuthenticated, navigate]);

    const mutation = useMutation({
        mutationFn: (values: FieldType) => login(values),
        onSuccess: (data) => {
            const payload = {
                key: data.key,
                user: {
                    ...data.user,
                },
                isAuthenticated: true,
            };
            shouldRedirect.current = true;
            authLogin(payload);
        },
        onError: (error: AxiosError) => {
            if (error.code === "ERR_BAD_REQUEST") {
                openErrorNotification(
                    "Credenciales incorrectas",
                    "Invalid Credentials",
                    api,
                );
            } else if (error.code === "ERR_NETWORK") {
                openErrorNotification("Error de red", error.message, api);
            }
        },
    });
    const handleSubmit = (values: FieldType) => {
        mutation.mutate(values);
    };

    return (
        <AuthLayout>
            {contextHolder}
            <div className="grid grid-cols-1 md:grid-cols-2 items-center">
                <div className="flex justify-center md:block">
                    <CeuWhite />
                    <p className="hidden text-3xl md:block md:text-5xl md:max-w-lg text-black-full">
                        En{" "}
                        <span className="text-blue-full font-bold">
                            CEU Centro de Especialización
                        </span>{" "}
                        nos interesa tu futuro
                    </p>
                </div>
                <div className="space-y-5">
                    <h2 className="text-center text-5xl font-semibold text-black-full">
                        Ingresar
                    </h2>
                    <Form
                        name="login"
                        layout="vertical"
                        className="space-y-8"
                        onFinish={handleSubmit}
                    >
                        <Form.Item<FieldType>
                            name="username"
                            label={
                                <span className="font-medium text-base">Usuario</span>
                            }
                            rules={[
                                {
                                    required: true,
                                    message: "Por favor. Ingrese un usuario!",
                                },
                            ]}
                        >
                            <Input placeholder="Ej. Juan" className="py-3" />
                        </Form.Item>
                        <Form.Item<FieldType>
                            name="password"
                            label={
                                <span className="font-medium text-base">
                                    Contraseña
                                </span>
                            }
                            rules={[
                                {
                                    required: true,
                                    message: "Por favor. Ingrese la contraseña!",
                                },
                            ]}
                        >
                            <Input.Password
                                className="py-3"
                                autoComplete="ceu-password"
                            />
                        </Form.Item>
                        <div className="w-full flex justify-end">
                            <Link to="/forgot-password">
                                <Text className="text-blue-full font-semibold">
                                    Olvidaste tu contraseña
                                </Text>
                            </Link>
                        </div>
                        <Form.Item>
                            <Button
                                type="primary"
                                block
                                size="large"
                                className="h-fit"
                                htmlType="submit"
                            >
                                <span className="py-1">Ingresar</span>
                            </Button>
                        </Form.Item>
                    </Form>
                    <div className="w-full flex justify-center">
                        <p className="max-w-96 text-center">
                            Si tiene problemas para ingresar puedes{" "}
                            <Link to="/contact-support">
                                <span className="text-blue-full font-semibold underline">
                                    contactar con Soporte
                                </span>
                            </Link>
                        </p>
                    </div>
                </div>
            </div>
        </AuthLayout>
    );
}
