import CreatePaymentForm from "@/features/crm/components/organisms/create-payment-form";
import PaymentsTable from "@/features/crm/components/organisms/payments-table";
import PaymentsFilters from "@/features/crm/components/organisms/payments-filters";
import { usePayments } from "@/features/crm/hooks/use-payment";
import CrmLayout from "@/features/crm/layout";
import {
    DEFAULT_PAGE,
    DEFAULT_PAGE_SIZE,
    ListPaymentsQueryParams,
} from "@/features/crm/types/payment";
import WelcomeBar from "@components/shared/molecules/WelcomeBar";
import { Badge, Button, Input, Modal, Pagination, Typography } from "antd";
import { Plus, SlidersHorizontal } from "lucide-react";
import { useState } from "react";
import { useSearchParams } from "react-router-dom";

const { Text } = Typography;
const { Search } = Input;

const STATUS_FILTER_TAGS = [
    {
        value: undefined,
        label: "Todos",
    },
    {
        value: "isPaid",
        label: "Pagados",
    },
    {
        value: "isPending",
        label: "Pendientes",
    },
    {
        value: "isLost",
        label: "Perdidos",
    },
];

export default function PaymentsListPage() {
    const [searchParams, setSearchParams] = useSearchParams();
    const status = searchParams.get("status") || undefined;
    const page = Number(searchParams.get("page")) || DEFAULT_PAGE;
    const pageSize = Number(searchParams.get("pageSize")) || DEFAULT_PAGE_SIZE;
    const search = searchParams.get("search") || "";
    const currency = searchParams.get("currency") || undefined;
    const startDate = searchParams.get("startDate") || undefined;
    const endDate = searchParams.get("endDate") || undefined;
    const filterDateBy = searchParams.get("filterDateBy") || undefined;
    const createdBy = searchParams.get("createdBy") || undefined;
    const paymentMethod = searchParams.get("paymentMethod") || undefined;
    const isFirstPayment = searchParams.get("isFirstPayment")
        ? searchParams.get("isFirstPayment") === "true"
        : undefined;
    const minAmount = searchParams.get("minAmount")
        ? Number(searchParams.get("minAmount"))
        : undefined;
    const maxAmount = searchParams.get("maxAmount")
        ? Number(searchParams.get("maxAmount"))
        : undefined;

    const queryParams: ListPaymentsQueryParams = {
        page,
        pageSize,
        search,
        status,
        currency,
        startDate,
        endDate,
        filterDateBy,
        createdBy,
        paymentMethod,
        isFirstPayment,
        minAmount,
        maxAmount,
    };
    const { payments, count } = usePayments({ queryParams });

    const [isFilterDrawerOpen, setIsFilterDrawerOpen] = useState<boolean>(false);
    const [isCreatePaymentModalOpen, setIsCreatePaymentModalOpen] =
        useState<boolean>(false);

    const handleSetSearchQuery = (value: string) => {
        setSearchParams((prev) => {
            prev.set("search", value);
            return prev;
        });
    };

    const handleCreatePaymentModalOpen = () => {
        setIsCreatePaymentModalOpen(true);
    };
    const handleCreatePaymentModalClose = () => {
        setIsCreatePaymentModalOpen(false);
    };

    const setPaymentStatus = (key: string | undefined) => {
        if (!key) {
            setSearchParams((prev) => {
                prev.delete("status");
                return prev;
            });
            return;
        }
        setSearchParams((prev) => {
            prev.set("status", key);
            return prev;
        });
    };

    const handleSetPage = (page: number, pageSize: number) => {
        setSearchParams((prev) => {
            prev.set("page", String(page));
            prev.set("pageSize", String(pageSize));
            return prev;
        });
    };

    return (
        <CrmLayout>
            <div className="w-full h-full space-y-5">
                <div className="flex justify-between items-center">
                    <WelcomeBar helperText="Gestiona aquí los pagos y deudas de CEU" />
                    <div className="flex gap-3">
                        <Button
                            type="primary"
                            size="large"
                            style={{ fontSize: 16 }}
                            icon={<Plus />}
                            onClick={() => {
                                handleCreatePaymentModalOpen();
                            }}
                        >
                            Agregar
                        </Button>
                        <Modal
                            centered
                            open={isCreatePaymentModalOpen}
                            onCancel={() => {
                                handleCreatePaymentModalClose();
                            }}
                            footer={false}
                            title={"Agregar nuevo pago"}
                        >
                            <CreatePaymentForm
                                onFinish={handleCreatePaymentModalClose}
                            />
                        </Modal>
                    </div>
                </div>
                <div className="p-5 bg-white-full rounded-lg space-y-5">
                    <div className="flex flex-col lg:flex-row justify-between items-center">
                        <Text className="text-black-medium text-2xl font-semibold">
                            Pagos <Badge count={count} color="blue" size="default" />
                        </Text>
                        <Search
                            size="large"
                            placeholder="Buscar por nombre, email o teléfono"
                            onSearch={(value) => {
                                handleSetSearchQuery(value);
                            }}
                            enterButton
                            allowClear
                            className="max-w-screen-sm"
                        />
                        <div className="flex items-center gap-3">
                            <Button
                                icon={<SlidersHorizontal size={16} />}
                                onClick={() => setIsFilterDrawerOpen(true)}
                            >
                                Filtros
                            </Button>
                            <PaymentsFilters
                                isOpen={isFilterDrawerOpen}
                                onClose={() => setIsFilterDrawerOpen(false)}
                            />
                        </div>
                    </div>
                </div>

                <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg shadow-sm">
                    {STATUS_FILTER_TAGS.map((tag) => (
                        <button
                            key={tag.value || "all"}
                            onClick={() => setPaymentStatus(tag.value)}
                            className={`px-4 py-2 text-sm font-medium rounded-full transition-all duration-200 ${
                                status === tag.value
                                    ? "bg-blue-500 text-white-full shadow-md"
                                    : "bg-white text-gray-600 hover:bg-gray-100"
                            }`}
                        >
                            {tag.label}
                        </button>
                    ))}
                </div>

                <PaymentsTable initialData={payments} />
                <div className="flex justify-between items-center p-4 bg-white-full rounded-lg shadow-sm">
                    <Text type="secondary">
                        {payments.length} de {count} órdenes
                    </Text>
                    <Pagination
                        current={page}
                        pageSize={pageSize}
                        total={count}
                        onChange={handleSetPage}
                        showSizeChanger
                    />
                </div>
            </div>
        </CrmLayout>
    );
}
