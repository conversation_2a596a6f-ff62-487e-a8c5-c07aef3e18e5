import { YooptaContentValue } from '@yoopta/editor';

/**
 * Extracts headings from Yoopta content
 */
export const extractHeadings = (content: YooptaContentValue) => {
  if (!content) return [];

  interface YooptaNode {
    id: string;
    type: string;
    meta: {
      order: number;
    };
    value: Array<{
      id: string;
      children: Array<{
        text: string;
      }>;
    }>;
  }

  return Object.values(content as Record<string, YooptaNode>)
    .filter(node =>
      node.type === 'HeadingOne' ||
      node.type === 'HeadingTwo' ||
      node.type === 'HeadingThree'
    )
    .sort((a, b) => a.meta.order - b.meta.order)
    .map(node => ({
      id: node.value[0].id,
      text: node.value[0].children[0]?.text || '',
      level: node.type === 'HeadingOne' ? 1 : node.type === 'HeadingTwo' ? 2 : 3
    }));
};

/**
 * Generates a slug from text
 */
export const generateSlug = (text: string): string => {
  return text
    .toLowerCase()
    .replace(/[^\w\s]/g, '')
    .replace(/\s+/g, '-');
};

/**
 * Estimates reading time based on content
 */
export const estimateReadingTime = (content: YooptaContentValue): number => {
  if (!content) return 0;

  interface YooptaNode {
    id: string;
    type: string;
    value: Array<{
      children: Array<{
        text?: string;
        children?: Array<{
          text?: string;
        }>;
      }>;
    }>;
  }

  // Extract all text from content
  const text = Object.values(content as Record<string, YooptaNode>)
    .map(node => {
      if (node.value && node.value[0] && node.value[0].children) {
        return node.value[0].children
          .map(child => {
            if (child.text) {
              return child.text;
            }
            if (child.children) {
              return child.children.map(c => c.text || '').join(' ');
            }
            return '';
          })
          .join(' ');
      }
      return '';
    })
    .join(' ');

  // Average reading speed: 200 words per minute
  const wordCount = text.split(/\s+/).length;
  const readingTime = Math.ceil(wordCount / 200);

  return Math.max(1, readingTime);
};
