import React, { useMemo } from "react";
import {
    Drawer,
    Form,
    Input,
    Select,
    InputNumber,
    Switch,
    Space,
    Divider,
    Typography,
    Avatar,
    Button,
    Tag,
    Tooltip,
    message,
} from "antd";
import { PlusCircle, User2, HelpCircle } from "lucide-react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { updateBlogPost } from "@services/portals/cms/blogs/post";
import { getInstructors } from "@services/portals/cms/instructor";
import { BlogPost, UpdateBlogPostBody } from "@myTypes/blog";
import { onSuccessMessage } from "@lib/message";
import { listBlogTags } from "@services/portals/cms/blogs/tag";
import { listBlogCategories } from "@services/portals/cms/blogs/category";
import { AxiosError } from "axios";
import { useApiError } from "@hooks/use-api-error";

const { Text, Paragraph } = Typography;
const { TextArea } = Input;
const { Option } = Select;

interface BlogEditorSettingsDrawerProps {
    visible: boolean;
    onClose: () => void;
    blog: BlogPost;
}

const BlogEditorSettingsDrawer: React.FC<BlogEditorSettingsDrawerProps> = ({
    visible,
    onClose,
    blog,
}) => {
    const [form] = Form.useForm<UpdateBlogPostBody>();
    const featuredValue = Form.useWatch("featured", form);
    const [messageApi, contextHolder] = message.useMessage();
    const queryClient = useQueryClient();

    // Fetch all tags
    const { data: tags } = useQuery({
        queryKey: ["blogTags"],
        queryFn: () =>
            listBlogTags({
                pageSize: 10,
            }),
        select: (data) => data.results,
    });

    // Fetch all categories
    const { data: categories } = useQuery({
        queryKey: ["blogCategories"],
        queryFn: () =>
            listBlogCategories({
                pageSize: 10,
            }),
        select: (data) => data.results,
    });

    // Fetch all instructors (authors)
    const { data: instructorsResponse } = useQuery({
        queryKey: ["instructors"],
        queryFn: () => getInstructors({ pageSize: 100 }),
    });

    const authors = instructorsResponse?.results || [];

    // Mutation to update blog post
    const { handleError: handleUpdateError } = useApiError({
        title: "Error al actualizar el blog",
        genericMessage: "No se pudo actualizar el blog",
    });

    const { mutate: updateBlog, isPending } = useMutation({
        mutationFn: (data: UpdateBlogPostBody) => updateBlogPost(blog.bid, data),
        onSuccess: () => {
            onSuccessMessage("Blog actualizado correctamente", messageApi);
            queryClient.invalidateQueries({ queryKey: ["blog", blog.bid] });
        },
        onError: (error: AxiosError) => {
            handleUpdateError(error);
        },
    });

    useMemo(() => {
        if (blog) {
            form.setFieldsValue({
                ...blog,
                authors: blog.authors?.map((author) => author.iid) || [],
                categories: blog.categories?.map((category) => category.bcid),
                tags: blog.tags?.map((tag) => tag.btid),
            });
        }
    }, [blog, form]);

    // Handle form submission
    const handleSubmit = () => {
        form.validateFields().then((values) => {
            const updateData: UpdateBlogPostBody = {
                summary: values.summary,
                slug: values.slug,
                authors: values.authors,
                categories: values.categories,
                tags: values.tags,
                readingTime: values.readingTime,
                featured: values.featured,
                featuredOrder: values.featuredOrder,
                metaTitle: values.metaTitle,
                metaDescription: values.metaDescription,
                metaKeywords: values.metaKeywords,
            };
            updateBlog(updateData);
        });
    };

    return (
        <Drawer
            title={
                <header className="flex items-center justify-between">
                    <Text strong className="text-base">
                        Configuración del blog
                    </Text>
                </header>
            }
            placement="right"
            width={500}
            mask={false}
            onClose={onClose}
            open={visible}
            className="blog-settings-drawer"
            styles={{
                body: { padding: "4px 16px" },
            }}
            footer={
                <footer className="text-left">
                    <Button onClick={onClose} className="mr-2">
                        Cancelar
                    </Button>
                    <Button type="primary" onClick={handleSubmit} loading={isPending}>
                        Guardar
                    </Button>
                </footer>
            }
        >
            {contextHolder}
            <Form
                form={form}
                layout="vertical"
                className="bg-white p-4 rounded-lg shadow-sm"
                initialValues={{
                    ...blog,
                    authors: blog.authors?.map((author) => author.iid) || [],
                    categories: blog.categories?.map((category) => category.bcid) || [],
                    tags: blog.tags?.map((tag) => tag.btid) || [],
                }}
            >
                <Form.Item<UpdateBlogPostBody>
                    label={
                        <Space>
                            <Text>Resumen</Text>
                            <Tooltip title="Breve descripción del blog">
                                <HelpCircle size={14} className="text-gray-400" />
                            </Tooltip>
                        </Space>
                    }
                    name="summary"
                    rules={[
                        {
                            required: true,
                            message: "El resumen es obligatorio",
                        },
                    ]}
                >
                    <TextArea size="large" className="text-xs p-2" />
                </Form.Item>

                <div className="mb-5">
                    <Text className="text-blue-full uppercase tracking-wider font-medium">
                        Información básica
                    </Text>
                </div>

                {/* Selección de autor */}
                <Form.Item<UpdateBlogPostBody>
                    name="authors"
                    label={
                        <Space>
                            <Text>Autor</Text>
                            <Tooltip title="Selecciona a los autores de este artículo">
                                <HelpCircle size={14} className="text-gray-400" />
                            </Tooltip>
                        </Space>
                    }
                    rules={[
                        {
                            required: true,
                            message: "Por favor selecciona al menos un autor",
                        },
                    ]}
                >
                    <div className="flex items-center gap-2">
                        <Select
                            mode="multiple"
                            placeholder="Selecciona los autores"
                            className="flex-1"
                            defaultValue={blog.authors?.map((author) => author.iid)}
                            onChange={(authors) => form.setFieldsValue({ authors })}
                            dropdownRender={(menu) => (
                                <>
                                    {menu}
                                    <Divider className="my-2" />
                                    <Space className="px-2 pb-1">
                                        <Button
                                            type="text"
                                            icon={<PlusCircle size={14} />}
                                            size="small"
                                        >
                                            Añadir nuevo autor
                                        </Button>
                                    </Space>
                                </>
                            )}
                        >
                            {authors.map((author) => (
                                <Option key={author.iid} value={author.iid}>
                                    <Space>
                                        <Avatar
                                            size="small"
                                            icon={<User2 size={14} />}
                                            src={author.profilePhoto?.url}
                                        />
                                        <Text>{author.fullName}</Text>
                                    </Space>
                                </Option>
                            ))}
                        </Select>
                    </div>
                </Form.Item>

                {/* Campo de slug */}
                <Form.Item<UpdateBlogPostBody>
                    name="slug"
                    label={
                        <Space>
                            <Text>Slug</Text>
                            <Tooltip title="URL amigable para el blog">
                                <HelpCircle size={14} className="text-gray-400" />
                            </Tooltip>
                        </Space>
                    }
                    rules={[{ required: true, message: "Por favor ingresa el slug" }]}
                >
                    <Input
                        prefix="/"
                        placeholder="mi-blog-de-ejemplo"
                        className="rounded-md"
                    />
                </Form.Item>

                {/* Selección de categorías */}
                <Form.Item<UpdateBlogPostBody>
                    name="categories"
                    label={
                        <Space>
                            <Text>Categorías</Text>
                            <Tooltip title="Categoriza tu blog con categorías relevantes">
                                <HelpCircle size={14} className="text-gray-400" />
                            </Tooltip>
                        </Space>
                    }
                    rules={[
                        {
                            required: true,
                            message: "Por favor selecciona al menos una categoría",
                        },
                    ]}
                >
                    <Select
                        mode="multiple"
                        maxCount={1}
                        placeholder="Selecciona las categorías"
                        className="w-full"
                        tagRender={(props) => (
                            <Tag
                                closable={props.closable}
                                onClose={props.onClose}
                                className="bg-blue-50 text-blue-500 rounded m-0.5 border-0"
                            >
                                {props.label}
                            </Tag>
                        )}
                    >
                        {categories?.map((category) => (
                            <Option key={category.bcid} value={category.bcid}>
                                {category.name}
                            </Option>
                        ))}
                    </Select>
                </Form.Item>

                {/* Selección de etiquetas */}
                <Form.Item<UpdateBlogPostBody>
                    name="tags"
                    label={
                        <Space>
                            <Text>Etiquetas</Text>
                            <Tooltip title="Categoriza tu blog con etiquetas relevantes">
                                <HelpCircle size={14} className="text-gray-400" />
                            </Tooltip>
                        </Space>
                    }
                    rules={[
                        {
                            required: true,
                            message: "Por favor selecciona al menos una etiqueta",
                        },
                    ]}
                >
                    <Select
                        mode="multiple"
                        placeholder="Selecciona las etiquetas"
                        className="w-full"
                        tagRender={(props) => (
                            <Tag
                                closable={props.closable}
                                onClose={props.onClose}
                                className="bg-blue-50 text-blue-500 rounded m-0.5 border-0"
                            >
                                {props.label}
                            </Tag>
                        )}
                    >
                        {tags?.map((tag) => (
                            <Option key={tag.btid} value={tag.btid}>
                                {tag.name}
                            </Option>
                        ))}
                    </Select>
                </Form.Item>

                {/* Tiempo de lectura */}
                <Form.Item<UpdateBlogPostBody>
                    name="readingTime"
                    label={
                        <Space>
                            <Text>Tiempo de lectura {"(minutos)"}</Text>
                            <Tooltip title="Tiempo estimado para leer el artículo completo">
                                <HelpCircle size={14} className="text-gray-400" />
                            </Tooltip>
                        </Space>
                    }
                    rules={[
                        {
                            required: true,
                            message: "Por favor ingresa el tiempo de lectura",
                        },
                    ]}
                >
                    <InputNumber min={1} max={60} className="w-full" />
                </Form.Item>

                <Divider className="my-6" />

                <div className="mb-5">
                    <Text className="text-blue-full uppercase tracking-wider font-medium">
                        Visualización
                    </Text>
                </div>

                {/* Blog destacado */}
                <Form.Item<UpdateBlogPostBody>
                    name="featured"
                    label={
                        <Space>
                            <Text>Blog destacado</Text>
                            <Tooltip title="Mostrar este blog en la sección de destacados">
                                <HelpCircle size={14} className="text-gray-400" />
                            </Tooltip>
                        </Space>
                    }
                    valuePropName="checked"
                >
                    <Switch />
                </Form.Item>

                {/* Orden de destacado */}
                <Form.Item<UpdateBlogPostBody>
                    name="featuredOrder"
                    label="Orden de destacado"
                    dependencies={["featured"]}
                    rules={[
                        ({ getFieldValue }) => ({
                            validator(_, value) {
                                if (!getFieldValue("featured") || value) {
                                    return Promise.resolve();
                                }
                                return Promise.reject(
                                    new Error(
                                        "Por favor ingresa el orden de destacado",
                                    ),
                                );
                            },
                        }),
                    ]}
                >
                    <InputNumber
                        min={1}
                        max={10}
                        className="w-full"
                        disabled={featuredValue == false}
                    />
                </Form.Item>

                <Divider className="my-6" />

                <div className="mb-5">
                    <Text className="text-blue-full uppercase tracking-wider font-medium">
                        SEO
                    </Text>
                    <Paragraph type="secondary" className="mt-1">
                        Optimiza tu blog para motores de búsqueda
                    </Paragraph>
                </div>

                {/* Meta título */}
                <Form.Item<UpdateBlogPostBody>
                    name="metaTitle"
                    label={
                        <Space>
                            <Text>Meta título</Text>
                            <Tooltip title="Título que aparecerá en los resultados de búsqueda">
                                <HelpCircle size={14} className="text-gray-400" />
                            </Tooltip>
                        </Space>
                    }
                >
                    <Input placeholder="Título para SEO" />
                </Form.Item>

                {/* Meta descripción */}
                <Form.Item<UpdateBlogPostBody>
                    name="metaDescription"
                    label={
                        <Space>
                            <Text>Meta descripción</Text>
                            <Tooltip title="Breve descripción que aparecerá en los resultados de búsqueda">
                                <HelpCircle size={14} className="text-gray-400" />
                            </Tooltip>
                        </Space>
                    }
                >
                    <TextArea
                        rows={3}
                        placeholder="Descripción para SEO"
                        className="rounded-md"
                    />
                </Form.Item>

                {/* Meta palabras clave */}
                <Form.Item<UpdateBlogPostBody>
                    name="metaKeywords"
                    label={
                        <Space>
                            <Text>Palabras clave</Text>
                            <Tooltip title="Palabras clave separadas por comas">
                                <HelpCircle size={14} className="text-gray-400" />
                            </Tooltip>
                        </Space>
                    }
                >
                    <Input placeholder="Palabras clave separadas por comas" />
                </Form.Item>
            </Form>
        </Drawer>
    );
};

export default BlogEditorSettingsDrawer;
