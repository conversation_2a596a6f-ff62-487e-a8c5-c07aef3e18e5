import { PaginatedResponse } from "@myTypes/base";
import { portalsApi } from "@services/portals";
import { LeadOriginCreate, ListLeadOrigin } from "../../types/lead-origins";

export type ListLeadOriginsParams = {
    page?: number;
    pageSize?: number;
    search?: string;
};

export const getLeadOrigins = async (
    params: ListLeadOriginsParams = {},
): Promise<PaginatedResponse<ListLeadOrigin>> => {
    const response = await portalsApi.get("crm/lead-origins", {
        params,
    });
    return response.data;
};

export const createLeadOrigin = async (leadOrigin: LeadOriginCreate) => {
    const response = await portalsApi.post("crm/lead-origins", leadOrigin);
    return response.data;
};
