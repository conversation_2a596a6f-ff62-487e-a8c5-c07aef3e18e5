import { useQuery } from "@tanstack/react-query";
import { getOfferings } from "../services/portals/offering";

export const useOfferings = () => {
    const { data, isLoading, isError } = useQuery({
        queryKey: ["offerings"],
        queryFn: () => getOfferings(),
    });

    const { count, results: offerings } = data || {
        count: 0,
        results: [],
    };

    return {
        isLoading,
        isError,
        offerings,
        count,
    };
};
