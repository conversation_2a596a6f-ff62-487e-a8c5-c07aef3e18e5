import { List, Button, message, Typography, Space, Tooltip } from "antd";
import { ExternalLink, Copy } from "lucide-react";
import { PartnershipEnrollmentUrl } from "@/features/crm/types/event-schedule";

const { Text, Link } = Typography;

interface PartnershipEnrollmentUrlsListProps {
    /**
     * Array of partnership enrollment URLs to display
     */
    partnershipEnrollmentUrls: PartnershipEnrollmentUrl[];
    /**
     * Custom class name for styling
     */
    className?: string;
}

/**
 * Component that displays a read-only list of partnership enrollment URLs
 * with actions to open externally and copy to clipboard
 */
export default function PartnershipEnrollmentUrlsList({
    partnershipEnrollmentUrls,
    className,
}: PartnershipEnrollmentUrlsListProps) {
    /**
     * Copies the enrollment URL to clipboard and shows success message
     */
    const handleCopyToClipboard = async (url: string, partnershipName: string) => {
        try {
            await navigator.clipboard.writeText(url);
            message.success(`URL de ${partnershipName} copiada al portapapeles`);
        } catch (error) {
            message.error("Error al copiar la URL");
        }
    };

    /**
     * Opens the enrollment URL in a new tab
     */
    const handleOpenExternal = (url: string) => {
        window.open(url, "_blank", "noopener,noreferrer");
    };

    /**
     * Truncates long URLs for better display
     */
    const truncateUrl = (url: string, maxLength: number = 50) => {
        if (url.length <= maxLength) return url;
        return `${url.substring(0, maxLength)}...`;
    };

    // Don't render if no URLs available
    if (!partnershipEnrollmentUrls || partnershipEnrollmentUrls.length === 0) {
        return null;
    }

    return (
        <div className={className}>
            <Text strong className="text-gray-400 text-sm mb-2 block">
                URLS DE INSCRIPCIÓN
            </Text>
            <List
                size="small"
                bordered
                dataSource={partnershipEnrollmentUrls}
                renderItem={(item) => (
                    <List.Item
                        key={item.pid}
                        actions={[
                            <Tooltip title="Abrir en nueva pestaña" key="external">
                                <Button
                                    type="text"
                                    size="small"
                                    icon={<ExternalLink size={14} />}
                                    onClick={() =>
                                        handleOpenExternal(item.enrollmentUrl)
                                    }
                                />
                            </Tooltip>,
                            <Tooltip title="Copiar al portapapeles" key="copy">
                                <Button
                                    type="text"
                                    size="small"
                                    icon={<Copy size={14} />}
                                    onClick={() =>
                                        handleCopyToClipboard(
                                            item.enrollmentUrl,
                                            item.name,
                                        )
                                    }
                                />
                            </Tooltip>,
                        ]}
                    >
                        <List.Item.Meta
                            title={
                                <Space direction="vertical" size={0}>
                                    <Text strong>{item.name}</Text>
                                    <Text type="secondary" className="text-xs">
                                        {item.institution}
                                    </Text>
                                </Space>
                            }
                            description={
                                <Link
                                    href={item.enrollmentUrl}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-xs"
                                >
                                    {truncateUrl(item.enrollmentUrl)}
                                </Link>
                            }
                        />
                    </List.Item>
                )}
            />
        </div>
    );
}
