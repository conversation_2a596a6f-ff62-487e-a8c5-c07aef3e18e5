import { useMutation, UseMutationOptions, useQuery } from "@tanstack/react-query";
import type { DashboardContactQueryParams } from "../types/dashboard/contact";
import {
    getContactDashboardData,
    invalidateContactDashboardCache,
} from "../services/portals/dashboard/contact";

export const useDashboardContacts = (query: DashboardContactQueryParams = {}) => {
    const { data, isLoading, isError, isFetching } = useQuery({
        queryKey: ["dashboard-contacts", query],
        queryFn: () => getContactDashboardData(query ?? {}),
    });

    return {
        isLoading: isLoading || isFetching,
        isError,
        data,
    };
};

export const useDashboardContactsInvalidateCache = (options: UseMutationOptions) => {
    const mutation = useMutation({
        mutationFn: () => invalidateContactDashboardCache(),
        ...options,
    });

    return mutation;
};
