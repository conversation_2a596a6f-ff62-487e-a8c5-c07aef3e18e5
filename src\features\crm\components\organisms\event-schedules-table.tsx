import { Config<PERSON>rovider, Table, Tooltip, Typography } from "antd";
import {
    EventSchedule,
    EventSchedulePartnership,
} from "@/features/crm/types/event-schedule";
import { Link } from "react-router-dom";
import EventScheduleDatesCell from "@/features/crm/components/atoms/event-schedule-dates-cell";
import EventStatusCell from "@/features/crm/components/atoms/event-status-cell";
import { EventStage } from "@/features/crm/types/event";
import EventSchedulePartnershipsCell from "@/features/crm/components/atoms/event-schedule-partnerships-cell";
import EventDateCell from "@/features/crm/components/atoms/event-date-cell";
import { Hash } from "lucide-react";

const { Text } = Typography;

export default function EventSchedulesTable({
    eventSchedules,
}: {
    eventSchedules: EventSchedule[];
}) {
    const columns = [
        {
            title: "ID",
            dataIndex: "esid",
            key: "esid",
            render: (esid: string) => (
                <Tooltip title="Ver detalles del evento">
                    <Link
                        to={`/crm/event-schedules/${esid}`}
                        className="font-semibold text-blue-full flex items-center gap-1"
                    >
                        <Hash size={14} strokeWidth={2.5} className="text-gray-500" />
                        {esid.slice(-6)}
                    </Link>
                </Tooltip>
            ),
        },
        {
            title: "NOMBRE",
            dataIndex: "name",
            key: "name",
        },
        {
            title: "HORARIO",
            dataIndex: ["startDate", "endDate"],
            key: "schedule",
            render: (_: string, record: EventSchedule) => (
                <EventScheduleDatesCell
                    startDate={record.startDate}
                    endDate={record.endDate}
                />
            ),
        },
        {
            title: "ALIANZAS",
            dataIndex: "partnerships",
            key: "partnerships",
            render: (partnerships: EventSchedulePartnership[] = []) => (
                <EventSchedulePartnershipsCell partnerships={partnerships} />
            ),
        },
        {
            title: "ESTADO",
            dataIndex: "stage",
            key: "stage",
            render: (stage: EventStage, record: EventSchedule) => (
                <EventStatusCell stage={stage} updatedAt={record.updatedAt} />
            ),
        },
        {
            title: "FECHAS",
            dataIndex: "createdAt",
            key: "dates",
            render: (_: string, record: EventSchedule) => (
                <div className="space-y-2">
                    <EventDateCell date={record.createdAt} label="Creación" />
                    <EventDateCell date={record.updatedAt} label="Actualización" />
                </div>
            ),
        },
    ];
    return (
        <ConfigProvider
            theme={{
                components: {
                    Table: {
                        headerBg: "#FBFCFD",
                        borderColor: "#fff",
                        headerSplitColor: "#fafafa",
                        headerBorderRadius: 8,
                        rowHoverBg: "#F6FAFD",
                        rowSelectedBg: "#F6FAFD",
                        rowSelectedHoverBg: "#F6FAFD",
                        footerBg: "#F1F1F1",
                    },
                },
            }}
        >
            <Table
                className="rounded-lg shadow-sm"
                footer={() => ""}
                pagination={false}
                columns={columns}
                dataSource={eventSchedules}
                locale={{
                    emptyText: (
                        <div className="text-center py-4">
                            <Text type="secondary">
                                No hay Programaciones para mostrar
                            </Text>
                        </div>
                    ),
                }}
            />
        </ConfigProvider>
    );
}
