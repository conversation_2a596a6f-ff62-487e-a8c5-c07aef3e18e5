import React from "react";
import { Card, Empty } from "antd";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Cell, <PERSON><PERSON><PERSON>, Legend } from "recharts";
import { Users } from "lucide-react";
import type { ContactSegmentation } from "@/features/crm/types/dashboard/events";

interface CustomLabelProps {
    cx: number;
    cy: number;
    midAngle: number;
    innerRadius: number;
    outerRadius: number;
    percent: number;
}

interface CustomTooltipProps {
    active?: boolean;
    payload?: Array<{
        color: string;
        name: string;
        value: number;
        payload: {
            percentage: number;
        };
    }>;
    label?: string;
}

interface ContactsSegmentationChartProps {
    data?: ContactSegmentation;
    isLoading?: boolean;
}

const COLORS = ["#1890ff", "#52c41a"];

const ContactsSegmentationChart: React.FC<ContactsSegmentationChartProps> = ({
    data,
    isLoading = false,
}) => {
    const chartData =
        data && (data.newContacts.total || data.hasContact.total)
            ? [
                  {
                      name: "Nuevos Contactos",
                      value: data.newContacts.total || 0,
                      percentage: data.newContacts.percentage || 0,
                  },
                  {
                      name: "Contactos Existentes",
                      value: data.hasContact.total || 0,
                      percentage: data.hasContact.percentage || 0,
                  },
              ]
            : [];

    const renderCustomizedLabel = ({
        cx,
        cy,
        midAngle,
        innerRadius,
        outerRadius,
        percent,
    }: CustomLabelProps) => {
        const RADIAN = Math.PI / 180;
        const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
        const x = cx + radius * Math.cos(-midAngle * RADIAN);
        const y = cy + radius * Math.sin(-midAngle * RADIAN);

        return (
            <text
                x={x}
                y={y}
                fill="white"
                textAnchor="middle"
                dominantBaseline="central"
            >
                {`${(percent * 100).toFixed(0)}%`}
            </text>
        );
    };

    const CustomTooltip = ({ active, payload }: CustomTooltipProps) => {
        if (active && payload && payload.length) {
            const data = payload[0];
            return (
                <div className="bg-white-full p-3 border border-gray-200 shadow-md rounded">
                    <p className="font-medium">{data.name}</p>
                    <p style={{ color: data.color }}>Cantidad: {data.value}</p>
                    <p style={{ color: data.color }}>
                        Porcentaje: {data.payload.percentage}%
                    </p>
                </div>
            );
        }
        return null;
    };

    return (
        <Card
            title={
                <div className="flex items-center gap-2">
                    <Users size={20} className="text-blue-500" />
                    <span>Segmentación de Contactos</span>
                </div>
            }
            className="shadow-md h-full"
        >
            <div className="h-64">
                {chartData.length && !isLoading ? (
                    <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                            <Pie
                                data={chartData}
                                cx="50%"
                                cy="50%"
                                labelLine={false}
                                label={renderCustomizedLabel}
                                outerRadius={80}
                                fill="#8884d8"
                                dataKey="value"
                            >
                                {chartData.map((_, index) => (
                                    <Cell
                                        key={`cell-${index}`}
                                        fill={COLORS[index % COLORS.length]}
                                    />
                                ))}
                            </Pie>
                            <Tooltip content={<CustomTooltip />} />
                            <Legend />
                        </PieChart>
                    </ResponsiveContainer>
                ) : (
                    <div className="h-full flex items-center justify-center">
                        <Empty description="No hay datos de contactos disponibles" />
                    </div>
                )}
            </div>
        </Card>
    );
};

export default ContactsSegmentationChart;
