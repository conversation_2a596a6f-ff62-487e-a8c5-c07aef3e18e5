import { useMemo, useState } from "react";
import { Link, useNavigate, useSearchParams } from "react-router-dom";
import {
    Button,
    Checkbox,
    ConfigProvider,
    Dropdown,
    Empty,
    Form,
    message,
    Modal,
    Pagination,
    Popover,
    Table,
    Typography,
} from "antd";
import { useMutation, useQuery } from "@tanstack/react-query";
import type { TableProps } from "antd";

const { Text } = Typography;

import WelcomeBar from "@components/shared/molecules/WelcomeBar";

import Trash from "@assets/icons/huge/trash-white.svg?react";
import Plus from "@assets/icons/huge/plus-white.svg?react";
import DeleteStroke from "@assets/icons/huge/delete-stroke.svg?react";
import EditStroke from "@assets/icons/huge/edit-stroke.svg?react";
import MoreVertical from "@assets/icons/huge/more-vertical.svg?react";
import Settings from "@assets/icons/huge/settings.svg?react";
import Reload from "@assets/icons/huge/reload.svg?react";

import CmsLayout from "@layouts/cms/CmsLayout";
import Spinner from "@components/shared/atoms/Spinner";
import {
    createBlogCategory,
    deleteBlogCategory,
    listBlogCategories,
} from "@services/portals/cms/blogs/category";
import { formatDateTime } from "@lib/helpers";
import { BlogCategory, CreateBlogCategoryBody } from "@myTypes/blog";
import { onErrorMessage, onSuccessMessage } from "@lib/message";
import BlogCategoryForm from "@components/cms/molecules/BlogCategoryForm";
import SearchText from "@components/shared/atoms/SearchText";
import { DeleteConfirm } from "@components/shared/atoms/DeleteConfirm";
import type { AxiosError } from "axios";

const INITIAL_COLUMNS: TableProps<BlogCategory>["columns"] = [
    {
        title: "NOMBRE",
        dataIndex: "name",
        key: "name",
        render: (name, record) => (
            <Link
                to={`${record.bcid}/edit`}
                className="text-blue-full font-semibold underline"
            >
                {name}
            </Link>
        ),
    },
    {
        title: "SLUG",
        dataIndex: "slug",
        key: "slug",
        render: (text: string) => <Text>{text}</Text>,
    },
    {
        title: "DESCRIPCIÓN",
        dataIndex: "description",
        key: "description",
        render: (text: string) => <Text>{text || "-"}</Text>,
    },
    {
        title: "FECHA DE CREACIÓN",
        dataIndex: "createdAt",
        key: "createdAt",
        render: (date: string) => <Text>{formatDateTime(date)}</Text>,
    },
];

const PAGE_SIZE = 10;

const INITIAL_CHECKED_VALUES = ["name", "slug", "description", "parent", "createdAt"];

const COLUMN_OPTIONS = [
    { label: "Nombre", value: "name" },
    { label: "Slug", value: "slug" },
    { label: "Descripción", value: "description" },
    { label: "Categoría padre", value: "parent" },
    { label: "Fecha de creación", value: "createdAt" },
];

export default function BlogCategoryListPage() {
    const [messageApi, messageContextHolder] = message.useMessage();
    const navigate = useNavigate();
    const [searchParams, setSearchParams] = useSearchParams();
    const DEFAULT_PAGE = searchParams.get("page")
        ? Number(searchParams.get("page"))
        : 1;
    const [modalOpen, setModalOpen] = useState(false);
    const [currentPage, setCurrentPage] = useState(DEFAULT_PAGE);
    const [checkedValues, setCheckedValues] =
        useState<string[]>(INITIAL_CHECKED_VALUES);
    const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

    const [addForm] = Form.useForm<CreateBlogCategoryBody>();

    const { isLoading, data, refetch } = useQuery({
        queryKey: ["blogCategories", currentPage, searchParams.toString()],
        queryFn: async () => {
            const search = searchParams.get("search") || undefined;
            const parentId = searchParams.get("parentId") || undefined;

            return listBlogCategories({
                page: currentPage,
                pageSize: PAGE_SIZE,
                search,
                parentId,
            });
        },
    });

    const tableColumns = useMemo(() => {
        return (INITIAL_COLUMNS ?? []).filter((column) => {
            return checkedValues.includes(column.key as string);
        });
    }, [checkedValues]);

    const defaultColumn = {
        title: "ACCIONES",
        key: "actions",
        render: (record: BlogCategory) => (
            <Dropdown
                trigger={["click"]}
                menu={{
                    items: [
                        {
                            key: "edit",
                            label: (
                                <>
                                    <div className="flex items-center gap-2 text-blue-full">
                                        <EditStroke className="w-5 h-5" /> Editar
                                    </div>
                                </>
                            ),
                        },
                        {
                            key: "delete",
                            label: (
                                <>
                                    <>
                                        <DeleteConfirm
                                            title="Eliminar categoría"
                                            description="¿Estás seguro de eliminar el categoría?"
                                            onConfirm={() =>
                                                deleteMutation.mutateAsync(record.bcid)
                                            }
                                            customTrigger={
                                                <div className="flex items-center gap-2 text-state-red-full">
                                                    <DeleteStroke className="w-5 h-5" />{" "}
                                                    Eliminar
                                                </div>
                                            }
                                        />
                                    </>
                                </>
                            ),
                        },
                    ],
                    onClick: ({ key }) => {
                        handleRowAction(key, record);
                    },
                }}
                placement="bottomRight"
            >
                <Button
                    icon={<MoreVertical className="w-5 h-5" />}
                    type="text"
                    size="small"
                />
            </Dropdown>
        ),
    };

    const handleRowAction = (key: string, record: BlogCategory) => {
        if (key === "edit") {
            navigate(`/cms/blog/categories/${record.bcid}/edit`);
        }
    };

    const handleShowHideColumns = (checkedValues: string[]) => {
        setCheckedValues(checkedValues);
    };

    const handlePageChange = (newPage: number) => {
        setSearchParams({ page: newPage.toString() });
        setCurrentPage(newPage);
    };

    const handleReloadData = () => {
        refetch();
    };

    const handleFormFinish = (
        values: CreateBlogCategoryBody | Partial<CreateBlogCategoryBody>,
    ) => {
        addMutation.mutate(values as CreateBlogCategoryBody);
    };

    const handleOnSelectChange = (selectedRowKeys: React.Key[]) => {
        setSelectedRowKeys(selectedRowKeys);
    };

    const hasSelected = selectedRowKeys.length > 0;

    const addMutation = useMutation({
        mutationFn: createBlogCategory,
        onSuccess: () => {
            onSuccessMessage("Categoría creada correctamente", messageApi);
            setModalOpen(false);
            addForm.resetFields();
            refetch();
        },
        onError: () => {
            onErrorMessage("Error al crear la categoría", messageApi);
        },
    });

    const deleteMutation = useMutation({
        mutationFn: deleteBlogCategory,
        onSuccess: () => {
            onSuccessMessage("Categoría eliminada correctamente", messageApi);
            refetch();
        },
        onError: (error: AxiosError) => {
            onErrorMessage(`Error al eliminar: ${error.response?.data}`, messageApi);
        },
    });

    return (
        <>
            {messageContextHolder}
            <CmsLayout>
                <div className="w-full h-full space-y-5">
                    <div className="flex justify-between items-center">
                        <WelcomeBar helperText="Gestiona aquí las categorías de los blogs" />
                        <div className="flex gap-3">
                            <Button
                                type="primary"
                                size="large"
                                style={{ fontSize: 16 }}
                                icon={<Plus />}
                                onClick={() => {
                                    setModalOpen(true);
                                }}
                            >
                                Agregar
                            </Button>
                            <Modal
                                centered
                                open={modalOpen}
                                onCancel={() => setModalOpen(false)}
                                footer={false}
                                title={
                                    <div className="w-full flex justify-center text-2xl py-4">
                                        Agregar nueva categoría
                                    </div>
                                }
                            >
                                <BlogCategoryForm
                                    form={addForm}
                                    onFinish={handleFormFinish}
                                />
                                <div className="grid grid-cols-2 gap-2 items-end mt-4">
                                    <Button
                                        onClick={() => setModalOpen(false)}
                                        className="h-fit"
                                        size="large"
                                    >
                                        Cancelar
                                    </Button>
                                    <Button
                                        type="primary"
                                        htmlType="submit"
                                        className="h-fit"
                                        size="large"
                                        block
                                        onClick={() => addForm.submit()}
                                        loading={addMutation.isPending}
                                    >
                                        Guardar
                                    </Button>
                                </div>
                            </Modal>
                        </div>
                    </div>

                    <div className="bg-white-full p-5 rounded-lg shadow-sm">
                        <div className="flex justify-between items-center mb-5">
                            <Text className="text-black-medium text-2xl font-semibold">
                                Categorías
                            </Text>

                            <div className="flex items-center gap-3">
                                <SearchText />

                                <Button
                                    icon={<Reload />}
                                    size="large"
                                    type="text"
                                    onClick={handleReloadData}
                                />
                                <Popover
                                    content={
                                        <div className="p-2 space-y-3">
                                            <div className="uppercase text-black-medium font-medium">
                                                Mostrar/Ocultar Columnas
                                            </div>
                                            <div className="px-2">
                                                <Checkbox.Group
                                                    defaultValue={
                                                        INITIAL_CHECKED_VALUES
                                                    }
                                                    onChange={handleShowHideColumns}
                                                    name="columns"
                                                    className="flex flex-col gap-1"
                                                    options={COLUMN_OPTIONS}
                                                />
                                            </div>
                                        </div>
                                    }
                                    trigger={["click"]}
                                    placement="bottomRight"
                                >
                                    <Button
                                        icon={<Settings />}
                                        size="large"
                                        type="text"
                                    />
                                </Popover>
                            </div>
                        </div>
                        <ConfigProvider
                            theme={{
                                components: {
                                    Table: {
                                        headerBg: "#FBFCFD",
                                        borderColor: "#fff",
                                        headerSplitColor: "#fafafa",
                                        headerBorderRadius: 8,
                                        rowHoverBg: "#F6FAFD",
                                        rowSelectedBg: "#F6FAFD",
                                        rowSelectedHoverBg: "#F6FAFD",
                                        footerBg: "#F1F1F1",
                                    },
                                },
                            }}
                        >
                            <Table
                                rowSelection={{
                                    type: "checkbox",
                                    onChange: handleOnSelectChange,
                                    selectedRowKeys,
                                }}
                                columns={
                                    tableColumns ? [...tableColumns, defaultColumn] : []
                                }
                                locale={{
                                    emptyText: (
                                        <>{isLoading ? <Spinner /> : <Empty />}</>
                                    ),
                                }}
                                dataSource={data?.results}
                                rowKey="bcid"
                                className="rounded-lg"
                                footer={() => ""}
                                pagination={false}
                            />
                            <div className="flex justify-between">
                                <div className="flex items-center gap-3">
                                    <Button
                                        danger
                                        disabled={!hasSelected}
                                        type="primary"
                                        size="large"
                                        icon={<Trash />}
                                    >
                                        Eliminar
                                    </Button>
                                </div>
                                <div>
                                    <Pagination
                                        defaultCurrent={DEFAULT_PAGE}
                                        total={data?.count || 0}
                                        pageSize={PAGE_SIZE}
                                        onChange={handlePageChange}
                                    />
                                </div>
                            </div>
                        </ConfigProvider>
                    </div>
                </div>
            </CmsLayout>
        </>
    );
}
