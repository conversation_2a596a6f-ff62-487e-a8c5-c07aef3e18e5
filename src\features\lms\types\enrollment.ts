import { PaginatedResponse } from "@myTypes/base";

export type AuditBase = {
    createdAt: string;
    updatedAt: string;
    createdBy?: string;
    updatedBy?: string;
    deleted?: boolean;
    deletedAt?: string;
    deletedBy?: string;
};

export type EnrollmentUser = {
    uid: string;
    firstName: string;
    lastName: string;
    fullName?: string;
    email?: string;
    phoneNumber: string;
};

export type OrderItem = {
    id: number;
    order: string;
};

export type EnrollmentOffering = {
    oid: string;
    name: string;
    longName?: string;
    codeName?: string;
};

export type CertificateFile = {
    fid: string;
    name: string;
    url: string;
};

export type Certificate = {
    cid: string;
    file: CertificateFile;
    verificationUrl: string;
    issuedAt: string;
};

export type EnrollmentListItem = {
    eid: string;
    key: string;
    user: EnrollmentUser;
    certificateIssued: boolean;
    certificate?: Certificate;
    isActive: boolean;
    orderItem: OrderItem;
    offering: EnrollmentOffering;
} & AuditBase;

export type EnrollmentRetrieve = {
    eid: string;
    orderItem: OrderItem;
    user: EnrollmentUser;
    certificateIssued: boolean;
    certificateSent: boolean;
    certificate?: Certificate;
    isActive: boolean;
    offering: EnrollmentOffering;
} & AuditBase;

export type EnrollmentQueryParams = {
    page?: number;
    pageSize?: number;
    search?: string;
    certificateIssued?: boolean;
    isActive?: boolean;
    user?: string;
    offering?: string;
};

export type EnrollmentCreate = {
    orderItem: string;
    user: string;
    isActive?: boolean;
};

export type EnrollmentUpdate = {
    certificateIssued?: boolean;
    certificateSent?: boolean;
    isActive?: boolean;
};

export type EnrollmentsResponse = PaginatedResponse<EnrollmentListItem>;

export const DEFAULT_PAGE = 1;
export const DEFAULT_PAGE_SIZE = 10;
