import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Drawer, Select } from "antd";
import { ContactOcupationLabel } from "@/features/crm/types/contact";
import { useSearchParams } from "react-router-dom";
import { useCallback, useEffect, useState } from "react";
import dayjs, { Dayjs } from "dayjs";
import "dayjs/locale/es";
import SelectMajor from "../molecules/select-major";
import SelectTerm from "../molecules/select-term";
import SelectEducationalInstitution from "../molecules/select-educational-institution";

dayjs.locale("es");

interface ContactsFiltersProps {
    isOpen: boolean;
    onClose: () => void;
    onClearFilters?: () => void;
}

export default function ContactsFilters({
    isOpen,
    onClose,
    onClearFilters,
}: ContactsFiltersProps) {
    const [searchParams, setSearchParams] = useSearchParams();

    // Local filter states
    const [selectedOcupations, setSelectedOcupations] = useState<string[]>([]);
    const [selectedMajors, setSelectedMajors] = useState<string[]>([]);
    const [selectedTerms, setSelectedTerms] = useState<string[]>([]);
    const [selectedEducationalInstitutions, setSelectedEducationalInstitutions] =
        useState<string[]>([]);
    const [dateRange, setDateRange] = useState<[Dayjs, Dayjs] | null>(null);

    // Initialize filters from URL
    useEffect(() => {
        const ocupations = searchParams.get("ocupation");
        const majors = searchParams.get("major");
        const terms = searchParams.get("term");
        const educationalInstitutions = searchParams.get("educationalInstitution");
        const createdAtAfter = searchParams.get("createdAtAfter");
        const createdAtBefore = searchParams.get("createdAtBefore");

        if (ocupations) {
            setSelectedOcupations(ocupations.split(","));
        } else {
            setSelectedOcupations([]);
        }

        if (majors) {
            setSelectedMajors(majors.split(","));
        } else {
            setSelectedMajors([]);
        }

        if (terms) {
            setSelectedTerms(terms.split(","));
        } else {
            setSelectedTerms([]);
        }

        if (educationalInstitutions) {
            setSelectedEducationalInstitutions(educationalInstitutions.split(","));
        } else {
            setSelectedEducationalInstitutions([]);
        }

        if (createdAtAfter && createdAtBefore) {
            setDateRange([dayjs(createdAtAfter), dayjs(createdAtBefore)]);
        } else {
            setDateRange(null);
        }
    }, [searchParams]);

    const handleApplyFilters = useCallback(() => {
        setSearchParams((prev) => {
            // Clear previous filters
            prev.delete("ocupation");
            prev.delete("major");
            prev.delete("term");
            prev.delete("educationalInstitution");
            prev.delete("createdAtAfter");
            prev.delete("createdAtBefore");

            // Apply ocupation filters
            if (selectedOcupations.length > 0) {
                prev.set("ocupation", selectedOcupations.join(","));
            }

            // Apply major filters
            if (selectedMajors.length > 0) {
                prev.set("major", selectedMajors.join(","));
            }

            // Apply term filters
            if (selectedTerms.length > 0) {
                prev.set("term", selectedTerms.join(","));
            }

            // Apply educational institution filters
            if (selectedEducationalInstitutions.length > 0) {
                prev.set(
                    "educationalInstitution",
                    selectedEducationalInstitutions.join(","),
                );
            }

            // Apply date range filters
            if (dateRange && dateRange[0] && dateRange[1]) {
                prev.set("createdAtAfter", dateRange[0].format("YYYY-MM-DD"));
                prev.set("createdAtBefore", dateRange[1].format("YYYY-MM-DD"));
            }

            // Reset to page 1
            prev.set("page", "1");

            return prev;
        });
        onClose();
    }, [
        selectedOcupations,
        selectedMajors,
        selectedTerms,
        selectedEducationalInstitutions,
        dateRange,
        setSearchParams,
        onClose,
    ]);

    const handleClearFilters = useCallback(() => {
        setSelectedOcupations([]);
        setSelectedMajors([]);
        setSelectedTerms([]);
        setSelectedEducationalInstitutions([]);
        setDateRange(null);

        setSearchParams((prev) => {
            prev.delete("ocupation");
            prev.delete("major");
            prev.delete("term");
            prev.delete("educationalInstitution");
            prev.delete("createdAtAfter");
            prev.delete("createdAtBefore");
            prev.set("page", "1");
            return prev;
        });

        onClearFilters?.();
        onClose();
    }, [setSearchParams, onClearFilters, onClose]);

    return (
        <Drawer
            title="Aplicar filtros"
            placement="right"
            closable={true}
            onClose={onClose}
            open={isOpen}
            width={480}
        >
            <div className="space-y-4">
                <div>
                    <h4 className="font-medium mb-2">Ocupación</h4>
                    <Select
                        mode="multiple"
                        style={{ width: "100%" }}
                        placeholder="Seleccionar ocupaciones"
                        value={selectedOcupations}
                        onChange={setSelectedOcupations}
                        allowClear
                        showSearch={false}
                    >
                        {Object.entries(ContactOcupationLabel).map(([value, label]) => (
                            <Select.Option key={value} value={value}>
                                {label}
                            </Select.Option>
                        ))}
                    </Select>
                </div>

                <div>
                    <h4 className="font-medium mb-2">Carrera profesional</h4>
                    <SelectMajor
                        mode="multiple"
                        className="w-full"
                        placeholder="Seleccionar carreras"
                        value={selectedMajors}
                        onChange={(value) => {
                            if (Array.isArray(value)) {
                                setSelectedMajors(value);
                            } else if (value) {
                                setSelectedMajors([value]);
                            } else {
                                setSelectedMajors([]);
                            }
                        }}
                    />
                </div>

                <div>
                    <h4 className="font-medium mb-2">Ciclo académico</h4>
                    <SelectTerm
                        mode="multiple"
                        className="w-full"
                        placeholder="Seleccionar ciclos"
                        value={selectedTerms}
                        onChange={(value) => {
                            if (Array.isArray(value)) {
                                setSelectedTerms(value);
                            } else if (value) {
                                setSelectedTerms([value]);
                            } else {
                                setSelectedTerms([]);
                            }
                        }}
                    />
                </div>

                <div>
                    <h4 className="font-medium mb-2">Universidad</h4>
                    <SelectEducationalInstitution
                        mode="multiple"
                        className="w-full"
                        placeholder="Seleccionar universidades"
                        value={selectedEducationalInstitutions}
                        onChange={(value) => {
                            if (Array.isArray(value)) {
                                setSelectedEducationalInstitutions(value);
                            } else if (value) {
                                setSelectedEducationalInstitutions([value]);
                            } else {
                                setSelectedEducationalInstitutions([]);
                            }
                        }}
                    />
                </div>

                <div>
                    <h4 className="font-medium mb-2">Fecha de creación</h4>
                    <div className="space-y-3">
                        <div>
                            <label className="text-sm text-gray-600 mb-1 block">
                                Seleccionar rango
                            </label>
                            <DatePicker.RangePicker
                                style={{ width: "100%" }}
                                placeholder={["Fecha inicio", "Fecha fin"]}
                                value={dateRange}
                                onChange={(dates) =>
                                    setDateRange(dates as [Dayjs, Dayjs] | null)
                                }
                                presets={[
                                    {
                                        label: "Hoy",
                                        value: [
                                            dayjs().startOf("day"),
                                            dayjs().endOf("day"),
                                        ],
                                    },
                                    {
                                        label: "Esta semana",
                                        value: [
                                            dayjs().startOf("week"),
                                            dayjs().endOf("week"),
                                        ],
                                    },
                                    {
                                        label: "Este mes",
                                        value: [
                                            dayjs().startOf("month"),
                                            dayjs().endOf("month"),
                                        ],
                                    },
                                    {
                                        label: "Último mes",
                                        value: [
                                            dayjs()
                                                .subtract(1, "month")
                                                .startOf("month"),
                                            dayjs().subtract(1, "month").endOf("month"),
                                        ],
                                    },
                                    {
                                        label: "Este año",
                                        value: [
                                            dayjs().startOf("year"),
                                            dayjs().endOf("year"),
                                        ],
                                    },
                                ]}
                            />
                        </div>
                    </div>
                </div>

                <div className="pt-4 space-y-2">
                    <Button type="primary" block onClick={handleApplyFilters}>
                        Aplicar filtros
                    </Button>
                    <Button block onClick={handleClearFilters}>
                        Limpiar filtros
                    </Button>
                </div>
            </div>
        </Drawer>
    );
}
