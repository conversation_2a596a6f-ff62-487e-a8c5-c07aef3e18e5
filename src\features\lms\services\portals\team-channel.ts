import { portalsApi } from "@services/portals";
import type { TeamChannel, TeamChannelQueryParams } from "../../types/team-channel";
import { PaginatedResponse } from "@myTypes/base";

export const getTeamChannels = async (
    params: TeamChannelQueryParams = {},
): Promise<PaginatedResponse<TeamChannel>> => {
    const response = await portalsApi.get("lms/team-channels", {
        params,
    });
    return response.data;
};
