import { <PERSON>po<PERSON>, Toolt<PERSON>, Badge } from "antd";
import { Calendar, CalendarPlus, Clock } from "lucide-react";
import { ContactListItem } from "@/features/crm/types/contact";
import { formatDateTime } from "@lib/helpers";
import dayjs from "dayjs";

const { Text } = Typography;

type ActivityInfoCellProps = {
    contact: ContactListItem;
};

const ActivityInfoCell = ({ contact }: ActivityInfoCellProps) => {
    const { lastGoogleSync } = contact;

    // Check if the sync is recent (less than 7 days)
    const isRecent = lastGoogleSync && dayjs().diff(dayjs(lastGoogleSync), "day") < 7;

    // Check if sync is old (more than 30 days)
    const isOld = lastGoogleSync && dayjs().diff(dayjs(lastGoogleSync), "day") > 30;

    // Determine the status color
    const getStatusColor = () => {
        if (!lastGoogleSync) return "default";
        if (isRecent) return "success";
        if (isOld) return "error";
        return "warning";
    };

    // Determine the status text
    const getStatusText = () => {
        if (!lastGoogleSync) return "Pendiente";
        if (isRecent) return "Reciente";
        if (isOld) return "Desactualizado";
        return "Completada";
    };

    return (
        <div className="flex flex-col gap-2.5">
            {/* Last activity date */}
            <div className="flex items-start gap-2">
                <Calendar size={14} className="text-blue-500 mt-1" strokeWidth={1.75} />
                <div>
                    <Text className="text-xs text-gray-500 block">
                        Última actividad
                    </Text>
                    <Text
                        className={`text-sm ${!lastGoogleSync ? "italic text-gray-400" : ""}`}
                    >
                        {lastGoogleSync
                            ? formatDateTime(lastGoogleSync)
                            : "Sin registro"}
                    </Text>
                </div>
            </div>

            {/* Sync status */}
            <div className="flex items-start gap-2">
                <CalendarPlus
                    size={14}
                    className="text-blue-500 mt-1"
                    strokeWidth={1.75}
                />
                <div>
                    <Text className="text-xs text-gray-500 block">Sincronización</Text>
                    <div className="flex items-center gap-2">
                        <Badge
                            status={
                                getStatusColor() as
                                    | "success"
                                    | "processing"
                                    | "error"
                                    | "default"
                                    | "warning"
                            }
                        />
                        <Text className="text-sm">{getStatusText()}</Text>
                    </div>
                </div>
            </div>

            {/* Time since last sync */}
            {lastGoogleSync && (
                <div className="flex items-start gap-2">
                    <Clock
                        size={14}
                        className="text-blue-500 mt-1"
                        strokeWidth={1.75}
                    />
                    <div>
                        <Text className="text-xs text-gray-500 block">
                            Tiempo desde última act.
                        </Text>
                        <Tooltip
                            title={`Última sincronización: ${formatDateTime(lastGoogleSync)}`}
                        >
                            <Text className="text-sm">
                                {dayjs().diff(dayjs(lastGoogleSync), "day") === 0
                                    ? "Hoy"
                                    : dayjs().diff(dayjs(lastGoogleSync), "day") === 1
                                      ? "Ayer"
                                      : `Hace ${dayjs().diff(dayjs(lastGoogleSync), "day")} días`}
                            </Text>
                        </Tooltip>
                    </div>
                </div>
            )}
        </div>
    );
};

export default ActivityInfoCell;
