import React from "react";
import { <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Tooltip, Skeleton } from "antd";
import { BarChart3, <PERSON>, Handshake, Info, TrendingUp } from "lucide-react";
import type {
    EventStats,
    ConversionMetric,
} from "@/features/crm/types/dashboard/events";

interface EventStatsCardsProps {
    stats?: EventStats;
    needsConciliation?: number;
    alliancesEnrollments?: number;
    conversion?: ConversionMetric;
    isLoading?: boolean;
}

const EventStatsCards: React.FC<EventStatsCardsProps> = ({
    stats,
    needsConciliation,
    alliancesEnrollments,
    conversion,
    isLoading = false,
}) => {
    if (isLoading) {
        return (
            <Row gutter={[16, 16]} className="mb-6">
                {[...Array(4)].map((_, index) => (
                    <Col xs={24} sm={12} lg={8} xl={6} key={index}>
                        <Card>
                            <Skeleton active paragraph={{ rows: 2 }} />
                        </Card>
                    </Col>
                ))}
            </Row>
        );
    }

    return (
        <Row gutter={[16, 16]}>
            {/* Total Events */}
            <Col xs={24} sm={12} lg={6}>
                <Card className="min-h-[130px]">
                    <Statistic
                        title={
                            <span className="text-gray-600 font-medium flex items-center">
                                Total de Eventos
                                <Tooltip title="Número total de eventos programados">
                                    <Info
                                        size={14}
                                        className="ml-1 text-gray-500 cursor-help"
                                    />
                                </Tooltip>
                            </span>
                        }
                        value={stats?.total || 0}
                        valueStyle={{ color: "#722ed1" }}
                        prefix={
                            <BarChart3 size={20} className="mr-2 text-purple-500" />
                        }
                    />
                </Card>
            </Col>

            {/* Needs Conciliation */}
            <Col xs={24} sm={12} lg={6}>
                <Card className="min-h-[130px]">
                    <Statistic
                        title={
                            <span className="text-gray-600 font-medium flex items-center">
                                Contactos por Conciliar
                                <Tooltip title="Contactos que requieren revisión o conciliación de datos">
                                    <Info
                                        size={14}
                                        className="ml-1 text-gray-500 cursor-help"
                                    />
                                </Tooltip>
                            </span>
                        }
                        value={needsConciliation || 0}
                        valueStyle={{ color: "#fa8c16" }}
                        prefix={<Users size={20} className="mr-2 text-orange-500" />}
                    />
                </Card>
            </Col>

            {/* Alliances Enrollments */}
            <Col xs={24} sm={12} lg={6}>
                <Card className="min-h-[130px]">
                    <Statistic
                        title={
                            <span className="text-gray-600 font-medium flex items-center">
                                Inscripciones de Alianzas
                                <Tooltip title="Número de inscripciones provenientes de alianzas estratégicas">
                                    <Info
                                        size={14}
                                        className="ml-1 text-gray-500 cursor-help"
                                    />
                                </Tooltip>
                            </span>
                        }
                        value={alliancesEnrollments || 0}
                        valueStyle={{ color: "#13c2c2" }}
                        prefix={<Handshake size={20} className="mr-2 text-cyan-500" />}
                    />
                </Card>
            </Col>

            {/* Conversion Rate */}
            <Col xs={24} sm={12} lg={6}>
                <Card className="min-h-[130px]">
                    <Statistic
                        title={
                            <span className="text-gray-600 font-medium flex items-center">
                                Tasa de Conversión
                                <Tooltip title="La conversión representa el porcentaje de usuarios registrados que posteriormente compraron el programa después de que finalizó el evento">
                                    <Info
                                        size={14}
                                        className="ml-1 text-gray-500 cursor-help"
                                    />
                                </Tooltip>
                            </span>
                        }
                        value={conversion?.percentage || 0}
                        suffix="%"
                        precision={1}
                        valueStyle={{ color: "#3f8600" }}
                        prefix={
                            <TrendingUp size={20} className="mr-2 text-green-500" />
                        }
                    />
                    {conversion && (
                        <div className="text-xs text-gray-500 mt-2">
                            {conversion.converted} de {conversion.totalEnrollments}{" "}
                            convertidos
                        </div>
                    )}
                </Card>
            </Col>
        </Row>
    );
};

export default EventStatsCards;
