import { useQuery } from "@tanstack/react-query";

import {
    getPaymentDashboardSummary,
    getPaymentDashboardHistorical,
    getPaymentDashboardCurrencyDistribution,
    getPaymentDashboardHistoricalMethods,
    getPaymentDashboardMethodsDistribution,
    getPaymentDashboardLastPayments,
} from "../services/portals/dashboard/payment";
import type {
    DashboardPaymentQueryParams,
    DashboardPaymentHistoricalQueryParams,
    PeriodFilter,
} from "../types/dashboard/payment";

// Hook for payment summary (main priority, loads first)
export const useDashboardPaymentsSummary = (
    query: DashboardPaymentQueryParams = {},
) => {
    const { data, isLoading, isError, isFetching } = useQuery({
        queryKey: ["dashboard-payments-summary", query],
        queryFn: () => getPaymentDashboardSummary(query ?? {}),
        refetchOnWindowFocus: false,
    });

    return {
        isLoading: isLoading || isFetching,
        isError,
        data,
    };
};

// Utility function to create reusable query parameters
export const createDashboardPaymentsQueryParams = (
    searchParams: URLSearchParams,
): DashboardPaymentQueryParams => {
    return {
        dateAfter: searchParams.get("dateAfter") || undefined,
        dateBefore: searchParams.get("dateBefore") || undefined,
        typeDate:
            (searchParams.get("typeDate") as
                | "created_at"
                | "payment_date"
                | "scheduled_payment_date") || "created_at", // API expects snake_case values
        status: (searchParams.get("status") as "all" | "paid" | "pending") || "all",
        currency: (searchParams.get("currency") as "pen" | "usd") || undefined, // pen, usd, or undefined for all
        paymentMethod: searchParams.get("paymentMethod") || undefined,
        offerings: searchParams.get("offerings") || undefined,
    };
};

// Hook for payment historical data (charts and trends)
export const useDashboardPaymentsHistorical = (
    query: DashboardPaymentHistoricalQueryParams,
) => {
    return useQuery({
        queryKey: ["payment-dashboard-historical", query],
        queryFn: () => getPaymentDashboardHistorical(query),
        enabled: !!query.period, // Only fetch when period is specified
        staleTime: 5 * 60 * 1000, // 5 minutes
        gcTime: 10 * 60 * 1000, // 10 minutes
    });
};

// Hook for currency distribution
export const useDashboardPaymentsCurrencyDistribution = (
    query: DashboardPaymentQueryParams = {},
) => {
    return useQuery({
        queryKey: ["payment-dashboard-currency-distribution", query],
        queryFn: () => getPaymentDashboardCurrencyDistribution(query),
        staleTime: 5 * 60 * 1000, // 5 minutes
    });
};

// Hook for historical payment methods
export const useDashboardPaymentsHistoricalMethods = (
    query: DashboardPaymentHistoricalQueryParams,
) => {
    return useQuery({
        queryKey: ["payment-dashboard-historical-methods", query],
        queryFn: () => getPaymentDashboardHistoricalMethods(query),
        enabled: !!query.period, // Only fetch when period is specified
        staleTime: 5 * 60 * 1000, // 5 minutes
        gcTime: 10 * 60 * 1000, // 10 minutes
    });
};

// Hook for payment methods distribution
export const useDashboardPaymentsMethodsDistribution = (
    query: DashboardPaymentQueryParams = {},
) => {
    return useQuery({
        queryKey: ["payment-dashboard-methods-distribution", query],
        queryFn: () => getPaymentDashboardMethodsDistribution(query),
        staleTime: 5 * 60 * 1000, // 5 minutes
    });
};

// Hook for last payments
export const useDashboardPaymentsLastPayments = (
    query: DashboardPaymentQueryParams = {},
) => {
    return useQuery({
        queryKey: ["payment-dashboard-last-payments", query],
        queryFn: () => getPaymentDashboardLastPayments(query),
        staleTime: 2 * 60 * 1000, // 2 minutes (more frequent updates for recent data)
    });
};

// Utility function to create historical query parameters (excludes date filters)
export const createDashboardPaymentsHistoricalQueryParams = (
    searchParams: URLSearchParams,
    period: PeriodFilter = "daily",
): DashboardPaymentHistoricalQueryParams => {
    return {
        status: (searchParams.get("status") as "all" | "paid" | "pending") || "all",
        currency: (searchParams.get("currency") as "pen" | "usd") || undefined,
        paymentMethod: searchParams.get("paymentMethod") || undefined,
        period,
    };
};
