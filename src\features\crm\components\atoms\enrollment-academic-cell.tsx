import { Typography, Tag, Tooltip } from "antd";
import { GraduationCap, BookOpen, Building } from "lucide-react";

const { Text } = Typography;

interface EnrollmentAcademicCellProps {
    university: string;
    major: string;
    term: string;
    occupation: string;
}

export default function EnrollmentAcademicCell({
    university,
    major,
    term,
    occupation,
}: EnrollmentAcademicCellProps) {
    return (
        <div className="flex flex-col gap-2">
            {/* University */}
            {university && (
                <div className="flex items-start gap-2">
                    <Building className="w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0" />
                    <Tooltip title={university}>
                        <Text className="text-xs text-gray-700 max-w-[150px] leading-tight">
                            {university}
                        </Text>
                    </Tooltip>
                </div>
            )}

            {/* Major */}
            {major && (
                <div className="flex items-start gap-2">
                    <GraduationCap className="w-4 h-4 text-purple-500 mt-0.5 flex-shrink-0" />
                    <Tooltip title={major}>
                        <Text className="text-xs text-gray-700 max-w-[150px] leading-tight">
                            {major}
                        </Text>
                    </Tooltip>
                </div>
            )}

            {/* Term & Occupation */}
            <div className="flex flex-wrap gap-1">
                {term && (
                    <Tag color="blue" className="text-xs">
                        <BookOpen className="w-3 h-3 mr-1" />
                        {term}
                    </Tag>
                )}
                {occupation && (
                    <Tag color="green" className="text-xs">
                        {occupation}
                    </Tag>
                )}
            </div>
        </div>
    );
}
