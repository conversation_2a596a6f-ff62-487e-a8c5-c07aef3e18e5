import { useQuery } from "@tanstack/react-query";
import type { TeamChannelQueryParams } from "../types/team-channel";
import { getTeamChannels } from "../services/portals/team-channel";

interface UseProgramProps {
    queryParams?: TeamChannelQueryParams;
    enabled?: boolean;
}

export const useTeamChannels = ({ enabled, queryParams }: UseProgramProps = {}) => {
    const { data, isLoading, isFetching, isError } = useQuery({
        queryKey: ["team-channels", queryParams],
        queryFn: () => getTeamChannels(queryParams),
        enabled,
    });

    const { count, results: teamChannels } = data || {
        count: 0,
        results: [],
    };

    return {
        isLoading: isLoading || isFetching,
        isError,
        teamChannels,
        count,
    };
};
