import type { Instructor } from "./instructor";
import type { File } from "./file";
import type { YooptaContentValue } from "@yoopta/editor";

export enum BlogStatus {
    DRAFT = "draft",
    PUBLISHED = "published",
    ARCHIVED = "archived",
}

/**
 * BlogCategory type definition
 */
export type BlogCategory = {
    bcid: string;
    name: string;
    slug: string;
    description?: string;
    createdAt: Date;
    updatedAt: Date;
};

/**
 * BlogTag type definition
 */
export type BlogTag = {
    btid: string;
    name: string;
    slug: string;
    description?: string;
    badgeColor: string;
    createdAt: Date;
    updatedAt: Date;
};

/**
 * BlogPost type definition
 */
export type BlogPost = {
    bid: string;
    title: string;
    slug: string;
    summary: string;
    content: YooptaContentValue;
    coverImage?: File;
    thumbnail?: File;
    readingTime?: number;
    status: BlogStatus;
    publishedAt?: Date;

    // visualization
    featured: boolean;
    featuredOrder?: number;

    // SEO
    metaTitle?: string;
    metaDescription?: string;
    metaKeywords?: string;

    // Analytics
    viewCount: number;

    // Audit fields
    createdBy?: {
        uid: string;
        username: string;
        fullName: string;
    };
    createdAt: Date;
    updatedAt: Date;

    authors?: Instructor[];
    categories?: BlogCategory[];
    tags?: BlogTag[];
};

export type CreateBlogPostBody = Omit<
    BlogPost,
    "bid" | "authors" | "categories" | "tags" | "createdAt" | "updatedAt"
> & {
    tags?: string[]; // Array of tag IDs
    authors?: string[]; // Array of author IDs
    categories?: string[]; // Array of category IDs
};

export type UpdateBlogPostBody = Partial<CreateBlogPostBody> & {
    coverImageId?: string;
    thumbnailId?: string;
};

export type BlogImageUploadResponse = {
    file: File;
};

// Create / update tags
export type CreateBlogTagBody = Omit<BlogTag, "btid" | "slug" | "createdAt" | "updatedAt">;
export type UpdateBlogTagBody = Partial<CreateBlogTagBody>;

// Create / update categories
export type CreateBlogCategoryBody = Omit<
    BlogCategory,
    "bcid" | "slug" | "createdAt" | "updatedAt"
>;
export type UpdateBlogCategoryBody = Partial<CreateBlogCategoryBody>;
