import { Template } from "@/features/crm/types/template";

export enum EventReminderStatus {
    DRAFT = "DRAFT",
    PENDING = "PENDING",
    SENT = "SENT",
    FAILED = "FAILED",
}
export enum EventReminderType {
    M0 = "M0",
    M1 = "M1",
    M2 = "M2",
    M3 = "M3",
}

export const EventReminderStatusLabel: Record<EventReminderStatus, string> = {
    [EventReminderStatus.DRAFT]: "Borrador",
    [EventReminderStatus.PENDING]: "Pendiente",
    [EventReminderStatus.SENT]: "Enviado",
    [EventReminderStatus.FAILED]: "Fallido",
};

export const EventReminderTypeLabel: Record<EventReminderType, string> = {
    [EventReminderType.M0]: "M0",
    [EventReminderType.M1]: "M1",
    [EventReminderType.M2]: "M2",
    [EventReminderType.M3]: "M3",
};
export type EventReminderVariableButton = {
    index: number;
    variable: string;
};
// Definición de la estructura de variables
export type EventReminderVariables = {
    body?: string[]; // Variables del cuerpo del mensaje (opcional)
    buttons?: EventReminderVariableButton[]; // Variables de los botones (opcional)
};

export type EventReminder = {
    rid: string; // UUID
    reminderType: EventReminderType;
    eventAllianceId: number;
    eventName: string;
    templateId: string; // UUID de la plantilla
    template: Template | null;
    variables?: EventReminderVariables | null; // Asegura que tenga body y buttons opcionales
    sendAt: string | Date;
    status: EventReminderStatus;
    createdAt: string | Date;
    updatedAt: string | Date;
};

// Formato para crear un nuevo recordatorio
export type CreateEventReminderFormValues = Omit<
    EventReminder,
    "rid" | "template" | "status" | "variables" | "createdAt" | "updatedAt"
>;

// Cuerpo de la petición para crear un nuevo recordatorio
export type CreateEventReminderBody = Omit<
    EventReminder,
    "template" | "variables" | "rid" | "createdAt" | "status" | "updatedAt"
>;

// Cuerpo de la petición para actualizar parcialmente un recordatorio
export type PartialUpdateEventReminderBody = Partial<
    Omit<EventReminder, "rid" | "eventName" | "template" | "createdAt" | "updatedAt">
>;
