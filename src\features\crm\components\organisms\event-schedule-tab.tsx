import { <PERSON><PERSON>, <PERSON><PERSON>, Modal } from "antd";
import { Event } from "../../types/event";
import EventSchedulesTable from "./event-schedules-table";
import { PlusIcon } from "lucide-react";
import { useState } from "react";
import CreateEventScheduleForm from "./create-event-schedule-form";
import { useEventSchedules } from "../../hooks/use-event-schedule";
import { ListEventSchedulesQueryParams } from "../../types/event-schedule";

type EventScheduleTabProps = {
    event: Event;
};

export default function EventScheduleTab({ event }: EventScheduleTabProps) {
    const { eid } = event;
    const [isModalOpen, setIsModalOpen] = useState(false);

    const queryParams: ListEventSchedulesQueryParams = {
        event: eid,
    };
    const { count, eventSchedules } = useEventSchedules({ queryParams });

    const handleModalClose = () => {
        setIsModalOpen(false);
    };

    return (
        <div className="space-y-2">
            <div className="flex p-2 rounded-md bg-white-full shadow-sm justify-between items-center">
                <div className="flex items-center gap-1">
                    <p className="text-lg text-gray-700 font-medium">Total</p>
                    <Badge count={count} color="blue" />
                </div>
                <div>
                    <Button
                        type="primary"
                        icon={<PlusIcon />}
                        onClick={() => setIsModalOpen(true)}
                    >
                        Agregar horario
                    </Button>
                    <Modal
                        centered
                        title={
                            <div className="w-full flex justify-center text-xl py-1">
                                Agregar nuevo Horario
                            </div>
                        }
                        onCancel={handleModalClose}
                        open={isModalOpen}
                        footer={false}
                    >
                        <CreateEventScheduleForm
                            closeModal={handleModalClose}
                            initialValues={{
                                event: eid,
                            }}
                        />
                    </Modal>
                </div>
            </div>
            <EventSchedulesTable eventSchedules={eventSchedules} />
        </div>
    );
}
