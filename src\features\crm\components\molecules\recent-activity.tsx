import React from "react";
import { Card, List, Avatar, Tag } from "antd";
import { ClockIcon } from "lucide-react";

interface RecentContactsProps {
    activities: {
        id: number;
        contact: string;
        action: string;
        date: string;
    }[];
}

const formatRelativeTime = (dateStr: string): string => {
    const rtf = new Intl.RelativeTimeFormat("es", { numeric: "auto" });
    const now = new Date();
    const pastDate = new Date(dateStr);
    const diffMs = now.getTime() - pastDate.getTime();

    // Convertir a segundos
    const diffSec = Math.floor(diffMs / 1000);

    // Determinar la mejor unidad para mostrar el tiempo relativo
    if (diffSec < 60) {
        return rtf.format(-Math.floor(diffSec), "second");
    } else if (diffSec < 3600) {
        return rtf.format(-Math.floor(diffSec / 60), "minute");
    } else if (diffSec < 86400) {
        return rtf.format(-Math.floor(diffSec / 3600), "hour");
    } else if (diffSec < 604800) {
        return rtf.format(-Math.floor(diffSec / 86400), "day");
    } else if (diffSec < 2592000) {
        return rtf.format(-Math.floor(diffSec / 604800), "week");
    } else if (diffSec < 31536000) {
        return rtf.format(-Math.floor(diffSec / 2592000), "month");
    } else {
        return rtf.format(-Math.floor(diffSec / 31536000), "year");
    }
};

const RecentActivity: React.FC<RecentContactsProps> = ({ activities }) => {
    return (
        <Card
            title={
                <div className="flex items-center gap-2">
                    <ClockIcon className="h-5 w-5 text-blue-500" />
                    <span>Actividad Reciente</span>
                </div>
            }
            className="shadow-md h-full"
        >
            <List
                dataSource={activities}
                renderItem={(item) => (
                    <List.Item>
                        <List.Item.Meta
                            avatar={
                                <Avatar className="bg-blue-100 text-blue-600">
                                    {item.contact.charAt(0)}
                                </Avatar>
                            }
                            title={item.contact}
                            description={
                                <div className="flex flex-col sm:flex-row sm:items-center gap-2">
                                    <Tag
                                        color={
                                            item.action === "Nuevo contacto"
                                                ? "green"
                                                : "blue"
                                        }
                                    >
                                        {item.action}
                                    </Tag>
                                    <span className="text-xs text-gray-500">
                                        {formatRelativeTime(item.date)}
                                    </span>
                                </div>
                            }
                        />
                    </List.Item>
                )}
            />
        </Card>
    );
};

export default RecentActivity;
