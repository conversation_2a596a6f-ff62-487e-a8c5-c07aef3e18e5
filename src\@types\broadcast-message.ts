import { Template } from "@/features/crm/types/template";

export enum BroadcastMessageStatus {
    DRAFT = "DRAFT",
    PENDING = "PENDING",
    SENT = "SENT",
    FAILED = "FAILED",
}

export enum BroadcastMessagePipelineState {
    PROSPECT = "PROSPECT",
    LEAD = "LEAD",
    PENDING_PAYMENT = "PENDING_PAYMENT",
    SOLD = "SOLD",
}

export const BroadcastMessageStatusLabel: Record<BroadcastMessageStatus, string> = {
    [BroadcastMessageStatus.DRAFT]: "Borrador",
    [BroadcastMessageStatus.PENDING]: "Pendiente",
    [BroadcastMessageStatus.SENT]: "Enviado",
    [BroadcastMessageStatus.FAILED]: "Fallido",
};

export const BroadcastMessagePipelineStateLabel: Record<
    BroadcastMessagePipelineState,
    string
> = {
    [BroadcastMessagePipelineState.PROSPECT]: "Prospectos",
    [BroadcastMessagePipelineState.LEAD]: "Interesados",
    [BroadcastMessagePipelineState.PENDING_PAYMENT]: "Por pagar",
    [BroadcastMessagePipelineState.SOLD]: "Vendidos",
};

export type BroadcastMessageVariableButton = {
    index: number;
    variable: string;
};
// Definición de la estructura de variables
export type BroadcastMessageVariables = {
    body?: string[]; // Variables del cuerpo del mensaje (opcional)
    buttons?: BroadcastMessageVariableButton[]; // Variables de los botones (opcional)
};

export type BroadcastMessage = {
    mid: string; // UUID
    offeringId?: number | null;
    offeringName?: string | null;
    pipelineState: BroadcastMessagePipelineState;
    templateId: string; // UUID de la plantilla
    template: Template;
    variables?: BroadcastMessageVariables | null;
    sendAt: string | Date;
    status: BroadcastMessageStatus;
    createdAt: string | Date;
    updatedAt: string | Date;
};

// Formato para crear un nuevo Broadcast Message
export type CreateBroadcastMessageFormValues = Omit<
    BroadcastMessage,
    "mid" | "template" | "status" | "variables" | "createdAt" | "updatedAt"
>;

// Cuerpo de la petición para crear un nuevo Broadcast Message
export type CreateBroadcastMessageBody = Omit<
    BroadcastMessage,
    "template" | "variables" | "mid" | "createdAt" | "status" | "updatedAt"
>;

// Cuerpo de la petición para actualizar parcialmente un Broadcast Message
export type PartialUpdateBroadcastMessageBody = Partial<
    Omit<
        BroadcastMessage,
        "mid" | "offeringName" | "template" | "createdAt" | "updatedAt"
    >
>;
