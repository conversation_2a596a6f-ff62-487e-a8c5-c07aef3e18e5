import { PaymentListItem, PaymentCurrency } from "../../types/payment";
import { DollarSign, Coins } from "lucide-react";

const formatAmount = (amount: number, currency: PaymentCurrency): string => {
    if (currency === PaymentCurrency.USD) {
        return new Intl.NumberFormat("en-US", {
            style: "currency",
            currency: "USD",
            minimumFractionDigits: 2,
        }).format(amount);
    } else if (currency === PaymentCurrency.PEN) {
        return new Intl.NumberFormat("es-PE", {
            style: "currency",
            currency: "PEN",
            minimumFractionDigits: 2,
        }).format(amount);
    }
    return amount.toString();
};

const getCurrencyInfo = (currency: PaymentCurrency) => {
    switch (currency) {
        case PaymentCurrency.USD:
            return {
                icon: (
                    <DollarSign
                        size={18}
                        className="text-emerald-600"
                        strokeWidth={1.75}
                    />
                ),
                name: "USD",
                bgColor: "bg-emerald-50",
                textColor: "text-emerald-800",
            };
        case PaymentCurrency.PEN:
            return {
                icon: <Coins size={18} className="text-blue-600" strokeWidth={1.75} />,
                name: "PEN",
                bgColor: "bg-blue-50",
                textColor: "text-blue-800",
            };
        default:
            return {
                icon: <Coins size={18} className="text-gray-600" strokeWidth={1.75} />,
                name: currency,
                bgColor: "bg-gray-50",
                textColor: "text-gray-800",
            };
    }
};

export default function PaymentAmountCell({
    amount,
    payment,
}: {
    amount: number;
    payment: PaymentListItem;
}) {
    const { currency } = payment;
    const formattedAmount = formatAmount(amount, currency);
    const currencyInfo = getCurrencyInfo(currency);

    return (
        <div className="flex flex-col gap-1">
            <div className="flex items-center gap-1.5">
                {currencyInfo.icon}
                <span className="text-base font-semibold text-gray-900">
                    {formattedAmount}
                </span>
            </div>

            <div className="flex items-center">
                <span
                    className={`text-xs font-medium px-2 py-0.5 rounded-full ${currencyInfo.bgColor} ${currencyInfo.textColor} border border-opacity-30 ${currencyInfo.bgColor.replace("bg-", "border-")}`}
                >
                    {currencyInfo.name}
                </span>
            </div>
        </div>
    );
}
