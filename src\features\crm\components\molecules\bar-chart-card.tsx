import React from "react";
import { Card } from "antd";
import {
    <PERSON><PERSON><PERSON>,
    <PERSON>,
    <PERSON>Axis,
    <PERSON>Axis,
    CartesianGrid,
    Toolt<PERSON>,
    ResponsiveContainer,
    Legend,
} from "recharts";

type LegendItem = {
    name: string;
    color: string;
};

interface BarChartCardProps {
    title: string;
    data: Array<Record<string, unknown>>;
    dataKey: string;
    xAxisDataKey?: string;
    secondaryDataKey?: string;
    barColor?: string;
    barColors?: string[];
    icon?: React.ReactNode;
    formatter?: (value: number) => string;
    legend?: LegendItem[];
    stacked?: boolean;
    stackedDataKeys?: string[]; // Claves de datos para el gráfico apilado
    stackedColors?: string[]; // Colores para cada segmento del stack
}

const BarChartCard: React.FC<BarChartCardProps> = ({
    title,
    data,
    dataKey,
    xAxisDataKey = "name",
    secondaryDataKey,
    barColor = "#1890ff",
    barColors = ["#1890ff", "#faad14"],
    icon,
    formatter,
    legend,
    stacked = false,
    stackedDataKeys = [],
    stackedColors = ["#4096ff", "#36cfc9", "#faad14", "#73d13d", "#ff4d4f"],
}) => {
    interface CustomTooltipProps {
        active?: boolean;
        payload?: Array<{
            value: number;
            dataKey: string;
            name: string;
            color: string;
        }>;
        label?: string;
    }

    const CustomTooltip = ({ active, payload, label }: CustomTooltipProps) => {
        if (active && payload && payload.length) {
            // Calcular total para gráficos apilados
            const total =
                stacked && payload.length > 1
                    ? payload.reduce((sum, entry) => sum + entry.value, 0)
                    : null;

            return (
                <div className="bg-white-full p-2 border border-gray-200 shadow-md rounded">
                    <p className="font-medium">{`${label}`}</p>
                    {payload.map((entry, index) => (
                        <p key={index} style={{ color: entry.color }}>
                            {legend && legend[index]
                                ? `${legend[index].name}: `
                                : `${entry.name}: `}
                            {formatter ? formatter(entry.value) : entry.value}
                        </p>
                    ))}
                    {total !== null && (
                        <div className="border-t border-gray-200 mt-1 pt-1">
                            <p className="font-medium">
                                Total: {formatter ? formatter(total) : total}
                            </p>
                        </div>
                    )}
                </div>
            );
        }
        return null;
    };

    return (
        <Card
            title={
                <div className="flex items-center gap-2">
                    {icon}
                    <span>{title}</span>
                </div>
            }
            className="shadow-md h-full"
        >
            <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                        data={data}
                        margin={{
                            top: 5,
                            right: 30,
                            left: 20,
                            bottom: 5,
                        }}
                    >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis
                            dataKey={xAxisDataKey}
                            angle={-45}
                            textAnchor="end"
                            height={80}
                            interval={0}
                            tick={{ fontSize: 12 }}
                        />
                        <YAxis
                            tickFormatter={(value) =>
                                formatter
                                    ? formatter(value).split(" ")[0] // For currency formatters
                                    : value
                            }
                        />
                        <Tooltip content={<CustomTooltip />} />
                        {legend && <Legend />}

                        {/* Renderizado condicional: stacked vs normal */}
                        {stacked && stackedDataKeys.length > 0 ? (
                            // Gráfico apilado
                            stackedDataKeys.map((key, index) => (
                                <Bar
                                    key={key}
                                    dataKey={key}
                                    stackId="stack"
                                    fill={stackedColors[index % stackedColors.length]}
                                    name={legend && legend[index] ? legend[index].name : key}
                                    radius={
                                        index === stackedDataKeys.length - 1
                                            ? [4, 4, 0, 0]
                                            : [0, 0, 0, 0]
                                    }
                                    animationDuration={1500}
                                />
                            ))
                        ) : (
                            // Gráfico normal (implementación actual)
                            <>
                                <Bar
                                    dataKey={dataKey}
                                    fill={barColor}
                                    name={legend ? legend[0].name : dataKey}
                                    radius={[4, 4, 0, 0]}
                                    animationDuration={1500}
                                />
                                {secondaryDataKey && (
                                    <Bar
                                        dataKey={secondaryDataKey}
                                        fill={barColors[1]}
                                        name={
                                            legend ? legend[1].name : secondaryDataKey
                                        }
                                        radius={[4, 4, 0, 0]}
                                        animationDuration={1500}
                                    />
                                )}
                            </>
                        )}
                    </BarChart>
                </ResponsiveContainer>
            </div>
        </Card>
    );
};

export default BarChartCard;
