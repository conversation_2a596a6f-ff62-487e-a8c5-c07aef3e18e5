import { But<PERSON>, Dropdown, Table, Tooltip } from "antd";
import { <PERSON> } from "react-router-dom";
import type { TableProps } from "antd";
import type { Major } from "@/features/crm/types/major";
import { MoreVertical, Scissors } from "lucide-react";
import EditStroke from "@assets/icons/huge/edit-stroke.svg?react";
import DeleteStroke from "@assets/icons/huge/delete-stroke.svg?react";

type MajorsTableProps = {
    data: Major[];
    loading?: boolean;
    onEdit?: (major: Major) => void;
    onDelete?: (major: Major) => void;
};

export default function MajorsTable({
    data,
    loading,
    onEdit,
    onDelete,
}: MajorsTableProps) {
    const baseColumns: TableProps<Major>["columns"] = [
        {
            title: "ID",
            dataIndex: "mid",
            key: "mid",
            width: 120,
            render: (mid: string) => (
                <Link
                    to={`/crm/majors/${mid}`}
                    className="text-blue-full font-semibold"
                >
                    {mid?.slice(-6)}
                </Link>
            ),
        },
        {
            title: "NOMBRE",
            dataIndex: "name",
            key: "name",
        },
    ];
    const actionColumn: TableProps<Major>["columns"] = [
        {
            title: <Scissors />,
            key: "actions",
            width: 80,
            render: (_: unknown, record: Major) => (
                <Dropdown
                    trigger={["click"]}
                    menu={{
                        items: [
                            {
                                key: "edit",
                                label: (
                                    <div className="flex items-center gap-2 text-blue-full">
                                        <EditStroke className="w-5 h-5" /> Editar
                                    </div>
                                ),
                            },
                            {
                                key: "delete",
                                label: (
                                    <div className="flex items-center gap-2 text-state-red-full">
                                        <DeleteStroke className="w-5 h-5" /> Eliminar
                                    </div>
                                ),
                            },
                        ],
                        onClick: ({ key }: { key: string }) => {
                            if (key === "edit" && onEdit) onEdit(record);
                            if (key === "delete" && onDelete) onDelete(record);
                        },
                    }}
                    placement="bottomRight"
                >
                    <Tooltip title="Acciones">
                        <Button
                            icon={<MoreVertical className="w-5 h-5" />}
                            type="text"
                            size="small"
                        />
                    </Tooltip>
                </Dropdown>
            ),
        },
    ];

    return (
        <Table
            className="rounded-lg shadow-sm"
            loading={loading}
            pagination={false}
            dataSource={data}
            rowKey="mid"
            columns={[...baseColumns, ...(onEdit || onDelete ? actionColumn : [])]}
            scroll={{ x: "max-content" }}
        />
    );
}
