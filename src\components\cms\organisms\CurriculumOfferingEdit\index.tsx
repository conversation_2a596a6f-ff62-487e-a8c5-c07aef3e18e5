import { MessageInstance } from "antd/es/message/interface";
import { NotificationInstance } from "antd/es/notification/interface";
import React, { useState, useCallback, useMemo } from "react";
import { Form, Input, Button, Card, Space, Typography, Select, Divider } from "antd";
import { PlusCircle, MinusCircle, ChevronDown, ChevronRight, Plus } from "lucide-react";
import { Offering } from "@myTypes/offering";
import { useMutation, useQuery } from "@tanstack/react-query";
import { deleteOffering, updateOffering } from "@services/portals/cms/offering";
import { useNavigate } from "react-router-dom";
import { AxiosError } from "axios";
import type { SelectProps } from "antd";

import Save from "@assets/icons/general/save-stroke.svg?react";
import Trash from "@assets/icons/huge/trash-white.svg?react";
import { getInstructors } from "@services/portals/cms/instructor";
import { InstructorStatusEnum } from "@myTypes/instructor";

const { Title } = Typography;

interface CurriculumModulesEditProps {
    data: Offering;
    oid: string;
    messageApi: MessageInstance;
    notificationApi: NotificationInstance;
    handleRefetch: () => void;
}

interface ExpandedSections {
    modules: Record<string, boolean>;
    courses: Record<string, boolean>;
}

type FormValues = Pick<Offering, "modules" | "instructors">;

interface ModuleField {
    name: number;
    key: number;
    fieldKey?: number;
}

interface CourseField {
    name: number;
    key: number;
    fieldKey?: number;
}

interface TopicField {
    name: number;
    key: number;
    fieldKey?: number;
}

const CurriculumModulesEdit: React.FC<CurriculumModulesEditProps> = ({
    data,
    oid,
    messageApi,
    handleRefetch,
}) => {
    const navigate = useNavigate();
    const [form] = Form.useForm<FormValues>();
    const [expandedSections, setExpandedSections] = useState<ExpandedSections>({
        modules: {},
        courses: {},
    });

    const { mutate: saveMutate } = useMutation({
        mutationFn: (values: FormValues) => updateOffering(oid, values),
        onSuccess: () => {
            messageApi.success("Cambios guardados exitosamente");
            handleRefetch();
        },
        onError: () => {
            messageApi.error("Error al actualizar los objetivos");
        },
    });

    const { mutate: deleteMutate, isError } = useMutation({
        mutationFn: () => deleteOffering(oid as string),
        onSuccess: () => {
            navigate("/cms/offering", { replace: true });
        },
        onError: (error: AxiosError) => {
            console.error(error);
        },
    });

    const toggleSection = useCallback(
        (type: keyof ExpandedSections, index: string | number) => {
            setExpandedSections((prev) => ({
                ...prev,
                [type]: {
                    ...prev[type],
                    [index]: !prev[type][index],
                },
            }));
        },
        [],
    );

    const onFinish = async (values: FormValues) => {
        saveMutate({ ...values, instructors: selectedInstructors });
    };

    const handleSave = useCallback(() => {
        form.submit();
    }, [form]);

    const renderModuleFields = (
        fields: ModuleField[],
        { add, remove }: { add: () => void; remove: (index: number) => void },
    ) => (
        <div>
            <Space style={{ marginBottom: 16 }} align="baseline">
                <Title level={5}>Módulos</Title>
            </Space>

            {fields.map((moduleField, moduleIndex) => (
                <Card
                    key={moduleField.key}
                    style={{ marginBottom: 16 }}
                    size="small"
                    title={
                        <Space>
                            <Button
                                type="text"
                                onClick={() => toggleSection("modules", moduleIndex)}
                                icon={
                                    expandedSections.modules[moduleIndex] ? (
                                        <ChevronDown size={16} />
                                    ) : (
                                        <ChevronRight size={16} />
                                    )
                                }
                            />
                            <span>Módulo {moduleIndex + 1}</span>
                        </Space>
                    }
                    extra={
                        <Button
                            type="text"
                            onClick={() => remove(moduleIndex)}
                            icon={<MinusCircle size={16} className="text-red-500" />}
                        />
                    }
                >
                    {expandedSections.modules[moduleIndex] && (
                        <>
                            <Form.Item
                                name={[moduleField.name, "title"]}
                                label="Nombre"
                                rules={[
                                    {
                                        required: true,
                                        message: "Nombre requerido",
                                    },
                                ]}
                            >
                                <Input placeholder="Nombre del Módulo" />
                            </Form.Item>

                            <Form.List name={[moduleField.name, "courses"]}>
                                {(
                                    courseFields,
                                    { add: addCourse, remove: removeCourse },
                                ) =>
                                    renderCourseFields(
                                        courseFields,
                                        { add: addCourse, remove: removeCourse },
                                        moduleIndex,
                                    )
                                }
                            </Form.List>
                        </>
                    )}
                </Card>
            ))}

            <div className="flex justify-end">
                <Button
                    type="dashed"
                    onClick={() => add()}
                    icon={<PlusCircle size={16} />}
                >
                    Agregar Módulo
                </Button>
            </div>
        </div>
    );

    const renderCourseFields = (
        courseFields: CourseField[],
        { add, remove }: { add: () => void; remove: (index: number) => void },
        moduleIndex: number,
    ) => (
        <div style={{ marginLeft: 24 }}>
            <Space style={{ marginBottom: 16 }} align="baseline">
                <Title level={5}>Cursos</Title>
            </Space>

            {courseFields.map((courseField, courseIndex) => (
                <Card
                    key={courseField.key}
                    style={{ marginBottom: 16 }}
                    size="small"
                    title={
                        <Space>
                            <Button
                                type="text"
                                onClick={() =>
                                    toggleSection(
                                        "courses",
                                        `${moduleIndex}-${courseIndex}`,
                                    )
                                }
                                icon={
                                    expandedSections.courses[
                                        `${moduleIndex}-${courseIndex}`
                                    ] ? (
                                        <ChevronDown size={16} />
                                    ) : (
                                        <ChevronRight size={16} />
                                    )
                                }
                            />
                            <span>Curso {courseIndex + 1}</span>
                        </Space>
                    }
                    extra={
                        <Button
                            type="text"
                            onClick={() => remove(courseIndex)}
                            icon={<MinusCircle size={16} className="text-red-500" />}
                        />
                    }
                >
                    {expandedSections.courses[`${moduleIndex}-${courseIndex}`] && (
                        <>
                            <Form.Item
                                name={[courseField.name, "title"]}
                                label="Nombre"
                                rules={[
                                    {
                                        required: true,
                                        message: "Nombre requerido",
                                    },
                                ]}
                            >
                                <Input placeholder="Nombre del Curso" />
                            </Form.Item>

                            <Form.List name={[courseField.name, "topics"]}>
                                {(
                                    topicFields,
                                    { add: addTopic, remove: removeTopic },
                                ) =>
                                    renderTopicFields(topicFields, {
                                        add: addTopic,
                                        remove: removeTopic,
                                    })
                                }
                            </Form.List>
                        </>
                    )}
                </Card>
            ))}
            <div className="flex justify-end">
                <Button
                    type="dashed"
                    onClick={() => add()}
                    icon={<PlusCircle size={16} />}
                >
                    Agregar curso
                </Button>
            </div>
        </div>
    );

    const renderTopicFields = (
        topicFields: TopicField[],
        { add, remove }: { add: () => void; remove: (index: number) => void },
    ) => (
        <div style={{ marginLeft: 16 }}>
            {topicFields.map((topicField, topicIndex) => (
                <Card
                    key={topicField.key}
                    style={{ marginBottom: 16 }}
                    size="small"
                    title={`Tópico ${topicIndex + 1}`}
                    extra={
                        <Button
                            type="text"
                            onClick={() => remove(topicIndex)}
                            icon={<MinusCircle size={16} className="text-red-500" />}
                        />
                    }
                >
                    <Form.Item
                        name={[topicField.name, "title"]}
                        label="Nombre"
                        rules={[
                            {
                                required: true,
                                message: "Nombre requerido",
                            },
                        ]}
                    >
                        <Input placeholder="Nombre del Tópico" />
                    </Form.Item>
                </Card>
            ))}
            <div className="flex justify-end">
                <Button
                    type="dashed"
                    onClick={() => add()}
                    icon={<PlusCircle size={16} />}
                >
                    Agregar tópico
                </Button>
            </div>
        </div>
    );

    const { data: instructorsResponse } = useQuery({
        queryKey: ["instructors"],
        queryFn: () =>
            getInstructors({
                filters: {
                    status: [InstructorStatusEnum.Published],
                },
            }),
    });

    const { results: instructors } = instructorsResponse || {
        results: [],
    };

    const selectInstructorsOptions: SelectProps["options"] = instructors.map(
        (instructor) => {
            return {
                value: instructor.iid,
                label: instructor.fullName,
            };
        },
    );

    const [selectedInstructorsIds, setSelectedInstructorsIds] = useState<string[]>(
        data.instructors ? (data.instructors as string[]) : [],
    );

    const handleOnSelectChange = (value: string[]) => {
        setSelectedInstructorsIds(value);
    };

    const selectedInstructors = useMemo(
        () =>
            instructors.filter((instructor) =>
                selectedInstructorsIds.includes(instructor.iid),
            ),
        [instructors, selectedInstructorsIds],
    );

    return (
        <div className="grid grid-cols-1 lg:grid-cols-6 gap-y-6 lg:gap-6">
            <div className="bg-white-full col-span-1 lg:col-span-4 p-5 rounded-lg shadow-sm">
                <p className="text-gray-400 font-semibold text-sm">
                    GESTIÓN DE CONTENIDO
                </p>
                <Form<FormValues>
                    form={form}
                    initialValues={data}
                    onFinish={onFinish}
                    layout="vertical"
                    style={{ maxWidth: "800px", margin: "0 auto" }}
                >
                    <Form.List name="modules">
                        {(fields, { add, remove }) =>
                            renderModuleFields(fields, { add, remove })
                        }
                    </Form.List>
                </Form>
            </div>
            <div className="col-span-1 lg:col-span-2 space-y-6">
                <div className="bg-white-full p-5 rounded-lg shadow-sm">
                    <p className="text-gray-400 font-semibold text-sm">ACCIONES</p>
                    <div className="flex justify-end gap-3">
                        <Button
                            type="primary"
                            size="large"
                            style={{ fontSize: 16 }}
                            icon={<Trash />}
                            danger
                            disabled={isError}
                            onClick={() => deleteMutate()}
                        >
                            Eliminar
                        </Button>
                        <Button
                            type="primary"
                            size="large"
                            style={{ fontSize: 16 }}
                            icon={<Save />}
                            onClick={handleSave}
                        >
                            Guardar
                        </Button>
                    </div>
                </div>

                <div className="bg-white-full p-5 rounded-lg shadow-sm">
                    <p className="text-gray-400 font-semibold text-sm">INSTRUCTORES</p>
                    <div className="py-4 space-y-4">
                        <Select
                            showSearch
                            allowClear
                            mode="multiple"
                            size="large"
                            className="w-full"
                            placeholder="Selecciona los docentes"
                            options={selectInstructorsOptions}
                            onChange={handleOnSelectChange}
                            defaultValue={[...(data.instructors as string[])]}
                            dropdownRender={(menu) => (
                                <>
                                    {menu}
                                    <Divider
                                        style={{ margin: "8px 0" }}
                                        className="hidden"
                                    />
                                    <div className="hidden items-center justify-between px-2">
                                        <p className="text-gray-500">
                                            ¿No encuentra al instructor?
                                        </p>
                                        <Button icon={<Plus />} type="primary">
                                            Agregar
                                        </Button>
                                    </div>
                                </>
                            )}
                        />

                        <div className="space-y-2">
                            {selectedInstructors.map((instructor) => (
                                <div
                                    key={instructor.iid}
                                    className="flex items-center gap-3"
                                >
                                    <div className="w-12 h-12 overflow-hidden rounded-full flex-shrink-0">
                                        <img
                                            className="w-full h-full object-cover"
                                            src={instructor.profilePhoto?.url}
                                            alt={instructor.profilePhoto?.name}
                                        />
                                    </div>
                                    <p className="text-gray-800 font-medium">
                                        {instructor.fullName}
                                    </p>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default CurriculumModulesEdit;
