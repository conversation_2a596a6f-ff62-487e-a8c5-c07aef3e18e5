import { Config<PERSON>rovider, Table, Typography, Tooltip } from "antd";
import type { TableProps } from "antd";
import { useMemo } from "react";
import { Event } from "../../types/event";
import { Link } from "react-router-dom";
import { Hash } from "lucide-react";
import EventNameCell from "../atoms/event-name-cell";
import EventTypeCell from "../atoms/event-type-cell";
import EventInstructorCell from "../atoms/event-instructor-cell";
import EventDateCell from "../atoms/event-date-cell";

const { Text } = Typography;

const INITIAL_COLUMNS: TableProps<Event>["columns"] = [
    {
        title: "ID",
        dataIndex: "eid",
        key: "eid",
        width: 80,
        render: (eid: string) => (
            <Tooltip title="Ver detalles del evento">
                <Link
                    to={`/crm/events/${eid}`}
                    className="font-semibold text-blue-full flex items-center gap-1"
                >
                    <Hash size={14} strokeWidth={2.5} className="text-gray-500" />
                    {eid.slice(-6)}
                </Link>
            </Tooltip>
        ),
    },
    {
        title: "NOMBRE",
        dataIndex: "name",
        key: "name",
        width: 480,
        render: (_: string, record: Event) => <EventNameCell event={record} />,
    },
    {
        title: "PROGRAMA",
        dataIndex: "offering",
        key: "offering",
        render: (offering: Event["offering"]) => (
            <div className="max-w-60 space-y-1">
                {offering && (
                    <>
                        <div className="truncate">
                            <Text className="font-medium text-gray-900">
                                {offering.name || ""}
                            </Text>
                        </div>
                        <div className="truncate">
                            <Text className="text-xs text-gray-500 font-mono bg-gray-50 px-2 py-0.5 rounded">
                                {offering.codeName || offering.slug || ""}
                            </Text>
                        </div>
                    </>
                )}
            </div>
        ),
    },
    {
        title: "TIPO/MODALIDAD",
        key: "type",
        width: 180,
        render: (_, record: Event) => (
            <EventTypeCell type={record.type} modality={record.modality} />
        ),
    },
    {
        title: "INSTRUCTOR",
        dataIndex: "instructor",
        key: "instructor",
        render: (_: Event["instructor"], record: Event) => (
            <EventInstructorCell
                instructor={record.instructor}
                offering={record.offering}
            />
        ),
    },
    {
        title: "FECHAS",
        dataIndex: "createdAt",
        key: "dates",
        render: (_: string, record: Event) => (
            <div className="space-y-2">
                <EventDateCell date={record.createdAt} label="Creación" />
                <EventDateCell date={record.updatedAt} label="Actualización" />
            </div>
        ),
    },
];

type EventsTableProps = {
    events: Event[];
};

export default function EventsTable({ events }: EventsTableProps) {
    const columns = useMemo(() => INITIAL_COLUMNS, []);

    return (
        <>
            <ConfigProvider
                theme={{
                    components: {
                        Table: {
                            headerBg: "#FBFCFD",
                            borderColor: "#fff",
                            headerSplitColor: "#fafafa",
                            headerBorderRadius: 8,
                            rowHoverBg: "#F6FAFD",
                            rowSelectedBg: "#F6FAFD",
                            rowSelectedHoverBg: "#F6FAFD",
                            footerBg: "#F1F1F1",
                        },
                    },
                }}
            >
                <Table
                    className="rounded-lg shadow-sm"
                    footer={() => ""}
                    columns={columns}
                    pagination={false}
                    dataSource={events}
                    rowKey="eid"
                    scroll={{ x: "max-content" }}
                    locale={{
                        emptyText: (
                            <div className="text-center py-4">
                                <Text type="secondary">
                                    No hay eventos para mostrar
                                </Text>
                            </div>
                        ),
                    }}
                    onChange={(_p, filters, sorters) => {
                        console.log("filters", filters);
                        console.log("sorters", sorters);
                    }}
                />
            </ConfigProvider>
        </>
    );
}
