import { portalsApi } from "@services/portals";
import {
    EventReminder,
    EventReminderMetrics,
    EventReminderMetricsQueryParams,
    ListEventRemindersQueryParams,
} from "../../types/event-reminder";

export interface EventRemindersResponse {
    count: number;
    next: string | null;
    previous: string | null;
    results: EventReminder[];
}

// Get event reminders list
export const listEventReminders = async (
    queryParams: ListEventRemindersQueryParams = {},
): Promise<EventRemindersResponse> => {
    const res = await portalsApi.get("crm/event-reminders", {
        params: queryParams,
    });
    return res.data;
};

// Get event reminders metrics
export const getEventReminderMetrics = async (
    queryParams: EventReminderMetricsQueryParams = {},
): Promise<EventReminderMetrics> => {
    const res = await portalsApi.get("crm/event-reminders/metrics", {
        params: queryParams,
    });
    return res.data;
};

// Bulk retry failed invitations
export const bulkRetryEventReminders = async (): Promise<void> => {
    await portalsApi.post("crm/event-reminders/bulk-retry");
};

// Delete event reminder
export const deleteEventReminder = async (rid: string): Promise<void> => {
    await portalsApi.delete(`crm/event-reminders/${rid}`);
};

// Retry email invitation
export const retryEmailInvitation = async (rid: string): Promise<void> => {
    await portalsApi.post(`crm/event-reminders/${rid}/retry-email`);
};

// Retry WhatsApp invitation
export const retryWhatsappInvitation = async (rid: string): Promise<void> => {
    await portalsApi.post(`crm/event-reminders/${rid}/retry-whatsapp`);
};

// Send email invitation
export const sendEmailInvitation = async (rid: string): Promise<void> => {
    await portalsApi.post(`crm/event-reminders/${rid}/send-email`);
};

// Send WhatsApp invitation
export const sendWhatsappInvitation = async (rid: string): Promise<void> => {
    await portalsApi.post(`crm/event-reminders/${rid}/send-whatsapp`);
};
