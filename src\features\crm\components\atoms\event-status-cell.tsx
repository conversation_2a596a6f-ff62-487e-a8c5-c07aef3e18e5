import { EventStage, EventStageLabels } from "../../types/event";
import { Calendar, Clock, Check, Bell, XCircle } from "lucide-react";
import { Tooltip } from "antd";

type EventStatusCellProps = {
    stage: EventStage;
    updatedAt: string;
};

export default function EventStatusCell({ stage, updatedAt }: EventStatusCellProps) {
    // Format the date to "dd MMM yyyy"
    const formattedDate = new Date(updatedAt).toLocaleDateString("es-ES", {
        day: "numeric",
        month: "short",
        year: "numeric",
    });

    const getStageInfo = (stage: EventStage) => {
        switch (stage) {
            case EventStage.PLANNING:
                return {
                    icon: <Clock size={16} className="text-blue-600" strokeWidth={2} />,
                    bgColor: "bg-blue-50",
                    borderColor: "border-blue-200",
                    textColor: "text-blue-800",
                };
            case EventStage.LAUNCHED:
                return {
                    icon: <Bell size={16} className="text-green-600" strokeWidth={2} />,
                    bgColor: "bg-green-50",
                    borderColor: "border-green-200",
                    textColor: "text-green-800",
                };
            case EventStage.ENROLLMENT_CLOSED:
                return {
                    icon: (
                        <Check size={16} className="text-orange-600" strokeWidth={2} />
                    ),
                    bgColor: "bg-orange-50",
                    borderColor: "border-orange-200",
                    textColor: "text-orange-800",
                };
            case EventStage.FINISHED:
                return {
                    icon: (
                        <XCircle size={16} className="text-gray-600" strokeWidth={2} />
                    ),
                    bgColor: "bg-gray-50",
                    borderColor: "border-gray-200",
                    textColor: "text-gray-800",
                };
            default:
                return {
                    icon: <Clock size={16} className="text-gray-600" strokeWidth={2} />,
                    bgColor: "bg-gray-50",
                    borderColor: "border-gray-200",
                    textColor: "text-gray-800",
                };
        }
    };

    const stageInfo = getStageInfo(stage);

    return (
        <div className="flex flex-col gap-1.5">
            <Tooltip title={`Estado: ${EventStageLabels[stage]}`}>
                <div className="flex items-center">
                    <span
                        className={`text-xs font-medium px-3 py-1 rounded-full border
                            ${stageInfo.bgColor} ${stageInfo.textColor} ${stageInfo.borderColor}`}
                    >
                        <div className="flex items-center gap-1.5">
                            {stageInfo.icon}
                            <span>{EventStageLabels[stage]}</span>
                        </div>
                    </span>
                </div>
            </Tooltip>

            <div className="flex items-start gap-1.5">
                <Calendar size={14} className="text-gray-500 mt-1" strokeWidth={1.75} />
                <span className="text-xs font-medium text-gray-500">
                    Actualizado: {formattedDate}
                </span>
            </div>
        </div>
    );
}
