import { useCallback } from "react";
import { useSearchParams } from "react-router-dom";

export function useMutateSearchParams() {
    const [searchParams, setSearchParams] = useSearchParams();

    /**
     * Mutates multiple search params
     * @param newParams - Object with key-value pairs to update the search params
     */
    const mutateManySearchParams = useCallback((newParams: Record<string, string | null>) => {;
        Object.entries(newParams).forEach(([key, value]) => {
            if (value === null || value === undefined || value === "") {
                searchParams.delete(key);
            } else {
                searchParams.set(key, value);
            }
        });
        setSearchParams(searchParams);
    }, [searchParams, setSearchParams]);

    /**
     * Mutates a single search param
     * @param key - Key of the search param to update
     * @param value - Value of the search param to update
     */
    const mutateSearchParam = useCallback((key: string, value?: string | null) => { 
        if (value === null || value === undefined || value === "") {
            if (!searchParams.has(key)) return;
            searchParams.delete(key);
        } else {    
            searchParams.set(key, value);
        }
        setSearchParams(searchParams);
    }, [searchParams, setSearchParams]);

    return {
        mutateManySearchParams,
        mutateSearchParam,
        searchParams,
    };
}
