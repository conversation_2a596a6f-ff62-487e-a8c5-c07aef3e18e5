import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
    createLeadOrigin,
    getLeadOrigins,
    ListLeadOriginsParams,
} from "../services/portals/lead-origin";
import { AxiosError } from "axios";
import { LeadOriginCreate } from "../types/lead-origins";
import { App } from "antd";
import { useApiError } from "@hooks/use-api-error";

export type UseLeadOriginsQuery = Pick<ListLeadOriginsParams, "search">;

type UseLeadOriginsProps = {
    page?: number;
    pageSize?: number;
    query?: UseLeadOriginsQuery;
};

export const useLeadOrigins = ({
    page = 1,
    pageSize = 20,
    query,
}: UseLeadOriginsProps = {}) => {
    const { data, isLoading, isError, isFetching } = useQuery({
        queryKey: ["lead-origins", { page, pageSize, query }],
        queryFn: () =>
            getLeadOrigins({
                page,
                pageSize,
                search: query?.search,
            }),
        refetchOnWindowFocus: false,
    });

    const { count: COUNT, results: leadOrigins } = data || {
        count: 0,
        results: [],
    };

    return {
        isLoading: isLoading || isFetching,
        isError,
        leadOrigins,
        COUNT,
    };
};

type CreateLeadOriginProps = {
    onSuccess?: () => void;
    onError?: (error: AxiosError) => void;
};

export const useCreateLeadOrigin = ({
    onSuccess,
    onError,
}: CreateLeadOriginProps = {}) => {
    const { message } = App.useApp();
    const { handleError } = useApiError({
        title: "Error al crear la fuente de lead",
        genericMessage: "No se pudo crear la fuente de lead",
    });
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async (values: LeadOriginCreate) => createLeadOrigin(values),
        onSuccess: () => {
            message.success({
                content: "Fuente de lead creada exitosamente",
            });
            queryClient.invalidateQueries({
                queryKey: ["lead-origins"],
            });
            onSuccess?.();
        },
        onError: (error: AxiosError) => {
            handleError(error);
            onError?.(error);
        },
    });
};
