import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, SelectProps } from "antd";
import { useBenefits } from "../../hooks/use-benefit";
import { Plus } from "lucide-react";
import CreateBenefitForm from "../organisms/create-benefit-form";
import { useState } from "react";

interface SelectOrderBenefitsProps extends Omit<SelectProps, "options"> {
    value?: string; // Add value prop for controlled component
    onChange?: (value: string) => void; // Add onChange prop
}
export default function SelectOrderBenefits({
    value,
    onChange,
    ...restProps
}: SelectOrderBenefitsProps) {
    const [modalOpen, setModalOpen] = useState(false);
    const handleCloseModal = () => {
        setModalOpen(false);
    };
    const { benefits, isLoading } = useBenefits();
    const options = benefits.map((benefit) => ({
        value: benefit.bid,
        label: benefit.name,
    }));
    return (
        <>
            <Modal
                title={
                    <div className="text-lg font-semibold text-center">
                        <PERSON><PERSON><PERSON> Beneficio
                    </div>
                }
                footer={false}
                open={modalOpen}
                centered
                onCancel={() => {
                    setModalOpen(false);
                }}
            >
                <CreateBenefitForm handleCloseModal={handleCloseModal} />
            </Modal>
            <Select
                {...restProps}
                value={value}
                onChange={onChange}
                options={options}
                mode="multiple"
                allowClear
                placeholder="Selecciona los beneficios"
                showSearch
                loading={isLoading}
                dropdownRender={(menu) => (
                    <>
                        {menu}
                        <Divider className="my-1" />
                        <div className="flex justify-between items-center px-2">
                            <p className="text-sm text-gray-700 font-medium">
                                ¿No encuentras el Beneficio que buscas?
                            </p>
                            <Button
                                size="small"
                                type="primary"
                                icon={<Plus size={12} />}
                                onClick={() => setModalOpen(true)}
                            >
                                Agregar
                            </Button>
                        </div>
                    </>
                )}
            />
        </>
    );
}
