{
  "compilerOptions": {
      "target": "ES2020",
      "useDefineForClassFields": true,
      "lib": ["ES2020", "DOM", "DOM.Iterable"],
      "module": "ESNext",
      "skipLibCheck": true,

      /* Bundler mode */
      "moduleResolution": "bundler",
      "allowImportingTsExtensions": true,
      "resolveJsonModule": true,
      "isolatedModules": true,
      "noEmit": true,
      "jsx": "react-jsx",
      "paths": {
          "@components/*": ["./src/components/*"],
          "@pages/*": ["./src/pages/*"],
          "@layouts/*": ["./src/layouts/*"],
          "@assets/*": ["./src/assets/*"],
          "@routes": ["./src/routes"],
          "@services/*": ["./src/services/*"],
          "@context/*": ["./src/context/*"],
          "@redux/*": ["./src/redux/*"],
          "@hooks/*": ["./src/hooks/*"],
          "@lib/*": ["./src/lib/*"],
          "@myTypes/*": ["./src/@types/*"],
          "@store/*": ["./src/store/*"],
          "@/features/*": ["./src/features/*"],
          "@/core/*": ["./src/core/*"]
      },

      /* Linting */
      "strict": true,
      "noUnusedLocals": true,
      "noUnusedParameters": true,
      "noFallthroughCasesInSwitch": true
  },
  "include": ["src"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
