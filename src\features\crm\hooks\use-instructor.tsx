import { getInstructors } from "@services/portals/cms/instructor";
import { useQuery } from "@tanstack/react-query";

export const useInstructors = () => {
    const { data, isLoading, isError } = useQuery({
        queryKey: ["instructors"],
        queryFn: () => getInstructors({}),
        refetchOnWindowFocus: false,
    });

    const { count, results: instructors } = data || {
        count: 0,
        results: [],
    };

    return {
        isLoading,
        isError,
        instructors,
        count,
    };
};
