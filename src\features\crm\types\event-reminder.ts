import { EventScheduleEnrollmentUser } from "./event-schedule-enrollment";

export enum EventReminderStatus {
    PENDING = "PENDING",
    SENT = "SENT",
    FAILED = "FAILED",
    RETRYING = "RETRYING",
    CANCELLED = "CANCELLED",
}

type AuditBaseType = {
    createdAt: string;
    updatedAt: string;
};

export type EventReminderEnrollment = {
    id: number;
    fullName: string;
    email: string;
    phoneNumber: string;
    user: EventScheduleEnrollmentUser | null;
};

export type EventReminderWhatsappTemplate = {
    tid: string;
    name: string;
    extReference: string;
};

export type EventReminder = {
    rid: string;
    enrollment: EventReminderEnrollment;
    whatsappTemplate: EventReminderWhatsappTemplate;
    scheduledDatetimeWhatsapp: string;
    scheduledDatetimeEmail: string;
    statusWhatsapp: EventReminderStatus;
    statusEmail: EventReminderStatus;
    sentAtWhatsapp: string | null;
    sentAtEmail: string | null;
    lastErrorWhatsapp: string | null;
    lastErrorEmail: string | null;
    retryCountWhatsapp: number;
    retryCountEmail: number;
} & AuditBaseType;

export type ListEventRemindersQueryParams = Partial<{
    page: number;
    pageSize: number;
    user: string;
    statusWhatsapp: EventReminderStatus;
    statusEmail: EventReminderStatus;
    eventSchedule: string;
    whatsappTemplate: string;
    hasFailedInvitations: boolean;
    hasPendingInvitations: boolean;
    hasSentInvitations: boolean;
}>;

type EventReminderChannelMetrics = {
    totalPending: number;
    totalSent: number;
    totalFailed: number;
};

export type EventReminderMetricsQueryParams = Partial<{
    eventSchedule: string; // event schedule ID
}>;

export type EventReminderMetrics = {
    totalReminders: number;
    totalPending: number;
    totalSent: number;
    totalFailed: number;
    whatsapp: EventReminderChannelMetrics;
    email: EventReminderChannelMetrics;
};

// Constants
export const DEFAULT_PAGE = 1;
export const DEFAULT_PAGE_SIZE = 10;
