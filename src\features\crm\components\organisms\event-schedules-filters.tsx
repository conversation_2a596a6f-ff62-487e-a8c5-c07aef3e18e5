import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Drawer, Radio, Select } from "antd";
import { EventStageLabels } from "@/features/crm/types/event";
import { useSearchParams } from "react-router-dom";
import { useCallback, useEffect, useState } from "react";
import dayjs, { Dayjs } from "dayjs";
import "dayjs/locale/es";
import SelectPartnership from "../molecules/select-partnership";
import SelectInstructor from "../molecules/select-instructor";
import SelectEducationalInstitution from "../molecules/select-educational-institution";

dayjs.locale("es");

// Opciones para filtro de fecha
const dateFilterOptions = [
    { value: "createdAt", label: "Fecha de creación" },
    { value: "scheduledDate", label: "Fecha programada" },
];

interface EventSchedulesFiltersProps {
    isOpen: boolean;
    onClose: () => void;
    onClearFilters?: () => void;
}

export default function EventSchedulesFilters({
    isOpen,
    onClose,
    onClearFilters,
}: EventSchedulesFiltersProps) {
    const [searchParams, setSearchParams] = useSearchParams();

    // Local filter states
    const [selectedStages, setSelectedStages] = useState<string[]>([]);
    const [selectedIsGeneral, setSelectedIsGeneral] = useState<string>("all");
    const [selectedPartnerships, setSelectedPartnerships] = useState<string[]>([]);
    const [partnershipFilterType, setPartnershipFilterType] = useState<
        "specific" | "institution"
    >("specific");
    const [selectedInstructor, setSelectedInstructor] = useState<string | null>(null);
    const [dateRange, setDateRange] = useState<[Dayjs, Dayjs] | null>(null);
    const [selectedDateFilters, setSelectedDateFilters] =
        useState<string>("scheduledDate");

    // Initialize filters from URL
    useEffect(() => {
        const stages = searchParams.get("stage");
        const isGeneral = searchParams.get("isGeneral");
        const partnerships = searchParams.get("partnerships");
        const partnershipFilterTypeParam = searchParams.get("partnershipFilterType");
        const instructor = searchParams.get("instructor");
        const startDate = searchParams.get("startDate");
        const endDate = searchParams.get("endDate");
        const dateFilters = searchParams.get("filterDateBy");

        if (stages) {
            setSelectedStages(stages.split(","));
        } else {
            setSelectedStages([]);
        }

        if (isGeneral === "true") {
            setSelectedIsGeneral("general");
        } else if (isGeneral === "false") {
            setSelectedIsGeneral("specific");
        } else {
            setSelectedIsGeneral("all");
        }

        if (partnerships) {
            setSelectedPartnerships(partnerships.split(","));
        } else {
            setSelectedPartnerships([]);
        }

        if (partnershipFilterTypeParam === "institution") {
            setPartnershipFilterType("institution");
        } else {
            setPartnershipFilterType("specific");
        }

        if (instructor) {
            setSelectedInstructor(instructor);
        } else {
            setSelectedInstructor(null);
        }

        if (startDate && endDate) {
            setDateRange([dayjs(startDate), dayjs(endDate)]);
        } else {
            setDateRange(null);
        }

        if (dateFilters) {
            setSelectedDateFilters(dateFilters);
        } else {
            setSelectedDateFilters("scheduledDate");
        }
    }, [searchParams]);

    const handleApplyFilters = useCallback(() => {
        setSearchParams((prev) => {
            // Clear previous filters
            prev.delete("stage");
            prev.delete("isGeneral");
            prev.delete("partnerships");
            prev.delete("partnershipFilterType");
            prev.delete("instructor");
            prev.delete("startDate");
            prev.delete("endDate");
            prev.delete("filterDateBy");

            // Apply stage filters
            if (selectedStages.length > 0) {
                prev.set("stage", selectedStages.join(","));
            }

            // Apply isGeneral filters
            if (selectedIsGeneral === "general") {
                prev.set("isGeneral", "true");
            } else if (selectedIsGeneral === "specific") {
                prev.set("isGeneral", "false");
            }

            // Apply partnerships filters
            if (selectedPartnerships.length > 0) {
                prev.set("partnerships", selectedPartnerships.join(","));
                prev.set("partnershipFilterType", partnershipFilterType);
            }

            // Apply instructor filters
            if (selectedInstructor) {
                prev.set("instructor", selectedInstructor);
            }

            // Apply date range filters
            if (dateRange && dateRange[0] && dateRange[1]) {
                // Apply date filter type
                if (selectedDateFilters.length > 0) {
                    prev.set("filterDateBy", selectedDateFilters);
                }

                prev.set("startDate", dateRange[0].format("YYYY-MM-DD"));
                prev.set("endDate", dateRange[1].format("YYYY-MM-DD"));
            }

            // Reset to page 1
            prev.set("page", "1");

            return prev;
        });
        onClose();
    }, [
        selectedStages,
        selectedIsGeneral,
        selectedPartnerships,
        partnershipFilterType,
        selectedInstructor,
        dateRange,
        selectedDateFilters,
        setSearchParams,
        onClose,
    ]);

    const handleClearFilters = useCallback(() => {
        setSelectedStages([]);
        setSelectedIsGeneral("all");
        setSelectedPartnerships([]);
        setPartnershipFilterType("specific");
        setSelectedInstructor(null);
        setDateRange(null);
        setSelectedDateFilters("scheduledDate");

        setSearchParams((prev) => {
            prev.delete("stage");
            prev.delete("isGeneral");
            prev.delete("partnerships");
            prev.delete("partnershipFilterType");
            prev.delete("instructor");
            prev.delete("startDate");
            prev.delete("endDate");
            prev.delete("filterDateBy");
            prev.set("page", "1");
            return prev;
        });

        onClearFilters?.();
        onClose();
    }, [setSearchParams, onClearFilters, onClose]);

    return (
        <Drawer
            title="Aplicar filtros"
            placement="right"
            closable={true}
            onClose={onClose}
            open={isOpen}
            width={480}
        >
            <div className="space-y-4">
                <div>
                    <h4 className="font-medium mb-2">Etapa</h4>
                    <Select
                        mode="multiple"
                        style={{ width: "100%" }}
                        placeholder="Seleccionar etapas"
                        value={selectedStages}
                        onChange={setSelectedStages}
                        allowClear
                        showSearch={false}
                    >
                        {Object.entries(EventStageLabels).map(([value, label]) => (
                            <Select.Option key={value} value={value}>
                                {label}
                            </Select.Option>
                        ))}
                    </Select>
                </div>

                <div>
                    <h4 className="font-medium mb-2">Tipo de evento</h4>
                    <Radio.Group
                        value={selectedIsGeneral}
                        onChange={(e) => setSelectedIsGeneral(e.target.value)}
                        className="w-full"
                    >
                        <div className="space-y-2">
                            <Radio value="all">Todos</Radio>
                            <Radio value="general">General</Radio>
                            <Radio value="specific">Específico</Radio>
                        </div>
                    </Radio.Group>
                </div>

                <div>
                    <h4 className="font-medium mb-2">Alianza</h4>
                    <div className="space-y-3">
                        <Radio.Group
                            value={partnershipFilterType}
                            onChange={(e) => {
                                setPartnershipFilterType(e.target.value);
                                // Clear selected partnerships when changing filter type
                                setSelectedPartnerships([]);
                            }}
                            className="w-full"
                        >
                            <div className="space-y-2">
                                <Radio value="specific">Específico</Radio>
                                <Radio value="institution">Por Institución</Radio>
                            </div>
                        </Radio.Group>

                        {partnershipFilterType === "specific" ? (
                            <SelectPartnership
                                mode="multiple"
                                className="w-full"
                                placeholder="Seleccionar alianzas"
                                value={selectedPartnerships}
                                onChange={(value) => {
                                    if (Array.isArray(value)) {
                                        setSelectedPartnerships(value);
                                    } else if (value) {
                                        setSelectedPartnerships([value]);
                                    } else {
                                        setSelectedPartnerships([]);
                                    }
                                }}
                            />
                        ) : (
                            <SelectEducationalInstitution
                                mode="multiple"
                                className="w-full"
                                placeholder="Seleccionar instituciones"
                                value={selectedPartnerships}
                                onChange={(value) => {
                                    if (Array.isArray(value)) {
                                        setSelectedPartnerships(value);
                                    } else if (value) {
                                        setSelectedPartnerships([value]);
                                    } else {
                                        setSelectedPartnerships([]);
                                    }
                                }}
                            />
                        )}
                    </div>
                </div>

                <div>
                    <h4 className="font-medium mb-2">Instructor</h4>
                    <SelectInstructor
                        className="w-full"
                        placeholder="Seleccionar instructor"
                        value={selectedInstructor || undefined}
                        onChange={(value) => {
                            setSelectedInstructor(value);
                        }}
                    />
                </div>

                <div>
                    <h4 className="font-medium mb-2">Filtrar por fecha</h4>
                    <div className="space-y-3">
                        <div>
                            <label className="text-sm text-gray-600 mb-1 block">
                                Tipo de fecha
                            </label>
                            <Select
                                style={{ width: "100%" }}
                                placeholder="Seleccionar tipo de fecha"
                                value={selectedDateFilters}
                                onChange={setSelectedDateFilters}
                                options={dateFilterOptions}
                            />
                        </div>
                        <div>
                            <label className="text-sm text-gray-600 mb-1 block">
                                Rango de fechas
                            </label>
                            <DatePicker.RangePicker
                                style={{ width: "100%" }}
                                placeholder={["Fecha inicio", "Fecha fin"]}
                                value={dateRange}
                                onChange={(dates) =>
                                    setDateRange(dates as [Dayjs, Dayjs] | null)
                                }
                                presets={[
                                    {
                                        label: "Hoy",
                                        value: [
                                            dayjs().startOf("day"),
                                            dayjs().endOf("day"),
                                        ],
                                    },
                                    {
                                        label: "Esta semana",
                                        value: [
                                            dayjs().startOf("week"),
                                            dayjs().endOf("week"),
                                        ],
                                    },
                                    {
                                        label: "Este mes",
                                        value: [
                                            dayjs().startOf("month"),
                                            dayjs().endOf("month"),
                                        ],
                                    },
                                    {
                                        label: "Último mes",
                                        value: [
                                            dayjs()
                                                .subtract(1, "month")
                                                .startOf("month"),
                                            dayjs().subtract(1, "month").endOf("month"),
                                        ],
                                    },
                                    {
                                        label: "Este año",
                                        value: [
                                            dayjs().startOf("year"),
                                            dayjs().endOf("year"),
                                        ],
                                    },
                                ]}
                            />
                        </div>
                    </div>
                </div>

                <div className="pt-4 space-y-2">
                    <Button type="primary" block onClick={handleApplyFilters}>
                        Aplicar filtros
                    </Button>
                    <Button block onClick={handleClearFilters}>
                        Limpiar filtros
                    </Button>
                </div>
            </div>
        </Drawer>
    );
}
