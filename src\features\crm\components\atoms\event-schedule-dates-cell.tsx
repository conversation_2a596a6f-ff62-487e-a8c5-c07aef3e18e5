import dayjs from "dayjs";
import duration from "dayjs/plugin/duration";
import { Calendar, Clock, Timer } from "lucide-react";

// Extend dayjs with duration plugin
dayjs.extend(duration);

export default function EventScheduleDatesCell({
    startDate,
    endDate,
}: {
    startDate: string;
    endDate: string;
}) {
    // Parse the dates
    const start = dayjs(startDate);
    const end = dayjs(endDate);

    // Calculate duration in hours and minutes
    const durationMs = end.diff(start);
    const hours = Math.floor(dayjs.duration(durationMs).asHours());
    const minutes = Math.floor(dayjs.duration(durationMs).asMinutes()) % 60;

    // Format the duration string
    const durationText = `${hours}h ${minutes > 0 ? `${minutes}m` : ""}`;

    return (
        <div className="flex flex-col space-y-2 text-sm">
            <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-gray-500" />
                <span className="font-medium">{start.format("DD/MM/YYYY")}</span>
            </div>

            <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-blue-500" />
                <span>
                    <span className="font-semibold">Inicio: </span>
                    {start.format("HH:mm")}
                </span>
                <span className="mx-1">•</span>
                <span>
                    <span className="font-semibold">Fin: </span>
                    {end.format("HH:mm")}
                </span>
            </div>

            <div className="flex items-center gap-2 text-gray-600">
                <Timer className="h-4 w-4 text-green-500" />
                <span>
                    <span className="font-semibold">Duración: </span>
                    {durationText}
                </span>
            </div>
        </div>
    );
}
