import { Form, DatePicker, InputNumber, Switch, Tooltip } from "antd";
import FormLabel from "@/features/crm/components/atoms/FormLabel";
import SelectTemplate from "@/features/crm/components/molecules/select-template";
import { Info } from "lucide-react";
import { FormInstance } from "antd";
import { RetrieveEventSchedule } from "../../types/event-schedule";

interface InvitationFormProps {
    eventSchedule: RetrieveEventSchedule;
    form: FormInstance;
    className?: string;
}

export default function InvitationForm({
    form,
    className,
    eventSchedule,
}: InvitationFormProps) {
    const isWhatsappActive = Form.useWatch("isWhatsappActive", form);
    const emailsReminderAuto = Form.useWatch("emailsReminderAuto", form) === true;

    return (
        <div className={`p-2 flex flex-col gap-4 w-full ${className}`}>
            <section className="flex-1 bg-white-full">
                <div>
                    <div className="flex flex-col">
                        <Form.Item
                            name="emailsReminderAuto"
                            label={
                                <FormLabel>
                                    Enviar invitación por email inmediata
                                </FormLabel>
                            }
                            valuePropName="checked"
                        >
                            <Switch />
                        </Form.Item>

                        {!emailsReminderAuto && (
                            <Form.Item
                                name="scheduledDatetimeEmail"
                                label={
                                    <div className="flex items-center gap-2">
                                        <FormLabel>
                                            Fecha y hora de envío por Email
                                        </FormLabel>
                                        <Tooltip title="Fecha y hora programada para enviar la invitación por correo electrónico">
                                            <Info size={16} />
                                        </Tooltip>
                                    </div>
                                }
                                rules={[
                                    {
                                        required: !emailsReminderAuto,
                                        message:
                                            "Por favor, seleccione la fecha para el envío de email",
                                    },
                                ]}
                            >
                                <DatePicker
                                    showTime
                                    className="w-full"
                                    placeholder="Seleccionar fecha y hora"
                                    format="DD/MM/YYYY HH:mm"
                                />
                            </Form.Item>
                        )}
                    </div>

                    <div className="flex flex-row items-center gap-2">
                        <Form.Item
                            name="isWhatsappActive"
                            label={
                                <FormLabel>Enviar invitación por WhatsApp</FormLabel>
                            }
                            valuePropName="checked"
                        >
                            <Switch />
                        </Form.Item>
                    </div>

                    {isWhatsappActive && (
                        <div className="space-y-4">
                            <Form.Item
                                name="whatsappTemplate"
                                label={
                                    <div className="flex items-center gap-2">
                                        <FormLabel>Plantilla de WhatsApp</FormLabel>
                                        <Tooltip title="Mensaje de invitación que se enviará en la fecha y hora programada">
                                            <Info size={16} />
                                        </Tooltip>
                                    </div>
                                }
                                rules={[
                                    {
                                        required: isWhatsappActive,
                                        message:
                                            "Por favor, seleccione una plantilla de WhatsApp",
                                    },
                                ]}
                            >
                                <SelectTemplate
                                    placeholder="Seleccionar plantilla"
                                    className="w-full"
                                    selectedTemplate={eventSchedule.whatsappTemplate}
                                />
                            </Form.Item>

                            <Form.Item
                                name="scheduledDatetimeWhatsapp"
                                label={<FormLabel>Fecha programada de envío</FormLabel>}
                                rules={[
                                    {
                                        required: isWhatsappActive,
                                        message:
                                            "Por favor, seleccione la fecha para el envío de WhatsApp",
                                    },
                                ]}
                            >
                                <DatePicker
                                    showTime
                                    className="w-full"
                                    placeholder="Seleccionar fecha y hora"
                                    format="DD/MM/YYYY HH:mm"
                                />
                            </Form.Item>

                            <div>
                                <FormLabel>
                                    Rango de retraso para WhatsApp (Segundos)
                                </FormLabel>
                                <div className="flex gap-4 mt-2">
                                    <Form.Item
                                        name={["whatsappDelayRange", 0]}
                                        rules={[
                                            {
                                                required: isWhatsappActive,
                                                message: "Valor mínimo requerido",
                                            },
                                        ]}
                                        className="flex-1 mb-0"
                                    >
                                        <InputNumber
                                            placeholder="Mínimo"
                                            min={0}
                                            max={9999}
                                            className="w-full"
                                            addonAfter="seg"
                                            controls={false}
                                            changeOnWheel={false}
                                        />
                                    </Form.Item>
                                    <Form.Item
                                        name={["whatsappDelayRange", 1]}
                                        rules={[
                                            {
                                                required: isWhatsappActive,
                                                message: "Valor máximo requerido",
                                            },
                                        ]}
                                        className="flex-1 mb-0"
                                    >
                                        <InputNumber
                                            placeholder="Máximo"
                                            min={0}
                                            max={9999}
                                            className="w-full"
                                            addonAfter="seg"
                                            controls={false}
                                            changeOnWheel={false}
                                        />
                                    </Form.Item>
                                </div>
                                <p className="text-xs text-gray-500 mt-1">
                                    Define el rango de segundos de retraso aleatorio
                                    para el envío de WhatsApp
                                </p>
                            </div>
                        </div>
                    )}
                </div>
            </section>
        </div>
    );
}
