import { useNavigate, useParams } from "react-router-dom";
import { Button, Form, message, Typography } from "antd";
import { useMutation, useQuery } from "@tanstack/react-query";
import CmsLayout from "@layouts/cms/CmsLayout";
import WelcomeBar from "@components/shared/molecules/WelcomeBar";
import Spinner from "@components/shared/atoms/Spinner";
import {
    retrieveBlogCategory,
    updateBlogCategory,
} from "@services/portals/cms/blogs/category";
import { BlogCategory, UpdateBlogCategoryBody } from "@myTypes/blog";
import { onErrorMessage, onSuccessMessage } from "@lib/message";
import BlogCategoryForm from "@components/cms/molecules/BlogCategoryForm";
import { detectChanges } from "@lib/form-helpers";

const { Title } = Typography;

export default function BlogCategoryEditPage() {
    const [messageApi, messageContextHolder] = message.useMessage();
    const { id } = useParams<{ id: string }>();
    const navigate = useNavigate();
    const [form] = Form.useForm<UpdateBlogCategoryBody>();

    const { data, isLoading } = useQuery({
        queryKey: ["blogCategory", id],
        queryFn: () => retrieveBlogCategory(id!),
        enabled: !!id,
    });

    const updateMutation = useMutation({
        mutationFn: (values: UpdateBlogCategoryBody) => updateBlogCategory(id!, values),
        onSuccess: () => {
            onSuccessMessage("Categoría actualizada correctamente", messageApi);
        },
        onError: () => {
            onErrorMessage("Error al actualizar la categoría", messageApi);
        },
    });

    const handleFormFinish = (values: UpdateBlogCategoryBody) => {
        const fieldsToCompare: (keyof BlogCategory)[] = ["name", "slug", "description"];

        if (detectChanges({ data, values, fieldsToCompare })) {
            updateMutation.mutate(values);
        }
    };

    return (
        <>
            {messageContextHolder}
            <CmsLayout>
                <div className="w-full h-full space-y-5">
                    <WelcomeBar helperText="Visualiza & Edita los campos necesarios" />

                    <div className="bg-white-full p-5 rounded-lg shadow-sm">
                        <div className="flex justify-between items-center mb-5">
                            <Title level={4} className="m-0">
                                Editar categoría
                            </Title>
                        </div>

                        {isLoading ? (
                            <div className="flex justify-center items-center h-64">
                                <Spinner />
                            </div>
                        ) : (
                            <>
                                <BlogCategoryForm
                                    form={form}
                                    initialValues={data || undefined}
                                    onFinish={handleFormFinish}
                                />
                                <div className="flex justify-end gap-3 mt-4">
                                    <Button
                                        size="large"
                                        onClick={() => navigate("/cms/blog/categories")}
                                    >
                                        Cancelar
                                    </Button>
                                    <Button
                                        type="primary"
                                        size="large"
                                        onClick={() => form.submit()}
                                        loading={updateMutation.isPending}
                                    >
                                        Guardar
                                    </Button>
                                </div>
                            </>
                        )}
                    </div>
                </div>
            </CmsLayout>
        </>
    );
}
