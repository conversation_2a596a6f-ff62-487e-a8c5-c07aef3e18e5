import { ContactListItem } from "../contact";

export type DashboardContactQueryParams = Partial<{
    active: boolean;
    search: string;
    createdAtBefore: string;
    createdAtAfter: string;
    country: string;
    ocupation: string;
    forceRefresh: boolean;
}>;

export type ContactStatusItem = {
    name: string;
    value: number;
};

export type ContactsByMonthItem = {
    name: string;
    contacts: number;
    totalAccumulated: number;
};

export type ContactsByCountryItem = {
    country: string;
    contacts: number;
};

export type ContactsByOccupationItem = {
    name: string;
    value: number;
};

export type StudentsByMajorItem = {
    name: string;
    value: number;
};

export type StudentsByTermItem = {
    name: string;
    value: number;
};

export type StudentsByUniversityItem = {
    name: string;
    value: number;
};

export type EmployeesByCompanyItem = {
    name: string;
    value: number;
};

/**
 * Rate types
 */
export type DashboardContactStatsRate = {
    value: number;
    percentage: number;
    tendency: "up" | "down" | "flat";
};

export type DashboardContactStats = {
    totalContacts: number;
    newThisMonth: DashboardContactStatsRate;
    activePercentage: number;
    conversionRate: DashboardContactStatsRate;
    contactsByStatus: ContactStatusItem[];
};

type DashboardContactsFilterOptions = {
    countries: string[];
};

export type DashboardContactData = {
    stats: DashboardContactStats;
    contactsByMonth: ContactsByMonthItem[];
    contactsByCountry: ContactsByCountryItem[];
    contactsByOccupation: ContactsByOccupationItem[];
    studentsByMajor: StudentsByMajorItem[];
    studentsByTerm: StudentsByTermItem[];
    studentsByUniversity: StudentsByUniversityItem[];
    employeesByCompany: EmployeesByCompanyItem[];
    recentActivityContacts: ContactListItem[];
    filterOptions: DashboardContactsFilterOptions;
};
