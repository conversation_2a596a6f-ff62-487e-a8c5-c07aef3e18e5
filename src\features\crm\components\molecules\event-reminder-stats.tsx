import { <PERSON>, <PERSON>, Row, Statistic, Tooltip } from "antd";
import {
    Mail,
    MessageCircle,
    Clock,
    CheckCircle,
    XCircle,
    Send,
    AlertTriangle,
} from "lucide-react";
import { EventReminderMetrics } from "../../types/event-reminder";

interface EventReminderStatsProps {
    metrics: EventReminderMetrics;
}

export default function EventReminderStats({ metrics }: EventReminderStatsProps) {
    const { totalReminders, totalPending, totalSent, totalFailed, whatsapp, email } =
        metrics;

    // Calculate percentages
    const sentPercentage =
        totalReminders > 0 ? ((totalSent / totalReminders) * 100).toFixed(1) : "0";
    const failedPercentage =
        totalReminders > 0 ? ((totalFailed / totalReminders) * 100).toFixed(1) : "0";

    return (
        <div className=" h-fit  rounded-lg shadow-sm">
            <Row gutter={[16, 16]}>
                <Col xs={24} sm={4} lg={4}>
                    <Card className="text-center">
                        <Statistic
                            title="Total Recordatorios"
                            value={totalReminders}
                            prefix={<Send className="text-blue-500" size={20} />}
                            valueStyle={{ color: "#1890ff", fontSize: "24px" }}
                        />
                    </Card>
                </Col>

                <Col xs={24} sm={4} lg={4}>
                    <Card className="text-center">
                        <Statistic
                            title="Enviados"
                            value={totalSent}
                            prefix={
                                <CheckCircle className="text-green-500" size={20} />
                            }
                            suffix={`(${sentPercentage}%)`}
                            valueStyle={{ color: "#52c41a", fontSize: "20px" }}
                        />
                    </Card>
                </Col>

                <Col xs={24} sm={4} lg={4}>
                    <Card className="text-center">
                        <Statistic
                            title="Pendientes"
                            value={totalPending}
                            prefix={<Clock className="text-orange-500" size={20} />}
                            valueStyle={{ color: "#fa8c16", fontSize: "20px" }}
                        />
                    </Card>
                </Col>

                <Col xs={24} sm={4} lg={4}>
                    <Card className="text-center">
                        <Statistic
                            title="Fallidos"
                            value={totalFailed}
                            prefix={<XCircle className="text-red-500" size={20} />}
                            suffix={`(${failedPercentage}%)`}
                            valueStyle={{ color: "#f5222d", fontSize: "20px" }}
                        />
                    </Card>
                </Col>

                {/* Channel-specific metrics */}
                <Col xs={24} sm={4} lg={4}>
                    <Tooltip
                        title={
                            <div>
                                <div>Enviados: {email.totalSent}</div>
                                <div>Pendientes: {email.totalPending}</div>
                                <div>Fallidos: {email.totalFailed}</div>
                            </div>
                        }
                    >
                        <Card className="text-center cursor-help">
                            <Statistic
                                title="Email"
                                value={email.totalSent}
                                prefix={<Mail className="text-blue-600" size={20} />}
                                suffix={
                                    email.totalFailed > 0 && (
                                        <AlertTriangle
                                            className="text-red-500 ml-1"
                                            size={16}
                                        />
                                    )
                                }
                                valueStyle={{ color: "#1890ff", fontSize: "18px" }}
                            />
                        </Card>
                    </Tooltip>
                </Col>

                <Col xs={24} sm={4} lg={4}>
                    <Tooltip
                        title={
                            <div>
                                <div>Enviados: {whatsapp.totalSent}</div>
                                <div>Pendientes: {whatsapp.totalPending}</div>
                                <div>Fallidos: {whatsapp.totalFailed}</div>
                            </div>
                        }
                    >
                        <Card className="text-center cursor-help">
                            <Statistic
                                title="WhatsApp"
                                value={whatsapp.totalSent}
                                prefix={
                                    <MessageCircle
                                        className="text-green-600"
                                        size={20}
                                    />
                                }
                                suffix={
                                    whatsapp.totalFailed > 0 && (
                                        <AlertTriangle
                                            className="text-red-500 ml-1"
                                            size={16}
                                        />
                                    )
                                }
                                valueStyle={{ color: "#25d366", fontSize: "18px" }}
                            />
                        </Card>
                    </Tooltip>
                </Col>
            </Row>
        </div>
    );
}
