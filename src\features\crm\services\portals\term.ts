import { PaginatedResponse } from "@myTypes/base";
import { portalsApi } from "@services/portals";
import { Term } from "@/features/crm/types/term";

export type ListTermsQueryParams = {
    page?: number;
    pageSize?: number;
    search?: string;
    ordering?: string;
};

const DEFAULT_PAGE = 1;
const DEFAULT_PAGE_SIZE = 100;

export const getTerms = async (
    query: ListTermsQueryParams = {},
): Promise<PaginatedResponse<Term>> => {
    const params: Record<string, string | number | undefined> = {
        page: DEFAULT_PAGE,
        pageSize: DEFAULT_PAGE_SIZE,
        ...query,
    };
    params.ordering = query.ordering ?? "name";
    if (query.search) params.search = query.search;
    const response = await portalsApi.get("crm/terms", { params });
    return response.data;
};

export const retrieveTerm = async (tid: string): Promise<Term> => {
    const response = await portalsApi.get(`crm/terms/${tid}`);
    return response.data;
};

export const createTerm = async (name: string): Promise<Term> => {
    const response = await portalsApi.post("crm/terms", { name });
    return response.data;
};

export const updateTerm = async (
    tid: string,
    payload: { name: string },
): Promise<Term> => {
    const response = await portalsApi.patch(`crm/terms/${tid}`, payload);
    return response.data;
};

export const deleteTerm = async (tid: string): Promise<void> => {
    await portalsApi.delete(`crm/terms/${tid}`);
};
