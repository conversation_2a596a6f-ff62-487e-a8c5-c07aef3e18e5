type AuditBaseType = {
    createdAt: string;
    updatedAt: string;
};

export type Institution = {
    eiid: string;
    name: string;
};

export type PartnershipListItem = {
    pid: string;
    name: string;
    description: string;
} & AuditBaseType;

export type PartnershipRetrieve = {
    key: string;
    pid: string;
    name: string;
    description?: string;
    institution: Institution;
} & AuditBaseType;

export type PartnershipCreate = {
    name: string;
    description?: string;
    institution: string;
};

export type PartnershipCreateForm = PartnershipCreate;
