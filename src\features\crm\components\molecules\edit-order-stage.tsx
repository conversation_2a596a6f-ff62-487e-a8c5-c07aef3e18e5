import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>confirm } from "antd";
import {
    OrderStage,
    OrderPartialUpdate,
    OrderStageLabels,
    RetrieveOrder,
} from "../../types/order";
import { Check, CornerDownRight } from "lucide-react";
import { useMutation } from "@tanstack/react-query";
import { partialUpdateOrder } from "../../services/portals/order";
import queryClient from "@lib/queryClient";
import { type AxiosError } from "axios";
import { useApiError } from "@hooks/use-api-error";

type EditOrderStageProps = {
    stage: OrderStage;
    currentStage: OrderStage;
    order: RetrieveOrder;
};

const EditOrderStage = ({ stage, currentStage, order }: EditOrderStageProps) => {
    const { message } = App.useApp();
    const isCurrentStage = stage === currentStage;
    const isCompleted = isStageCompleted(stage, currentStage);

    const { handleError } = useApiError({
        title: "Error al actualizar el estado",
        genericMessage: "No se pudo actualizar el estado de la orden",
    });

    const { mutate: updateOrderStageMutate, isPending } = useMutation({
        mutationFn: (payload: OrderPartialUpdate) =>
            partialUpdateOrder(order.oid, payload),
        onSuccess: () => {
            message.success({
                content: `Orden movida a estado: ${OrderStageLabels[stage]}`,
                duration: 2,
            });
            queryClient.invalidateQueries({
                queryKey: ["order", order.oid],
            });
        },
        onError: (error: AxiosError) => {
            handleError(error);
        },
    });

    const handleUpdateStage = () => {
        const now = new Date().toISOString();
        const payload: OrderPartialUpdate = { stage: stage, owner: order.owner.uid };

        if (stage === OrderStage.PROSPECT) {
            payload.prospectAt = now;
        } else if (stage === OrderStage.INTERESTED) {
            payload.interestedAt = now;
        } else if (stage === OrderStage.TO_PAY) {
            payload.toPayAt = now;
        } else if (stage === OrderStage.SOLD) {
            payload.soldAt = now;
        } else if (stage === OrderStage.LOST) {
            payload.lostAt = now;
        }

        updateOrderStageMutate(payload);
    };

    if (isCurrentStage) {
        return (
            <span className="flex items-center font-medium text-blue-full">
                <Check size={16} className="mr-1" /> Actual
            </span>
        );
    }

    if (isCompleted) {
        return (
            <span className="flex items-center text-green-600 font-medium">
                <Check size={16} className="mr-1" /> Completado
            </span>
        );
    }

    return (
        <Popconfirm
            title={`Cambiar estado de la orden`}
            description={`¿Estás seguro de cambiar la orden al estado ${OrderStageLabels[stage]}?`}
            onConfirm={handleUpdateStage}
            okText="Sí"
            cancelText="No"
            okButtonProps={{ loading: isPending }}
        >
            <Button
                type="text"
                size="small"
                className="text-gray-500 hover:text-blue-full"
                icon={<CornerDownRight size={14} className="mr-1" />}
                data-stage={stage}
            >
                Mover aquí
            </Button>
        </Popconfirm>
    );
};

// Helper function to determine if a stage is completed based on the current stage
function isStageCompleted(stage: OrderStage, currentStage: OrderStage): boolean {
    const stageOrder: OrderStage[] = [
        OrderStage.PROSPECT,
        OrderStage.INTERESTED,
        OrderStage.TO_PAY,
        OrderStage.SOLD,
    ];

    const stageIndex = stageOrder.indexOf(stage);
    const currentStageIndex = stageOrder.indexOf(currentStage);

    // If current stage is LOST, no other stages are considered "completed"
    if (currentStage === OrderStage.LOST) {
        return false;
    }

    return stageIndex < currentStageIndex;
}

export default EditOrderStage;
