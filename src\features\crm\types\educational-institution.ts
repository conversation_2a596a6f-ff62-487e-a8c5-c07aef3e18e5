export enum EducationalInstitutionType {
    UNIVERSITY_PUBLIC = "university_public",
    UNIVERSITY_PRIVATE = "university_private",
    INSTITUTE_PUBLIC = "institute_public",
    INSTITUTE_PRIVATE = "institute_private",
    COLLEGE_PUBLIC = "college_public",
    COLLEGE_PRIVATE = "college_private",
}

export const EducationalInstitutionTypeLabel: Record<
    EducationalInstitutionType,
    string
> = {
    [EducationalInstitutionType.UNIVERSITY_PUBLIC]: "Universidad Pública",
    [EducationalInstitutionType.UNIVERSITY_PRIVATE]: "Universidad Privada",
    [EducationalInstitutionType.INSTITUTE_PUBLIC]: "Instituto Público",
    [EducationalInstitutionType.INSTITUTE_PRIVATE]: "Instituto Privado",
    [EducationalInstitutionType.COLLEGE_PUBLIC]: "Colegio Público",
    [EducationalInstitutionType.COLLEGE_PRIVATE]: "Colegio Privado",
};

export type EducationalInstitution = {
    eiid: string;
    name: string;
};

export type CreateEducationalInstitutionValues = {
    name: string;
    country?: string;
    region?: string;
    city?: string;
    acronym?: string;
    institutionType?: EducationalInstitutionType;
};
