import { UploadFile } from "antd";

export enum TestimonialStatusEnum {
    PUBLISHED = "Published",
    DRAFT = "Draft",
}

export type TestimonialAuthorPhoto = {
    /**
     * Unique UUID identifier for the testimonial profile photo
     * @example 123e4567-e89b-12d3-a456-************
     * @format uuid
     */
    fid: string;

    /** File name of the testimonial profile photo
     */
    name: string;

    /** URL of the testimonial profile photo
     */
    url: string;
};

export type Testimonial = {
    /**
     * Unique identifier for the testimonial
     */
    tid: string;

    /**
     * Unique identifier for the testimonial
     */
    key: string;

    /**
     * Full name of the testimonial
     */
    authorName: string;

    /**
     * Title of the testimonial author
     */
    authorTitle?: string;

    /**
     * Content of the testimonial
     */
    content: string;

    /**
     * Order for the testimonial
     */
    order: number;

    /**
     * Profile photo of the testimonial author
     */
    authorPhoto?: TestimonialAuthorPhoto;

    /**
     * Status of the testimonial
     */
    status: TestimonialStatusEnum;

    /**
     * Date the testimonial was created
     */
    createdAt: string;

    /**
     * Date the testimonial was last updated
     */
    updatedAt: string;

    /**
     * If the testimonial has been deleted
     */
    deleted: boolean;
};

export type RetrieveTestimonial = Testimonial;

export type CreateTestimonial = Partial<
    Omit<Testimonial, "id" | "key" | "createdAt" | "updatedAt" | "deleted">
> & {
    authorPhotoFile?: File[];
};

export type PartialUpdateTestimonial = Partial<
    Omit<Testimonial, "id" | "key" | "createdAt" | "updatedAt" | "deleted">
> & {
    authorPhotoFile?: UploadFile[];
    deleteAuthorPhoto?: boolean;
};
