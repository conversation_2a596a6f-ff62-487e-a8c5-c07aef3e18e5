import { useContacts } from "@/features/crm/hooks/use-contact";
import { Select, SelectProps } from "antd";
import { ExternalLink } from "lucide-react";
import { Link } from "react-router-dom";

interface SelectContactProps extends Omit<SelectProps, "options"> {
    value?: string; // Add value prop for controlled component
    onChange?: (value: string) => void; // Add onChange prop
    selectedStaffUser?: string;
}

export default function SelectStaffUser({
    value,
    onChange,
    selectedStaffUser,
    ...restProps
}: SelectContactProps) {
    const { contacts, isLoading } = useContacts({
        pageSize: 100,
        query: { isStaff: true },
    });

    const contactsOptions: SelectProps["options"] = contacts.map((contact) => ({
        value: contact.uid,
        label: contact.fullName,
        data: {
            info: contact,
        },
    }));

    return (
        <>
            <Select
                {...restProps}
                value={selectedStaffUser || value}
                onChange={onChange}
                options={contactsOptions}
                optionRender={(option) => (
                    <div className="flex justify-between items-center">
                        <span>{option.data.label}</span>
                        <span className="text-xs text-gray-600">
                            {option.data.data.info.phoneNumber}
                        </span>
                        <Link
                            to={`/crm/contacts/${option.data.value}`}
                            title="View Contact"
                        >
                            <ExternalLink size={14} />
                        </Link>
                    </div>
                )}
                loading={isLoading}
                optionFilterProp="label"
                allowClear
                showSearch
            />
        </>
    );
}
