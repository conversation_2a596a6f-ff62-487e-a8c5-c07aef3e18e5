import { Card, Col, Row, Statistic } from "antd";
import {
    <PERSON>,
    User<PERSON>he<PERSON>,
    UserX,
    AlertTriangle,
    T<PERSON>dingUp,
    UserPlus,
} from "lucide-react";
import { EventScheduleEnrollment } from "../../types/event-schedule-enrollment";

interface EnrollmentStatsProps {
    enrollments: EventScheduleEnrollment[];
    totalCount: number;
}

export default function EnrollmentStats({
    enrollments,
    totalCount,
}: EnrollmentStatsProps) {
    // Calculate statistics
    const withContact = enrollments.filter((e) => e.hasContact).length;
    const withoutContact = enrollments.filter((e) => !e.hasContact).length;
    const needsConciliation = enrollments.filter((e) => e.needsConciliation).length;
    const alreadyLeads = enrollments.filter((e) => e.alreadyLead).length;
    const withPartnership = enrollments.filter((e) => e.partnership !== null).length;

    // Calculate percentages
    const contactPercentage =
        totalCount > 0 ? ((withContact / totalCount) * 100).toFixed(1) : "0";
    const leadsPercentage =
        totalCount > 0 ? ((alreadyLeads / totalCount) * 100).toFixed(1) : "0";

    return (
        <Row gutter={[16, 16]} className="mb-6">
            <Col xs={24} sm={12} lg={4}>
                <Card size="small" className="text-center">
                    <Statistic
                        title="Total Inscritos"
                        value={totalCount}
                        prefix={<Users className="text-blue-500" size={20} />}
                        valueStyle={{ color: "#1890ff", fontSize: "24px" }}
                    />
                </Card>
            </Col>

            <Col xs={24} sm={12} lg={4}>
                <Card size="small" className="text-center">
                    <Statistic
                        title="Con Contacto"
                        value={withContact}
                        prefix={<UserCheck className="text-green-500" size={20} />}
                        suffix={`(${contactPercentage}%)`}
                        valueStyle={{ color: "#52c41a", fontSize: "20px" }}
                    />
                </Card>
            </Col>

            <Col xs={24} sm={12} lg={4}>
                <Card size="small" className="text-center">
                    <Statistic
                        title="Sin Contacto"
                        value={withoutContact}
                        prefix={<UserX className="text-red-500" size={20} />}
                        valueStyle={{ color: "#f5222d", fontSize: "20px" }}
                    />
                </Card>
            </Col>

            <Col xs={24} sm={12} lg={4}>
                <Card size="small" className="text-center">
                    <Statistic
                        title="Ya son Leads"
                        value={alreadyLeads}
                        prefix={<TrendingUp className="text-purple-500" size={20} />}
                        suffix={`(${leadsPercentage}%)`}
                        valueStyle={{ color: "#722ed1", fontSize: "20px" }}
                    />
                </Card>
            </Col>

            <Col xs={24} sm={12} lg={4}>
                <Card size="small" className="text-center">
                    <Statistic
                        title="Necesitan Conciliación"
                        value={needsConciliation}
                        prefix={<AlertTriangle className="text-orange-500" size={20} />}
                        valueStyle={{ color: "#fa8c16", fontSize: "20px" }}
                    />
                </Card>
            </Col>

            <Col xs={24} sm={12} lg={4}>
                <Card size="small" className="text-center">
                    <Statistic
                        title="Con Alianza"
                        value={withPartnership}
                        prefix={<UserPlus className="text-cyan-500" size={20} />}
                        valueStyle={{ color: "#13c2c2", fontSize: "20px" }}
                    />
                </Card>
            </Col>
        </Row>
    );
}
