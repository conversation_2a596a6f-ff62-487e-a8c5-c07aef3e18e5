import { PaginatedResponse } from "@myTypes/base";
import { portalsApi } from "@services/portals";
import {
    ActivityQueryParams,
    ActivityCreate,
    ActivityUpdate,
    ActivityRetrieve,
    ActivityList,
} from "../../types/activity";

export const DEFAULT_PAGE = 1;
export const DEFAULT_PAGE_SIZE = 20;

export const listActivities = async (
    queryParams: ActivityQueryParams = {
        page: DEFAULT_PAGE,
        pageSize: DEFAULT_PAGE_SIZE,
    },
): Promise<PaginatedResponse<ActivityList>> => {
    const response = await portalsApi.get("crm/activities", {
        params: queryParams,
    });
    return response.data;
};

export const retrieveActivity = async (aid: string): Promise<ActivityRetrieve> => {
    const response = await portalsApi.get(`crm/activities/${aid}`);
    return response.data;
};

export const createActivity = async (
    activity: ActivityCreate,
): Promise<ActivityRetrieve> => {
    const response = await portalsApi.post("crm/activities", activity);
    return response.data;
};

export const updateActivity = async (
    aid: string,
    activity: ActivityUpdate,
): Promise<ActivityRetrieve> => {
    const response = await portalsApi.put(`crm/activities/${aid}`, activity);
    return response.data;
};

export const deleteActivity = async (aid: string): Promise<void> => {
    await portalsApi.delete(`crm/activities/${aid}`);
};
