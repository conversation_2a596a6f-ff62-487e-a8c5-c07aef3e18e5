import type { Order, OrderItem } from "../../types/order";
import { DollarSign, CoinsIcon } from "lucide-react";
import { Tooltip } from "antd";
import EditOrderItemCustomAmountPopover from "../molecules/edit-order-item-custom-amount-popover";

type OrderItemPriceCellProps = {
    record: OrderItem;
    order: Order;
    editCustomAmount?: boolean;
};

export default function OrderItemPriceCell({
    record,
    order,
    editCustomAmount, // enables editing the custom amount with a EditOrderItemCustomAmountPopover
}: OrderItemPriceCellProps) {
    const isInternational = order.isInternational || false;

    return (
        <div className="flex items-center gap-2">
            <div className="flex flex-col space-y-1">
                {!isInternational ? (
                    <Tooltip title="Precio en soles">
                        <div className="flex items-center gap-2">
                            <CoinsIcon
                                size={16}
                                className="text-complementary-yellow"
                                strokeWidth={1.75}
                            />
                            <span className="text-sm font-medium text-black-full">
                                S/{" "}
                                {(record.customAmount !== null
                                    ? Number(record.customAmount)
                                    : (record.effectiveUnitPrice ?? 0)
                                ).toFixed(2)}
                            </span>
                        </div>
                    </Tooltip>
                ) : (
                    <Tooltip title="Precio en dólares">
                        <div className="flex items-center gap-2">
                            <DollarSign
                                size={16}
                                className="text-blue-medium"
                                strokeWidth={1.75}
                            />
                            <span className="text-sm font-medium text-black-full">
                                {(record.customAmount !== null
                                    ? Number(record.customAmount)
                                    : (record.effectiveUnitPrice ?? 0)
                                ).toFixed(2)}
                            </span>
                        </div>
                    </Tooltip>
                )}
            </div>

            {editCustomAmount && (
                <EditOrderItemCustomAmountPopover
                    orderItem={record}
                    orderId={order.oid}
                    isInternational={isInternational}
                />
            )}
        </div>
    );
}
