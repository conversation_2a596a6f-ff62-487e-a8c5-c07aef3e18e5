import { Typography } from "antd";
import { Calendar, Clock, AlertTriangle } from "lucide-react";
import dayjs from "dayjs";

const { Text } = Typography;

interface ActivityDeadlineCellProps {
    deadline: string | null;
}

const ActivityDeadlineCell = ({ deadline }: ActivityDeadlineCellProps) => {
    if (!deadline) {
        return (
            <div className="flex items-center gap-2">
                <Clock size={14} className="text-gray-400" />
                <Text type="secondary" className="text-xs">
                    Sin fecha límite
                </Text>
            </div>
        );
    }

    const deadlineDate = dayjs(deadline);
    const now = dayjs();
    const isOverdue = deadlineDate.isBefore(now);
    const isToday = deadlineDate.isSame(now, "day");
    const isTomorrow = deadlineDate.isSame(now.add(1, "day"), "day");

    const getDisplayText = () => {
        if (isToday) return "Hoy";
        if (isTomorrow) return "Ma<PERSON>na";
        return deadlineDate.format("DD/MM/YYYY");
    };

    const getStatusConfig = () => {
        if (isOverdue) {
            return {
                bgColor: "bg-red-50",
                textColor: "text-red-800",
                borderColor: "border-red-200",
                icon: (
                    <AlertTriangle size={14} className="text-red-600" strokeWidth={2} />
                ),
                label: "Vencida",
            };
        } else if (isToday) {
            return {
                bgColor: "bg-orange-50",
                textColor: "text-orange-800",
                borderColor: "border-orange-200",
                icon: <Clock size={14} className="text-orange-600" strokeWidth={2} />,
                label: "Vence hoy",
            };
        } else if (isTomorrow) {
            return {
                bgColor: "bg-yellow-50",
                textColor: "text-yellow-800",
                borderColor: "border-yellow-200",
                icon: <Clock size={14} className="text-yellow-600" strokeWidth={2} />,
                label: "Vence mañana",
            };
        }
        return {
            bgColor: "bg-blue-50",
            textColor: "text-blue-800",
            borderColor: "border-blue-200",
            icon: <Calendar size={14} className="text-blue-600" strokeWidth={2} />,
            label: "Programada",
        };
    };

    const config = getStatusConfig();

    return (
        <div className="flex flex-col gap-2">
            <span
                className={`text-xs font-medium px-2 py-1 rounded-full ${config.bgColor} ${config.textColor} ${config.borderColor} border w-fit`}
            >
                <div className="flex items-center gap-1">
                    {config.icon}
                    <span>{config.label}</span>
                </div>
            </span>

            <div className="flex items-center gap-2">
                <div className="flex flex-col">
                    <span className="text-xs font-medium text-gray-600">
                        {getDisplayText()}
                    </span>
                    <span className="text-xs text-gray-500">
                        {deadlineDate.format("HH:mm")}
                    </span>
                </div>
            </div>
        </div>
    );
};

export default ActivityDeadlineCell;
