import { useMutation, useQuery } from "@tanstack/react-query";
import {
    getPaymentMethods,
    createPaymentMethod,
} from "../services/portals/payment-method";
import { CreatePaymentMethod } from "../types/payment-method";
import queryClient from "@lib/queryClient";
import { App } from "antd";

export const usePaymentMethods = () => {
    const { data, isLoading, isError } = useQuery({
        queryKey: ["payment-methods"],
        queryFn: () => getPaymentMethods(),
    });

    const { count, results: paymentMethods } = data || {
        count: 0,
        results: [],
    };

    return {
        isLoading,
        isError,
        paymentMethods,
        count,
    };
};

export const useCreatePaymentMethod = ({
    onSuccess,
}: { onSuccess?: () => void } = {}) => {
    const { notification } = App.useApp();

    return useMutation({
        mutationFn: (data: CreatePaymentMethod) => createPaymentMethod(data),
        onSuccess: () => {
            notification.success({
                message: "Método de pago creado",
                description: "El método de pago ha sido creado exitosamente",
            });
            queryClient.invalidateQueries({ queryKey: ["payment-methods"] });
            onSuccess?.();
        },
        onError: () => {
            notification.error({
                message: "Error al crear el método de pago",
                description: "Ha ocurrido un error al intentar crear el método de pago",
            });
        },
    });
};
