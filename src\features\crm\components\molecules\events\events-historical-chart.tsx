import React from "react";
import { Card, Empty } from "antd";
import {
    <PERSON><PERSON><PERSON>,
    <PERSON>,
    <PERSON>Axis,
    <PERSON>Axis,
    CartesianGrid,
    Tooltip,
    ResponsiveContainer,
} from "recharts";
import { Calendar } from "lucide-react";
import dayjs from "dayjs";
import type {
    DashboardEventsHistoricalData,
    EventHistoricalEducationalInstitution,
} from "@/features/crm/types/dashboard/events";

interface EventsHistoricalChartProps {
    data?: DashboardEventsHistoricalData;
    isLoading?: boolean;
}

interface ChartDataItem {
    eventName: string;
    formattedDate: string;
    totalEnrollments: number;
    // Barra 1: Total (con/sin universidad)
    withUniversity: number;
    withoutUniversity: number;
    // Barra 2: Universidades individuales
    topEducationalInstitutions: EventHistoricalEducationalInstitution[];
    [key: string]: number | string | EventHistoricalEducationalInstitution[];
}

interface CustomTooltipProps {
    active?: boolean;
    payload?: Array<{
        value: number;
        dataKey: string;
        name: string;
        color: string;
        payload: ChartDataItem;
    }>;
    label?: string;
}

// Variable para identificar qué barra está siendo hover
let currentTooltipBar: string = "";

const EventsHistoricalChart: React.FC<EventsHistoricalChartProps> = ({
    data,
    isLoading,
}) => {
    // Transformar datos para el gráfico
    const chartData: ChartDataItem[] = React.useMemo(() => {
        if (!data?.data) return [];

        return data.data.map((event) => {
            // Usar el campo que ya viene del backend, con validación para evitar NaN
            const withoutUniversityCount = event.withouthEducationalInstitution || 0;
            const withUniversity = Math.max(
                0,
                event.totalEnrollments - withoutUniversityCount,
            );
            const withoutUniversity = withoutUniversityCount;

            // Formatear fecha como "04/09 - 4:30pm"
            const startDate = dayjs(event.startDate);
            const formattedDate = `${startDate.format("DD/MM")} ${startDate.format("h:mma")}`;

            const chartItem: ChartDataItem = {
                eventName: event.eventName,
                formattedDate,
                totalEnrollments: event.totalEnrollments,
                withUniversity,
                withoutUniversity,
                topEducationalInstitutions: event.topEducationalInstitutions || [],
            };

            // Agregar universidades dinámicamente para las barras
            (event.topEducationalInstitutions || []).forEach((uni, index) => {
                chartItem[`university${index + 1}`] = uni.enrollments;
                chartItem[`university${index + 1}Name`] = uni.name;
            });

            return chartItem;
        });
    }, [data]);

    // Calcular el número máximo de universidades en cualquier evento
    const maxUniversities = React.useMemo(() => {
        if (!chartData.length) return 0;
        return Math.max(
            ...chartData.map((item) => item.topEducationalInstitutions.length),
        );
    }, [chartData]);

    // Tooltip personalizado usando el patrón recomendado
    const CustomTooltip = ({ active, payload }: CustomTooltipProps) => {
        if (!active || !currentTooltipBar || !payload) return null;

        const data = payload[0]?.payload;
        if (!data) return null;

        // Tooltip para barra de totales (con/sin universidad)
        if (currentTooltipBar === "total") {
            const withUniversityPercentage =
                data.totalEnrollments > 0
                    ? ((data.withUniversity / data.totalEnrollments) * 100).toFixed(1)
                    : "0";
            const withoutUniversityPercentage =
                data.totalEnrollments > 0
                    ? ((data.withoutUniversity / data.totalEnrollments) * 100).toFixed(
                          1,
                      )
                    : "0";

            return (
                <div className="bg-white-full p-3 border border-gray-200 shadow-md rounded">
                    <p className="font-medium mb-2">{data.eventName}</p>
                    <p className="text-sm mb-1">
                        <strong>Total de inscritos:</strong> {data.totalEnrollments}
                    </p>
                    <div className="space-y-1">
                        <p className="text-sm" style={{ color: "#4096ff" }}>
                            Con universidad: {data.withUniversity} (
                            {withUniversityPercentage}%)
                        </p>
                        <p className="text-sm" style={{ color: "#36cfc9" }}>
                            Sin universidad: {data.withoutUniversity} (
                            {withoutUniversityPercentage}%)
                        </p>
                    </div>
                </div>
            );
        }

        // Tooltip para barras de universidades
        if (currentTooltipBar.startsWith("university")) {
            const universityKey = currentTooltipBar;
            const nameKey = `${universityKey}Name`;
            const universityName = data[nameKey] as string;
            const enrollments = data[universityKey] as number;
            const globalPercentage =
                data.totalEnrollments > 0
                    ? ((enrollments / data.totalEnrollments) * 100).toFixed(1)
                    : "0";

            return (
                <div className="bg-white-full p-3 border border-gray-200 shadow-md rounded">
                    <p className="font-medium mb-2">{data.eventName}</p>
                    <p className="text-sm mb-1">
                        <strong>Universidad:</strong> {universityName}
                    </p>
                    <p className="text-sm" style={{ color: "#faad14" }}>
                        Inscritos: {enrollments} ({globalPercentage}% del total)
                    </p>
                </div>
            );
        }

        return null;
    };

    // Colores para las universidades
    const universityColors = ["#faad14", "#73d13d", "#ff4d4f", "#722ed1", "#eb2f96"];

    if (isLoading) {
        return (
            <Card
                title={
                    <div className="flex items-center gap-2">
                        <Calendar size={20} className="text-blue-500" />
                        <span>Histórico de Eventos</span>
                    </div>
                }
                className="shadow-md h-full"
            >
                <div className="h-64 flex items-center justify-center">
                    <div className="animate-pulse">Cargando...</div>
                </div>
            </Card>
        );
    }

    if (!chartData.length) {
        return (
            <Card
                title={
                    <div className="flex items-center gap-2">
                        <Calendar size={20} className="text-blue-500" />
                        <span>Histórico de Eventos</span>
                    </div>
                }
                className="shadow-md h-full"
            >
                <Empty description="No hay datos disponibles" />
            </Card>
        );
    }

    return (
        <Card
            title={
                <div className="flex items-center gap-2">
                    <Calendar size={20} className="text-blue-500" />
                    <span>Histórico de Eventos</span>
                </div>
            }
            className="shadow-md h-full"
        >
            <div className="h-[450px]">
                <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                        data={chartData}
                        margin={{
                            top: 20,
                            right: 30,
                            left: 20,
                        }}
                    >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis
                            dataKey="formattedDate"
                            angle={-45}
                            textAnchor="end"
                            height={80}
                            interval={0}
                            tick={{ fontSize: 10 }}
                        />
                        <YAxis tick={{ fontSize: 12 }} />

                        {/* Barra 1: Total (con/sin universidad) */}
                        <Bar
                            dataKey="withUniversity"
                            stackId="total"
                            fill="#4096ff"
                            name="Con Universidad"
                            onMouseOver={() => (currentTooltipBar = "total")}
                            radius={[0, 0, 0, 0]}
                        />
                        <Bar
                            dataKey="withoutUniversity"
                            stackId="total"
                            fill="#36cfc9"
                            name="Sin Universidad"
                            onMouseOver={() => (currentTooltipBar = "total")}
                            radius={[4, 4, 0, 0]}
                        />

                        {/* Barra 2: Universidades individuales */}
                        {Array.from({ length: maxUniversities }, (_, index) => (
                            <Bar
                                key={`university${index + 1}`}
                                dataKey={`university${index + 1}`}
                                stackId="universities"
                                fill={universityColors[index % universityColors.length]}
                                name={`Universidad ${index + 1}`}
                                onMouseOver={() =>
                                    (currentTooltipBar = `university${index + 1}`)
                                }
                                radius={
                                    index === maxUniversities - 1
                                        ? [4, 4, 0, 0]
                                        : [0, 0, 0, 0]
                                }
                            />
                        ))}

                        {/* Tooltip personalizado */}
                        <Tooltip
                            content={<CustomTooltip />}
                            cursor={{ fill: "rgba(64, 150, 255, 0.1)" }}
                        />
                    </BarChart>
                </ResponsiveContainer>
            </div>
        </Card>
    );
};

export default EventsHistoricalChart;
