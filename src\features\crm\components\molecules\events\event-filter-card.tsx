import React, { useEffect, useRef } from "react";
import { Card, DatePicker, Select, Button, Form, Tooltip } from "antd";
import { Filter, RefreshCwIcon } from "lucide-react";
import locale from "antd/es/date-picker/locale/es_ES";
import dayjs, { Dayjs } from "dayjs";
import { useMutateSearchParams } from "@hooks/use-mutate-search-params";
import "dayjs/locale/es";
import SelectOfferings from "../select-offerings";
import SelectEvents from "../select-events";
import {
    EventStageLabels,
    EventTypeLabels,
    EventModalityLabels,
} from "@/features/crm/types/event";
import { useDashboardEventsInvalidateCache } from "@/features/crm/hooks/use-dashboard-events";
import { useNavigate } from "react-router-dom";
import Reload from "@assets/icons/huge/reload.svg?react";

dayjs.locale("es");

const { RangePicker } = DatePicker;
const { Option } = Select;

export type EventStageFilter =
    | "planning"
    | "launched"
    | "enrollment_closed"
    | "finished";
export type EventTypeFilter = "workshop" | "webinar" | "hands_of_workshop";
export type EventModalityFilter = "remote" | "in_person";

interface FilterValues {
    dateRange?: [Dayjs, Dayjs] | null;
    stage?: EventStageFilter[];
    eventType?: EventTypeFilter[];
    modality?: EventModalityFilter[];
    programs?: string[];
    events?: string[];
}

/**
 * Estado de los filtros de eventos para usar con search params
 */
export interface EventFiltersState {
    dateRange?: [Dayjs, Dayjs] | null;
    stage: EventStageFilter[];
    eventType: EventTypeFilter[];
    modality: EventModalityFilter[];
    programs: string[];
    events: string[];
}

interface EventFilterCardProps {
    className?: string;
}

const EventFilterCard: React.FC<EventFilterCardProps> = ({ className }) => {
    const [form] = Form.useForm<FilterValues>();
    const { searchParams, mutateManySearchParams } = useMutateSearchParams();
    const navigate = useNavigate();

    // Capturar valores iniciales
    const initialValuesRef = useRef<FilterValues | null>(null);

    if (initialValuesRef.current === null) {
        const values: FilterValues = {};

        // Parse stage
        const stageParam = searchParams.get("stage");
        if (stageParam) {
            values.stage = stageParam.split(",") as EventStageFilter[];
        }

        // Parse eventType
        const eventTypeParam = searchParams.get("eventType");
        if (eventTypeParam) {
            values.eventType = eventTypeParam.split(",") as EventTypeFilter[];
        }

        // Parse modality
        const modalityParam = searchParams.get("modality");
        if (modalityParam) {
            values.modality = modalityParam.split(",") as EventModalityFilter[];
        }

        // Parse programs
        const programsParam = searchParams.get("programs");
        if (programsParam) {
            values.programs = programsParam.split(",");
        }

        // Parse events
        const eventsParam = searchParams.get("events");
        if (eventsParam) {
            values.events = eventsParam.split(",");
        }

        // Date range
        const startDate = searchParams.get("startDate");
        const endDate = searchParams.get("endDate");
        if (startDate && endDate) {
            values.dateRange = [dayjs(startDate), dayjs(endDate)];
        } else {
            // Esta semana por defecto
            values.dateRange = [dayjs().startOf("week"), dayjs().endOf("week")];
        }

        initialValuesRef.current = values;
    }

    const getInitialValues = () => {
        if (!initialValuesRef.current) return {};
        return {
            ...initialValuesRef.current,
            dateRange: undefined,
        };
    };

    // Establecer valores iniciales solo una vez
    useEffect(() => {
        if (initialValuesRef.current) {
            form.setFieldsValue(getInitialValues());
        }
    }, [form]);

    const handleApplyFilters = () => {
        const values = form.getFieldsValue();

        // Map form values to search params with correct names
        const newParams: Record<string, string | null> = {};

        // Date range
        if (values.dateRange) {
            newParams.startDate = values.dateRange[0].format("YYYY-MM-DD");
            newParams.endDate = values.dateRange[1].format("YYYY-MM-DD");
        }

        // Stage (multiple values)
        if (values.stage && values.stage.length > 0) {
            newParams.stage = values.stage.join(",");
        } else {
            newParams.stage = null;
        }

        // Event type (multiple values)
        if (values.eventType && values.eventType.length > 0) {
            newParams.eventType = values.eventType.join(",");
        } else {
            newParams.eventType = null;
        }

        // Modality (multiple values)
        if (values.modality && values.modality.length > 0) {
            newParams.modality = values.modality.join(",");
        } else {
            newParams.modality = null;
        }

        // Programs
        if (values.programs && values.programs.length > 0) {
            newParams.programs = values.programs.join(",");
        } else {
            newParams.programs = null;
        }

        // Events
        if (values.events && values.events.length > 0) {
            newParams.events = values.events.join(",");
        } else {
            newParams.events = null;
        }

        mutateManySearchParams(newParams);
    };

    const handleClearFilters = () => {
        // Valores por defecto: rango de fechas "esta semana"

        const defaultValues = {
            dateRange: undefined,
            stage: [] as EventStageFilter[],
            eventType: [] as EventTypeFilter[],
            modality: [] as EventModalityFilter[],
            programs: [] as string[],
            events: [] as string[],
        };

        // Limpiar los search params y resetear el formulario al mismo tiempo
        const emptyParams: Record<string, string | null> = {
            startDate: null,
            endDate: null,
            stage: null,
            eventType: null,
            modality: null,
            programs: null,
            events: null,
        };

        form.setFieldsValue(defaultValues);
        mutateManySearchParams(emptyParams);
    };

    const { mutateAsync: invalidateCache } = useDashboardEventsInvalidateCache();
    const handleInvalidateCache = async () => {
        await invalidateCache();
        navigate(0);
    };

    return (
        <Card
            className={`shadow-md mb-6 ${className || ""}`}
            title={
                <div className="flex items-center">
                    <Filter className="mr-2 h-5 w-5 text-blue-500" />
                    <span>Filtrar eventos</span>
                </div>
            }
            extra={
                <Tooltip
                    title="Fuerza la actualización de los datos desde el servidor. Úsalo solo cuando sea necesario para no sobrecargar el sistema."
                    placement="bottomRight"
                >
                    <Button onClick={handleInvalidateCache}>
                        <Reload />
                        <span className="sr-only">Forzar actualización</span>
                    </Button>
                </Tooltip>
            }
        >
            <Form
                form={form}
                layout="vertical"
                initialValues={undefined}
                onFinish={handleApplyFilters}
            >
                <div className="flex flex-wrap gap-4">
                    <Form.Item
                        name="dateRange"
                        label="Rango de fechas"
                        className="mb-0"
                    >
                        <RangePicker
                            className="w-full"
                            locale={locale}
                            presets={[
                                {
                                    label: "Hoy",
                                    value: [dayjs(), dayjs()],
                                },
                                {
                                    label: "Esta semana",
                                    value: [
                                        dayjs().startOf("week"),
                                        dayjs().endOf("week"),
                                    ],
                                },
                                {
                                    label: "Este mes",
                                    value: [
                                        dayjs().startOf("month"),
                                        dayjs().endOf("month"),
                                    ],
                                },
                                {
                                    label: "Último mes",
                                    value: [
                                        dayjs().subtract(1, "month").startOf("month"),
                                        dayjs().subtract(1, "month").endOf("month"),
                                    ],
                                },
                                {
                                    label: "Este año",
                                    value: [
                                        dayjs().startOf("year"),
                                        dayjs().endOf("year"),
                                    ],
                                },
                            ]}
                        />
                    </Form.Item>

                    <Form.Item
                        name="programs"
                        label="Programas"
                        className="mb-0 min-w-[500px] flex-grow"
                    >
                        <SelectOfferings
                            placeholder="Seleccionar"
                            mode="multiple"
                            maxTagCount={1}
                            maxTagTextLength={22}
                            className="w-full overflow-hidden"
                            popupClassName="min-w-[500px]"
                            allowClear
                        />
                    </Form.Item>

                    <Form.Item
                        name="events"
                        label="Evento"
                        className="mb-0 min-w-[500px] flex-grow"
                    >
                        <SelectEvents
                            placeholder="Seleccionar eventos"
                            mode="multiple"
                            maxTagCount={1}
                            maxTagTextLength={22}
                            className="w-full  overflow-hidden"
                            popupClassName="min-w-[500px]"
                            allowClear
                        />
                    </Form.Item>

                    <div className="min-w-[180px]">
                        <Form.Item
                            name="eventType"
                            label="Tipo de evento"
                            className="mb-0"
                        >
                            <Select
                                className="w-full"
                                mode="multiple"
                                allowClear
                                placeholder="Seleccionar tipo"
                            >
                                {Object.entries(EventTypeLabels).map(
                                    ([value, label]) => (
                                        <Option key={value} value={value}>
                                            {label}
                                        </Option>
                                    ),
                                )}
                            </Select>
                        </Form.Item>
                    </div>

                    <div className="min-w-[180px]">
                        <Form.Item name="stage" label="Etapa" className="mb-0">
                            <Select
                                className="w-full"
                                mode="multiple"
                                allowClear
                                placeholder="Seleccionar etapa"
                            >
                                {Object.entries(EventStageLabels).map(
                                    ([value, label]) => (
                                        <Option key={value} value={value}>
                                            {label}
                                        </Option>
                                    ),
                                )}
                            </Select>
                        </Form.Item>
                    </div>

                    <div className="min-w-[180px]">
                        <Form.Item name="modality" label="Modalidad" className="mb-0">
                            <Select
                                className="w-full"
                                mode="multiple"
                                allowClear
                                placeholder="Seleccionar modalidad"
                            >
                                {Object.entries(EventModalityLabels).map(
                                    ([value, label]) => (
                                        <Option key={value} value={value}>
                                            {label}
                                        </Option>
                                    ),
                                )}
                            </Select>
                        </Form.Item>
                    </div>

                    <div className="flex gap-2 self-end">
                        <Button
                            type="default"
                            icon={<RefreshCwIcon className="h-4 w-4" />}
                            onClick={handleClearFilters}
                        >
                            Limpiar filtros
                        </Button>
                        <Button type="primary" htmlType="submit">
                            Aplicar filtros
                        </Button>
                    </div>
                </div>
            </Form>
        </Card>
    );
};

export default EventFilterCard;
