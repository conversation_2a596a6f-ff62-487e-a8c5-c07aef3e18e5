import { Select, SelectProps } from "antd";
import { ExternalLink } from "lucide-react";
import { Link } from "react-router-dom";
import { useInstructors } from "../../hooks/use-instructor";

interface SelectContactProps extends Omit<SelectProps, "options"> {
    value?: string; // Add value prop for controlled component
    onChange?: (value: string) => void; // Add onChange prop
}

export default function SelectInstructor({
    value,
    onChange,
    ...restProps
}: SelectContactProps) {
    const { instructors } = useInstructors();

    const instructorsOptions: SelectProps["options"] = instructors?.map(
        (instructor) => ({
            value: instructor.iid,
            label: instructor.fullName,
            data: {
                ...instructor,
            },
        }),
    );

    return (
        <>
            <Select
                {...restProps}
                value={value}
                onChange={onChange}
                options={instructorsOptions}
                optionRender={(option) => (
                    <div className="flex justify-between items-center">
                        <span>{option.data.label}</span>
                        <Link
                            to={`/cms/instructor/${option.data.value}`}
                            title="View Offering"
                        >
                            <ExternalLink size={14} />
                        </Link>
                    </div>
                )}
            />
        </>
    );
}
