import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ge } from "antd";
import { Plus } from "lucide-react";
import { usePayments } from "../../hooks/use-payment";
import { RetrieveOrder } from "../../types/order";
import { ListPaymentsQueryParams } from "../../types/payment";
import PaymentsTable from "./payments-table";
import CreatePaymentForm from "./create-payment-form";

const { Text } = Typography;

export default function OrderPaymentsTabs({ order }: { order: RetrieveOrder }) {
    const [isCreatePaymentModalOpen, setIsCreatePaymentModalOpen] =
        useState<boolean>(false);

    const queryParams: ListPaymentsQueryParams = {
        order: order.oid,
    };
    const { payments } = usePayments({ queryParams });

    const handleCreatePaymentModalOpen = () => {
        setIsCreatePaymentModalOpen(true);
    };

    const handleCreatePaymentModalClose = () => {
        setIsCreatePaymentModalOpen(false);
        // La lista se refrescará automáticamente gracias a la invalidación de queries en useCreatePayment
    };

    return (
        <div className="space-y-4">
            {/* Header con botón para agregar pago */}
            <div className="flex justify-between items-center">
                <Text className="text-black-medium text-xl font-semibold">
                    Pagos <Badge count={payments.length} color="blue" size="default" />
                </Text>
                <Button
                    type="primary"
                    size="large"
                    icon={<Plus />}
                    onClick={handleCreatePaymentModalOpen}
                >
                    Agregar Pago
                </Button>
            </div>

            {/* Modal para crear pago */}
            <Modal
                centered
                open={isCreatePaymentModalOpen}
                onCancel={handleCreatePaymentModalClose}
                footer={false}
                title="Agregar/Programar nuevo pago"
                width={600}
            >
                <CreatePaymentForm
                    onFinish={handleCreatePaymentModalClose}
                    preselectedOrder={order}
                />
            </Modal>

            {/* Tabla de pagos */}
            <PaymentsTable initialData={payments} />
        </div>
    );
}
