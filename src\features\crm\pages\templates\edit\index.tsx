import { useEffect, useState } from "react";
import { Link, useNavigate, useParams } from "react-router-dom";
import {
    <PERSON><PERSON>crumb,
    Button,
    Form,
    Image,
    Input,
    Modal,
    Select,
    Tooltip,
    Typography,
    Upload,
} from "antd";
import ImgCrop from "antd-img-crop";
import type { UploadFile, GetProp, UploadProps } from "antd";

const { Text } = Typography;
const { Dragger } = Upload;

import Spinner from "@components/shared/atoms/Spinner";

import Save from "@assets/icons/general/save-stroke.svg?react";
import CloudUpload from "@assets/shapes/cloud-upload.svg?react";

import {
    TemplateStatus,
    TemplateStatusLabel,
    PartialUpdateTemplateBody,
    // TemplateButton,
} from "@/features/crm/types/template";
import { getBase64 } from "@lib/helpers";
import WelcomeBar from "@components/shared/molecules/WelcomeBar";
import CrmLayout from "@/features/crm/layout";
import WhatsappTemplateEditor from "@/features/crm/components/molecules/templates/WhatsappTemplateEditor";
// import WhatsappButtonsEditor from "@/features/crm/components/molecules/templates/WhatsappButtonsEditor";
import WhatsAppMessagePreview from "@/features/crm/components/molecules/templates/WhatsappMessagePreview";
import SelectTemplateTypes from "@/features/crm/components/molecules/select-template-type";
import {
    useDeleteTemplate,
    useTemplate,
    useUpdateTemplate,
} from "@/features/crm/hooks/use-template";
import { Send, Trash } from "lucide-react";
import TestTemplateForm from "@/features/crm/components/organisms/test-template-form";

type FileType = Parameters<GetProp<UploadProps, "beforeUpload">>[0];

type DraggerProps = {
    customRequest: UploadProps["customRequest"];
    onRemove?: UploadProps["onRemove"];
    onPreview?: UploadProps["onPreview"];
    fileList?: UploadProps<FileType>["fileList"];
};

export default function TemplateEditPage() {
    const { tid } = useParams<{ tid: string }>();
    const [form] = Form.useForm<PartialUpdateTemplateBody>();
    const navigate = useNavigate();

    const [testModalOpen, setTestModalOpen] = useState(false);

    const { isLoading, template: data } = useTemplate(tid as string);
    const { mutate: updateTemplate, isPending } = useUpdateTemplate();
    const { mutate: deleteTemplate } = useDeleteTemplate({
        onSuccess: () => {
            navigate("/crm/templates", { replace: true });
        },
    });

    const [fileList, setFileList] = useState<UploadFile<FileType>[]>([]);
    const [previewOpen, setPreviewOpen] = useState(false);

    useEffect(() => {
        if (data && data.headerImage) {
            const currentFile = {
                uid: data.headerImage.fid,
                name: data.headerImage.name,
                status: "done",
                url: data.headerImage.url,
            };
            setFileList([currentFile as UploadFile<FileType>]);
        }
    }, [data, form]);

    const handleSave = () => {
        form.submit();
    };

    // 1. Primero, usa Form.useWatch para observar los cambios en los campos
    const buttons = Form.useWatch("buttons", form);
    const templateType = Form.useWatch("type", form);

    const handleFormFinish = (values: PartialUpdateTemplateBody) => {
        if (data?.headerImage && fileList.length === 0) {
            values.deleteHeaderImage = true;
        }
        updateTemplate({
            tid: tid as string,
            data: values,
        });
    };

    const draggerProps: DraggerProps = {
        customRequest: async (options) => {
            const { file, onSuccess, onError } = options;
            try {
                if (typeof onSuccess === "function") {
                    const base64file = await getBase64(file as FileType);
                    const uploadFile: UploadFile<FileType> =
                        file as UploadFile<FileType>;
                    uploadFile.url = base64file;

                    form.setFieldsValue({
                        headerImageFile: [uploadFile],
                    });
                    setFileList([uploadFile]);
                    onSuccess(uploadFile);
                }
            } catch (error) {
                typeof onError === "function" && onError(error as Error);
            }
        },
        onRemove: () => {
            form.setFieldsValue({
                headerImageFile: undefined,
            });
            setFileList([]);
        },
        onPreview: async () => {
            setPreviewOpen(true);
        },
        fileList,
    };

    function setBodyText(value: string): void {
        form.setFieldsValue({ bodyText: value });
    }

    // function setButtons(value: TemplateButton[]): void {
    //     form.setFieldsValue({ buttons: value });
    // }

    function setTemplateType(value: string | string[] | undefined | null): void {
        console.log("setTemplateType", value);
        if (!value) {
            form.setFieldsValue({ type: undefined });
            return;
        }
        // Usando mode single, el valor es de tipo string
        form.setFieldsValue({ type: value as string });
    }

    function handleDelete() {
        if (!tid) return;

        Modal.confirm({
            title: "¿Está seguro que desea eliminar esta plantilla?",
            content: `La plantilla "${data?.name}" será eliminada permanentemente.`,
            okText: "Eliminar",
            okButtonProps: { danger: true },
            cancelText: "Cancelar",
            onOk() {
                deleteTemplate({ tid: tid as string });
            },
        });
    }

    return (
        <>
            <CrmLayout>
                <div className="max-w-7xl w-full h-full space-y-5">
                    <div className="flex justify-between items-center">
                        <WelcomeBar helperText="Gestiona aquí las plantillas para mensajes de WhatsApp" />
                    </div>
                    {isLoading ? (
                        <Spinner />
                    ) : (
                        <>
                            <div className="p-5 bg-white-full rounded-lg space-y-5 shadow-sm">
                                <div className="flex items-center">
                                    <Breadcrumb
                                        separator=">"
                                        items={[
                                            {
                                                title: (
                                                    <Link
                                                        to="/crm/templates"
                                                        className="text-base"
                                                    >
                                                        Plantillas
                                                    </Link>
                                                ),
                                            },
                                            {
                                                title: (
                                                    <Link
                                                        to={`/crm/templates/${tid}`}
                                                        className="text-base"
                                                    >
                                                        {data?.name}
                                                    </Link>
                                                ),
                                            },
                                            {
                                                title: (
                                                    <Text className="text-base">
                                                        Editar
                                                    </Text>
                                                ),
                                            },
                                        ]}
                                    />
                                </div>
                            </div>
                            <div className="space-y-5">
                                <Form
                                    name="templateEdit"
                                    layout="vertical"
                                    form={form}
                                    initialValues={{
                                        ...data,
                                        type: data?.type?.ttid,
                                    }}
                                    onFinish={handleFormFinish}
                                >
                                    <div className="grid grid-cols-1 lg:grid-cols-6 gap-y-6 lg:gap-6">
                                        <div className="bg-white-full col-span-4 p-5 rounded-lg shadow-sm">
                                            <p className="text-gray-400 font-semibold text-sm">
                                                INFORMACIÓN DE LA PLANTILLA
                                            </p>
                                            <Form.Item<PartialUpdateTemplateBody>
                                                name="name"
                                                label={
                                                    <span className="font-semibold text-base">
                                                        Nombre de la Plantilla
                                                    </span>
                                                }
                                                rules={[
                                                    {
                                                        required: true,
                                                        message:
                                                            "Por favor, ingrese el nombre de la plantilla",
                                                    },
                                                ]}
                                            >
                                                <Input
                                                    placeholder="Ej. Bienvenida al curso"
                                                    className="py-2"
                                                />
                                            </Form.Item>
                                            <Form.Item<PartialUpdateTemplateBody>
                                                name="type"
                                                label={
                                                    <span className="font-semibold text-base">
                                                        Tipo de plantilla
                                                    </span>
                                                }
                                            >
                                                <div>
                                                    <SelectTemplateTypes
                                                        value={form.getFieldValue(
                                                            "type",
                                                        )}
                                                        onChange={setTemplateType}
                                                        selectedTemplateType={
                                                            data?.type
                                                        }
                                                        allowClear
                                                    />
                                                    <span className="text-xs text-gray-500 mt-1">
                                                        Selecciona un tipo de plantilla
                                                        para poder usar{" "}
                                                        {"{{ variables }}"} en el
                                                        contenido del mensaje.
                                                    </span>
                                                </div>
                                            </Form.Item>
                                            <div>
                                                <p className="text-gray-400 font-semibold text-sm">
                                                    IMAGEN DE CABECERA (OPCIONAL)
                                                </p>
                                                <Form.Item<PartialUpdateTemplateBody>
                                                    name="headerImageFile"
                                                    label={
                                                        <span className="font-semibold text-base">
                                                            Imagen de cabecera
                                                        </span>
                                                    }
                                                    valuePropName="listFile"
                                                    getValueFromEvent={(e) => {
                                                        return e?.fileList;
                                                    }}
                                                >
                                                    <ImgCrop aspect={1 / 1}>
                                                        <Dragger
                                                            {...draggerProps}
                                                            listType="picture"
                                                            maxCount={1}
                                                            multiple={false}
                                                        >
                                                            <div className="flex flex-col justify-center items-center">
                                                                <CloudUpload />
                                                                <Text className="font-medium text-black-full">
                                                                    Arrastre una imagen
                                                                    o haga click aquí
                                                                </Text>
                                                                <Text className="text-xs text-black-medium">
                                                                    {
                                                                        "(dimensiones recomendadas 1200 x 1200 px)"
                                                                    }
                                                                </Text>
                                                            </div>
                                                        </Dragger>
                                                    </ImgCrop>
                                                </Form.Item>
                                                <Image
                                                    wrapperStyle={{ display: "none" }}
                                                    preview={{
                                                        visible: previewOpen,
                                                        onVisibleChange: setPreviewOpen,
                                                    }}
                                                    src={fileList[0]?.url}
                                                />
                                            </div>
                                            <Form.Item<PartialUpdateTemplateBody>
                                                name="bodyText"
                                                label={
                                                    <span className="font-semibold text-base">
                                                        Texto del mensaje
                                                    </span>
                                                }
                                                rules={[
                                                    {
                                                        max: 1024,
                                                        message:
                                                            "El texto no puede superar los 1024 caracteres",
                                                    },
                                                ]}
                                            >
                                                <WhatsappTemplateEditor
                                                    value={data?.bodyText || ""}
                                                    onChange={setBodyText}
                                                    maxLength={1024}
                                                    templateType={
                                                        templateType || undefined
                                                    }
                                                />
                                            </Form.Item>
                                            {/* TODO: Implement buttons backend logic */}
                                            {/* <Form.Item<PartialUpdateTemplateBody>
                                                name="buttons"
                                                label={
                                                    <span className="font-semibold text-base">
                                                        Botones (Opcional)
                                                    </span>
                                                }
                                            >
                                                <WhatsappButtonsEditor
                                                    onChange={(buttons) => {
                                                        setButtons(buttons);
                                                    }}
                                                    value={data?.buttons || []}
                                                />
                                            </Form.Item> */}
                                        </div>
                                        <div className="col-span-2 space-y-6">
                                            <div className="bg-white-full p-5 rounded-lg shadow-sm">
                                                <p className="text-gray-400 font-semibold text-sm">
                                                    Acciones
                                                </p>
                                                <div className="flex gap-3">
                                                    <Button
                                                        type="primary"
                                                        size="large"
                                                        style={{ fontSize: 16 }}
                                                        icon={<Save />}
                                                        disabled={isPending}
                                                        onClick={handleSave}
                                                    >
                                                        Guardar
                                                    </Button>
                                                    <Button
                                                        type="primary"
                                                        size="large"
                                                        style={{ fontSize: 16 }}
                                                        icon={<Trash />}
                                                        danger
                                                        disabled={isPending}
                                                        onClick={handleDelete}
                                                    >
                                                        Eliminar
                                                    </Button>
                                                </div>
                                            </div>
                                            <div className="bg-white-full p-5 rounded-lg shadow-sm">
                                                <p className="text-gray-400 font-semibold text-sm">
                                                    ESTADO DE LA PLANTILLA
                                                </p>
                                                <div className="mt-3">
                                                    <Form.Item<PartialUpdateTemplateBody>
                                                        name="status"
                                                        label={
                                                            <span className="font-semibold text-base">
                                                                Estado de la plantilla
                                                            </span>
                                                        }
                                                    >
                                                        <Select
                                                            className="w-full"
                                                            disabled={isPending}
                                                        >
                                                            {Object.values(
                                                                TemplateStatus,
                                                            ).map((status) => (
                                                                <Select.Option
                                                                    key={status}
                                                                    value={status}
                                                                >
                                                                    <div className="flex items-center gap-2">
                                                                        {
                                                                            TemplateStatusLabel[
                                                                                status
                                                                            ]
                                                                        }
                                                                    </div>
                                                                </Select.Option>
                                                            ))}
                                                        </Select>
                                                    </Form.Item>
                                                </div>
                                            </div>
                                            <div className="h-full">
                                                <div className="py-4 px-5 bg-white-full rounded-t-lg shadow-sm cursor-pointer flex items-center  justify-between">
                                                    <p className="text-gray-400 font-semibold text-sm">
                                                        VISTA PREVIA
                                                    </p>
                                                    <Tooltip title="Probar plantilla">
                                                        <Button
                                                            type="primary"
                                                            size="large"
                                                            style={{ fontSize: 16 }}
                                                            icon={<Send size={16} />}
                                                            disabled={isPending}
                                                            onClick={() =>
                                                                setTestModalOpen(true)
                                                            }
                                                        />
                                                    </Tooltip>
                                                </div>
                                                <WhatsAppMessagePreview
                                                    imageUrl={data?.headerImage?.url}
                                                    bodyText={
                                                        data?.parsedBodyText || ""
                                                    }
                                                    buttons={
                                                        buttons?.map(
                                                            (button) => button.text,
                                                        ) ||
                                                        data?.buttons?.map(
                                                            (button) => button.text,
                                                        )
                                                    }
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </Form>
                            </div>
                        </>
                    )}
                </div>
            </CrmLayout>
            {/* Modal test template form */}
            {tid && (
                <Modal
                    title={
                        <div className="w-full flex justify-center text-2xl py-4">
                            Enviar mensaje de prueba
                        </div>
                    }
                    className="max-w-4xl w-full"
                    open={testModalOpen}
                    onCancel={() => setTestModalOpen(false)}
                    footer={false}
                >
                    <TestTemplateForm
                        templateId={tid}
                        onFinish={() => setTestModalOpen(false)}
                        closeModal={() => setTestModalOpen(false)}
                    />
                </Modal>
            )}
        </>
    );
}
