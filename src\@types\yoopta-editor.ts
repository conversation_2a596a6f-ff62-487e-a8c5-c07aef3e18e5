// Define types for Yoopta content nodes
export interface YooptaTextNode {
    text?: string;
    bold?: boolean;
    italic?: boolean;
    underline?: boolean;
    code?: boolean;
    link?: string;
    highlight?: {
        color: string;
    };
}

export interface YooptaChildNode {
    id?: string;
    type?: string;
    children?: (YooptaTextNode | YooptaChildNode)[];
    text?: string;
    bold?: boolean;
    italic?: boolean;
    underline?: boolean;
    code?: boolean;
    props?: {
        url?: string;
        rel?: string;
        title?: string;
        target?: string;
        node_type?: string;
    };
}

// Type for Yoopta node value items
export interface YooptaValueItem {
    id: string;
    type: string;
    children: (YooptaChildNode | YooptaTextNode)[];
    props?: {
        node_type?: string;
        fit?: string;
        src?: string;
        alt?: string;
        sizes?: {
            width: number;
            height: number;
        };
        src_set?: null | string;
        bg_color?: string | null;
        url?: string;
        rel?: string;
        title?: string;
        target?: string;
    };
}

// Type for Yoopta node with the structure from the example
export interface YooptaNodeBase {
    id: string;
    meta: {
        depth: number;
        order: number;
        align?: string;
    };
    type: string;
    value: YooptaValueItem[];
}
