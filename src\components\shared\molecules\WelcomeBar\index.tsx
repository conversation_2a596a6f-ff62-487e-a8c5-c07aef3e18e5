import { useAuthStore } from "@store/authStore";
import { Typography } from "antd";

const { Text } = Typography;

export default function WelcomeBar({ helperText }: { helperText?: string }) {
    const { user } = useAuthStore((state) => state);
    return (
        <>
            <div>
                <h1 className="text-2xl text-black-full">
                    Bienvenido,{" "}
                    <span className="text-blue-full font-semibold">
                        {user?.firstName === "" ? "Usuario" : user?.firstName}
                    </span>
                </h1>
                <Text className="text-sm text-gray-500 font-medium">
                    {helperText
                        ? helperText
                        : "Gestiona aquí tus datos y preferencias."}
                </Text>
            </div>
        </>
    );
}
