import { Navigate, Outlet } from "react-router-dom";
import { hasModuleAccess, modulePermissions } from "@/core/config/permissions";
import { useAuthStore } from "@store/authStore";
import { PermissionKey } from "@/core/config/permissions-map";
import { canView } from "@lib/permissions";

type Props = {
    moduleKey: keyof typeof modulePermissions;
    redirectTo?: string;
};

export function ModuleProtectedRoute({ moduleKey, redirectTo = "/" }: Props) {
    const { isHydrated, isAuthenticated, user } = useAuthStore((state) => state);

    const userGroups = user?.groups || [];

    if (isHydrated && !isAuthenticated) {
        return <Navigate to={"/login"} />;
    }

    if (!hasModuleAccess(modulePermissions[moduleKey], userGroups)) {
        return <Navigate to={redirectTo} replace />;
    }

    return <Outlet />;
}

interface SubModelueProps {
    subModule: PermissionKey;
    redirectTo?: string;
}

export function SubModuleProtectedRoute({
    subModule,
    redirectTo = "/",
}: SubModelueProps) {
    const { isHydrated, isAuthenticated, user } = useAuthStore((state) => state);

    if (isHydrated && !isAuthenticated) {
        return <Navigate to={"/login"} />;
    }

    const userPermissions = user?.permissions || [];

    if (!canView(userPermissions, subModule)) {
        return <Navigate to={redirectTo} replace />;
    }

    return <Outlet />;
}
