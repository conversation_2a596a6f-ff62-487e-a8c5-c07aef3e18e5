import React, { useEffect, useRef } from "react";
import { Card, DatePicker, Select, Radio, Button, Form } from "antd";
import { Filter, RefreshCwIcon } from "lucide-react";
import locale from "antd/es/date-picker/locale/es_ES";
import dayjs, { Dayjs } from "dayjs";
import { useMutateSearchParams } from "@hooks/use-mutate-search-params";
import "dayjs/locale/es";
import SelectOfferings from "./select-offerings";

dayjs.locale("es");

const { RangePicker } = DatePicker;
const { Option } = Select;

export type PaymentStatusFilter = "all" | "paid" | "pending";
export type CurrencyFilter = "all" | "pen" | "usd"; // API uses lowercase
export type PaymentMethodFilter = "all" | string;
export type DateTypeFilter = "created_at" | "payment_date" | "scheduled_payment_date"; // API expects snake_case

// Estructura simplificada que coincide con los datos mock actuales
export type SimplePaymentMethod = {
    pmid: string;
    name: string;
};

interface FilterValues {
    dateRange?: [Dayjs, Dayjs] | null;
    dateType?: DateTypeFilter;
    paymentStatus?: PaymentStatusFilter;
    currency?: CurrencyFilter;
    paymentMethod?: PaymentMethodFilter;
    offerings?: string[];
}

/**
 * Estado de los filtros de pago para usar con search params
 */
export interface PaymentFiltersState {
    dateRange?: [Dayjs, Dayjs] | null;
    dateType: DateTypeFilter;
    paymentStatus: PaymentStatusFilter;
    currency: CurrencyFilter;
    paymentMethod: PaymentMethodFilter;
    offerings: string[];
}

/**
 * Props para el componente PaymentFilterCard
 * @property paymentMethods - Lista de métodos de pago disponibles
 */
interface PaymentFilterCardProps {
    paymentMethods: SimplePaymentMethod[];
}

/**
 * Componente de filtro para la sección de pagos
 * Permite filtrar por fecha, estado, moneda y método de pago
 */
const PaymentFilterCard: React.FC<PaymentFilterCardProps> = ({ paymentMethods }) => {
    const [form] = Form.useForm<FilterValues>();
    const { searchParams, mutateManySearchParams } = useMutateSearchParams();

    // Capturar valores iniciales
    const initialValuesRef = useRef<FilterValues | null>(null);

    if (initialValuesRef.current === null) {
        const values: FilterValues = {
            ...Object.fromEntries(searchParams.entries()),
        };

        // Parse dateType
        values.dateType =
            (searchParams.get("typeDate") as DateTypeFilter) || "created_at"; // Changed from dateType to typeDate

        // Parse paymentStatus
        values.paymentStatus =
            (searchParams.get("status") as PaymentStatusFilter) || "all"; // Changed from paymentStatus to status

        // Parse currency
        values.currency = (searchParams.get("currency") as CurrencyFilter) || "all";

        // Parse paymentMethod
        values.paymentMethod = searchParams.get("paymentMethod") || "all";

        // Parse offerings
        values.offerings = searchParams.get("offerings")?.split(",");

        // Date range
        const dateAfter = searchParams.get("dateAfter");
        const dateBefore = searchParams.get("dateBefore");
        if (dateAfter && dateBefore) {
            values.dateRange = [dayjs(dateAfter), dayjs(dateBefore)];
        } else {
            // Esta semana por defecto
            values.dateRange = [dayjs().startOf("week"), dayjs().endOf("week")];
        }

        initialValuesRef.current = values;
    }

    const getInitialValues = () => {
        if (!initialValuesRef.current) return {};
        return {
            ...initialValuesRef.current,
            dateRange: [
                initialValuesRef.current.dateRange?.[0] as Dayjs,
                initialValuesRef.current.dateRange?.[1] as Dayjs,
            ],
        };
    };

    // Establecer valores iniciales solo una vez
    useEffect(() => {
        if (initialValuesRef.current) {
            form.setFieldsValue(getInitialValues());
        }
    }, [form]);

    const handleApplyFilters = () => {
        const values = form.getFieldsValue();

        // Map form values to search params with correct names
        const newParams: Record<string, string | null> = {};

        // Date range
        if (values.dateRange) {
            newParams.dateAfter = values.dateRange[0].format("YYYY-MM-DD");
            newParams.dateBefore = values.dateRange[1].format("YYYY-MM-DD");
        }

        // Date type (map to typeDate param)
        if (values.dateType) {
            newParams.typeDate = values.dateType;
        }

        // Payment status (map to status param)
        if (values.paymentStatus && values.paymentStatus !== "all") {
            newParams.status = values.paymentStatus;
        } else {
            newParams.status = null; // Clear if "all"
        }

        // Currency
        if (values.currency && values.currency !== "all") {
            newParams.currency = values.currency;
        } else {
            newParams.currency = null; // Clear if "all"
        }

        // Payment method
        if (values.paymentMethod && values.paymentMethod !== "all") {
            newParams.paymentMethod = values.paymentMethod;
        } else {
            newParams.paymentMethod = null; // Clear if "all"
        }

        // Offerings
        if (values.offerings && values.offerings?.length > 0) {
            newParams.offerings = values.offerings.join(",");
        } else {
            newParams.offerings = null;
        }

        mutateManySearchParams(newParams);
    };

    const handleClearFilters = () => {
        // Valores por defecto: rango de fechas "esta semana"
        const defaultDateRange: [Dayjs, Dayjs] = [
            dayjs().startOf("week"),
            dayjs().endOf("week"),
        ];

        const defaultValues = {
            dateRange: defaultDateRange,
            dateType: "created_at" as DateTypeFilter,
            paymentStatus: "all" as PaymentStatusFilter,
            currency: "all" as CurrencyFilter,
            paymentMethod: "all" as PaymentMethodFilter,
        };

        // Limpiar los search params y resetear el formulario al mismo tiempo
        const emptyParams: Record<string, string | null> = {
            dateAfter: defaultDateRange[0].format("YYYY-MM-DD"),
            dateBefore: defaultDateRange[1].format("YYYY-MM-DD"),
            typeDate: "created_at", // Use correct API parameter name
            status: null, // Use status instead of paymentStatus
            currency: null,
            paymentMethod: null,
        };

        form.setFieldsValue(defaultValues);
        mutateManySearchParams(emptyParams);
    };

    return (
        <Card
            className="shadow-md mb-6"
            title={
                <div className="flex items-center">
                    <Filter className="mr-2 h-5 w-5 text-blue-500" />
                    <span>Filtrar pagos</span>
                </div>
            }
        >
            <Form
                form={form}
                layout="vertical"
                initialValues={undefined}
                onFinish={handleApplyFilters}
            >
                <div className="flex flex-wrap gap-4">
                    <div className="flex-1 min-w-[300px]">
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                            <Form.Item
                                name="dateType"
                                label="Tipo de fecha"
                                className="mb-0"
                            >
                                <Select className="w-full">
                                    <Option value="created_at">
                                        Fecha de creación
                                    </Option>
                                    <Option value="payment_date">Fecha de pago</Option>
                                    <Option value="scheduled_payment_date">
                                        Fecha de pago programado
                                    </Option>
                                </Select>
                            </Form.Item>
                            <Form.Item
                                name="dateRange"
                                label="Rango de fechas"
                                className="mb-0"
                            >
                                <RangePicker
                                    className="w-full"
                                    locale={locale}
                                    presets={[
                                        {
                                            label: "Hoy",
                                            value: [dayjs(), dayjs()],
                                        },
                                        {
                                            label: "Esta semana",
                                            value: [
                                                dayjs().startOf("week"),
                                                dayjs().endOf("week"),
                                            ],
                                        },
                                        {
                                            label: "Este mes",
                                            value: [
                                                dayjs().startOf("month"),
                                                dayjs().endOf("month"),
                                            ],
                                        },
                                        {
                                            label: "Último mes",
                                            value: [
                                                dayjs()
                                                    .subtract(1, "month")
                                                    .startOf("month"),
                                                dayjs()
                                                    .subtract(1, "month")
                                                    .endOf("month"),
                                            ],
                                        },
                                        {
                                            label: "Este año",
                                            value: [
                                                dayjs().startOf("year"),
                                                dayjs().endOf("year"),
                                            ],
                                        },
                                    ]}
                                />
                            </Form.Item>
                        </div>
                    </div>

                    <div className="flex flex-wrap gap-4 items-end">
                        <div className="min-w-[200px]">
                            <Form.Item
                                name="paymentStatus"
                                label="Estado"
                                className="mb-0"
                            >
                                <Radio.Group className="w-full">
                                    <Radio.Button value="all">Todos</Radio.Button>
                                    <Radio.Button value="paid">Pagados</Radio.Button>
                                    <Radio.Button value="pending">
                                        Pendientes
                                    </Radio.Button>
                                </Radio.Group>
                            </Form.Item>
                        </div>

                        <div className="min-w-[120px]">
                            <Form.Item name="currency" label="Moneda" className="mb-0">
                                <Select className="w-full" allowClear>
                                    <Option value="all">Todas</Option>
                                    <Option value="pen">Soles</Option>
                                    <Option value="usd">Dólares</Option>
                                </Select>
                            </Form.Item>
                        </div>

                        <div className="min-w-[180px]">
                            <Form.Item
                                name="paymentMethod"
                                label="Método de pago"
                                className="mb-0"
                            >
                                <Select className="w-full" allowClear>
                                    <Option value="all">Todos</Option>
                                    {paymentMethods.map((method) => (
                                        <Option key={method.pmid} value={method.pmid}>
                                            {method.name}
                                        </Option>
                                    ))}
                                </Select>
                            </Form.Item>
                        </div>

                        <div className="min-w-[300px]">
                            {/* Filter by offerings */}
                            <Form.Item
                                name="offerings"
                                label="Programas"
                                className="mb-0"
                            >
                                <SelectOfferings
                                    placeholder="Seleccionar"
                                    mode="multiple"
                                    maxTagCount={1}
                                    maxTagTextLength={22}
                                    className="w-full max-w-[300px] overflow-hidden"
                                    popupClassName="min-w-[500px]"
                                    allowClear
                                />
                            </Form.Item>
                        </div>

                        <div className="flex gap-2">
                            <Button
                                type="default"
                                icon={<RefreshCwIcon className="h-4 w-4" />}
                                onClick={handleClearFilters}
                            >
                                Limpiar filtros
                            </Button>
                            <Button type="primary" htmlType="submit">
                                Aplicar filtros
                            </Button>
                        </div>
                    </div>
                </div>
            </Form>
        </Card>
    );
};

export default PaymentFilterCard;
