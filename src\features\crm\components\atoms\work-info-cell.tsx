import { Typography, Tooltip } from "antd";
import { Briefcase, Users } from "lucide-react";
import { ContactListItem } from "@/features/crm/types/contact";

const { Text } = Typography;

type WorkInfoCellProps = {
    contact: ContactListItem;
    showLabels?: boolean;
};

const WorkInfoCell = ({ contact, showLabels = true }: WorkInfoCellProps) => {
    const { company, role } = contact;

    // If no work info exists, return a placeholder
    if (!company && !role) {
        return (
            <Text type="secondary" italic>
                No hay información laboral
            </Text>
        );
    }

    return (
        <div className="flex flex-col gap-1.5">
            {/* Company */}
            {company && (
                <div className="flex items-start gap-1.5">
                    <Users
                        size={14}
                        className="text-green-500 mt-0.5"
                        strokeWidth={1.75}
                    />
                    <div className="flex flex-col">
                        {showLabels && (
                            <Text className="text-xs text-gray-500 font-medium">
                                Empresa
                            </Text>
                        )}
                        <Tooltip title={company}>
                            <Text className="text-sm truncate max-w-[200px]">
                                {company}
                            </Text>
                        </Tooltip>
                    </div>
                </div>
            )}

            {/* Role/Position */}
            {role && (
                <div className="flex items-start gap-1.5">
                    <Briefcase
                        size={14}
                        className="text-green-500 mt-0.5"
                        strokeWidth={1.75}
                    />
                    <div className="flex flex-col">
                        {showLabels && (
                            <Text className="text-xs text-gray-500 font-medium">
                                Rol/Puesto
                            </Text>
                        )}
                        <Tooltip title={role}>
                            <Text className="text-sm truncate max-w-[200px]">
                                {role}
                            </Text>
                        </Tooltip>
                    </div>
                </div>
            )}
        </div>
    );
};

export default WorkInfoCell;
