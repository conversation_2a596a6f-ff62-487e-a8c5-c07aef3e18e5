type AuditBaseType = {
    created_at: string;
    updated_at: string;
};

export type Offering = {
    oid: string;
    name: string;
    slug: string;
    description?: string;
    thumbnail?: string;
    cover_image?: string;
} & AuditBaseType;

export type CreateOfferingFormBody = {
    name: string;
    slug: string;
    description?: string;
};

export type PaginatedResponse<T> = {
    items: T[];
    total: number;
    page: number;
    pageSize: number;
};
