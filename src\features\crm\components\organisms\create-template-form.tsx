import { Button, Form, Input, Switch } from "antd";
import { CreateTemplateFormValues, RetrieveTemplate } from "../../types/template";
import { useCreateTemplate } from "../../hooks/use-template";
import SelectTemplateTypes from "../molecules/select-template-type";
import FormLabel from "../atoms/FormLabel";

type CreateTemplateFormProps = {
    onFinish?: () => void;
    template?: RetrieveTemplate;
    closeModal?: () => void;
};

export default function CreateTemplateForm({
    onFinish,
    template,
    closeModal,
}: CreateTemplateFormProps) {
    const [createTemplateForm] = Form.useForm<CreateTemplateFormValues>();

    const onCreateTemplateSuccess = () => {
        createTemplateForm.resetFields();
        onFinish?.();
    };

    const onCreateTemplateError = () => {
        onFinish?.();
    };

    const { mutate: createTemplate } = useCreateTemplate({
        onCreateTemplateSuccess,
        onCreateTemplateError,
    });

    const handleOnFinish = (values: CreateTemplateFormValues) => {
        createTemplate(values);
    };

    const handleTypeChange = (value: string | string[] | undefined) => {
        if (!value) {
            createTemplateForm.setFieldsValue({ type: undefined });
            return;
        }
        createTemplateForm.setFieldsValue({ type: value as string });
    };

    return (
        <Form
            name="createTemplate"
            layout="vertical"
            form={createTemplateForm}
            onFinish={handleOnFinish}
            initialValues={{
                ...template,
                type: template?.type?.ttid,
            }}
        >
            <Form.Item<CreateTemplateFormValues>
                name="name"
                label={<span className="font-semibold text-base">Nombre</span>}
                rules={[
                    {
                        required: true,
                        message: "Por favor, ingrese el nombre del programa",
                    },
                ]}
            >
                <Input placeholder="Ej. Manejo de Software" className="py-1" />
            </Form.Item>
            <Form.Item<CreateTemplateFormValues>
                name={"type"}
                label={
                    <span className="font-semibold text-base">Tipo de plantilla</span>
                }
            >
                <SelectTemplateTypes
                    value={createTemplateForm.getFieldValue("type")}
                    onChange={handleTypeChange}
                    allowClear
                />
                <span className="text-xs text-gray-500 mt-1">
                    Selecciona un tipo de plantilla para poder usar {"{{ variables }}"}{" "}
                    en el contenido del mensaje.
                </span>
            </Form.Item>

            <Form.Item<CreateTemplateFormValues>
                name="completeInfo"
                layout="horizontal"
                tooltip="Si se activa esta opción, se le redirigirá a la vista de edición."
                label={
                    <FormLabel className="text-xs">¿Completar información?</FormLabel>
                }
            >
                <Switch size="small" />
            </Form.Item>

            <div className="grid grid-cols-2 gap-2">
                <Button onClick={() => closeModal?.()} className="h-fit" size="large">
                    Cancelar
                </Button>
                <Form.Item>
                    <Button
                        type="primary"
                        htmlType="submit"
                        className="h-fit"
                        size="large"
                        block
                    >
                        Guardar
                    </Button>
                </Form.Item>
            </div>
        </Form>
    );
}
