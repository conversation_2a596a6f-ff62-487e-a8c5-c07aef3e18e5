import { Link, useLocation } from "react-router-dom";
import { Menu } from "antd";
import type { MenuProps } from "antd";

import Dashboard from "@assets/icons/huge/dashboard.svg?react";
import ContactBook from "@assets/icons/huge/contact-book.svg?react";
import Lead from "@assets/icons/huge/lead.svg?react";

type MenuItem = Required<MenuProps>["items"][number];

const items: MenuItem[] = [
    {
        key: "general",
        label: <span className="text-black-medium font-semibold text-xs">GENERAL</span>,
        type: "group",
        children: [
            {
                key: "dashboard",
                label: <Link to="/">Dashboard</Link>,
                icon: <Dashboard />,
            },
        ],
    },
    {
        type: "divider",
    },
    {
        key: "rrhh-group",
        label: <span className="text-black-medium font-semibold text-xs">RR.HH</span>,
        type: "group",
        children: [
            {
                key: "staff",
                label: (
                    <Link to="/erp/staff" className="font-medium text-sm">
                        Colaboradores
                    </Link>
                ),
                icon: <ContactBook />,
            },
            {
                key: "Departments",
                label: (
                    <Link to="/erp/departments" className="font-medium text-sm">
                        Departamentos
                    </Link>
                ),
                icon: <Lead />,
            },
        ],
    },
];

const PATHS: Record<string, string> = {
    "/": "dashboard",
    "/staff": "staff",
    "/departments": "departments",
};

export default function ErpSideBar() {
    const location = useLocation();

    const selectedKey = PATHS[location.pathname];

    const onClick: MenuProps["onClick"] = (e) => {
        console.info("click ", e);
    };

    return (
        <Menu
            onClick={onClick}
            style={{ width: 256, border: 0 }}
            defaultSelectedKeys={[selectedKey]}
            defaultOpenKeys={[""]}
            mode="inline"
            items={items}
            className="hidden md:block min-w-64 sticky left-0 top-0"
        />
    );
}
