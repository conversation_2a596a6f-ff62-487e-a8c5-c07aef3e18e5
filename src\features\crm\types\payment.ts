import { UploadFile } from "antd";
import { Dayjs } from "dayjs";
import { OrderStage } from "./order";

export const DEFAULT_PAGE = 1;
export const DEFAULT_PAGE_SIZE = 20;

export type ListContactsQuery = {
    status: string;
};

export type ListPaymentsQueryParams = {
    page?: number;
    pageSize?: number;
    search?: string;
    status?: string;
    isPaid?: string;
    isLost?: string;
    order?: string;
    owner?: string;
    currency?: string;
    createdBy?: string;
    filterDateBy?: string; // comma-separated
    paymentMethod?: string;
    isFirstPayment?: boolean;
    minAmount?: number;
    maxAmount?: number;
    startDate?: string;
    endDate?: string;
};

export type PaymentOrder = {
    oid: string;
    stage: OrderStage;
    owner: {
        uid: string;
        fullName: string;
        email?: string;
        phoneNumber: string;
    };
};

export type PaymentVoucher = {
    fid: string;
    name: string;
    url: string;
    contentType: string;
};

export enum PaymentCurrency {
    USD = "usd",
    PEN = "pen",
}

export const PaymentCurrencyLabels: Record<PaymentCurrency, string> = {
    [PaymentCurrency.USD]: "Dólares",
    [PaymentCurrency.PEN]: "Soles",
};

type AuditBaseType = {
    createdAt: string;
    updatedAt: string;
};

export type PaymentListItem = AuditBaseType & {
    key: string;
    pid: string;
    order: PaymentOrder;
    paymentMethod: {
        pmid: string;
        name: string;
    };
    paymentDate: string;
    amount: number;
    currency: PaymentCurrency;
    isPaid: boolean;
    isLost: boolean;
    isFirstPayment: boolean;
    scheduledPaymentDate?: string;
};

// isPending : if not isPaid or isLost
export const PAYMENT_STATUSES = ["isPaid", "isPending", "isLost"];

export type PaymentRetrieve = AuditBaseType & {
    pid: string;
    order: PaymentOrder;
    paymentMethod?: {
        pmid: string;
        name: string;
    };
    isPaid: boolean;
    isLost: boolean;
    isFirstPayment: boolean;
    paymentDate?: string;
    scheduledPaymentDate?: string;
    voucher: PaymentVoucher;
    amount: number;
    currency: PaymentCurrency;
};

export type PaymentUpdateForm = {
    paymentMethod: string;
    amount: number;
    currency: PaymentCurrency;
    paymentDate: Dayjs;
    isFirstPayment: boolean;
    isLost: boolean;
    scheduledPaymentDate?: Dayjs;
};

export type PaymentCreateForm = {
    order: string;
    amount: number;
    currency: PaymentCurrency;
    isPaid: boolean;
    isFirstPayment?: boolean;

    scheduledPaymentDate?: Dayjs;
    voucher?: UploadFile[];
    paymentMethod?: string;
};

export type PaymentCreateRequest = {
    order: string;
    amount: number;
    currency: PaymentCurrency;
    isPaid: boolean;
    isFirstPayment?: boolean;
    scheduledPaymentDate?: string;
    voucher?: string;
    paymentMethod?: string;
};

export type PaymentUpdateRequest = {
    amount?: number;
    currency?: PaymentCurrency;
    isPaid?: boolean;
    isFirstPayment?: boolean;
    isLost?: boolean;
    paymentDate?: string;
    scheduledPaymentDate?: string;
    paymentMethod?: string;
};
