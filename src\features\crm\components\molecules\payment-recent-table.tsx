import { Card, Table, Tag, Skeleton, Button } from "antd";
import {
    Activity,
    ExternalLink,
    DollarSign,
    Calendar,
    User,
    CreditCard,
} from "lucide-react";
import dayjs from "dayjs";
import { useNavigate } from "react-router-dom";

import {
    useDashboardPaymentsLastPayments,
    createDashboardPaymentsQueryParams,
} from "../../hooks/use-dashboard-payments";
import type { DashboardLastPaymentItem } from "../../types/dashboard/payment";
import { useSearchParams } from "react-router-dom";

interface PaymentRecentTableProps {
    className?: string;
}

export default function PaymentRecentTable({ className }: PaymentRecentTableProps) {
    const [searchParams] = useSearchParams();
    const navigate = useNavigate();

    // Create query parameters for last payments data
    const queryParams = createDashboardPaymentsQueryParams(searchParams);

    // Fetch last payments data
    const { data: paymentsData, isLoading } =
        useDashboardPaymentsLastPayments(queryParams);

    const handleViewAllPayments = () => {
        navigate("/crm/payments");
    };

    // Payment columns for the recent payments table
    const paymentColumns = [
        {
            title: "CLIENTE",
            dataIndex: ["order", "owner"],
            key: "customer",
            width: 200,
            render: (owner: DashboardLastPaymentItem["order"]["owner"]) => (
                <div className="flex flex-col">
                    <span className="font-medium text-gray-900 truncate">
                        {owner.fullName}
                    </span>
                    <span className="text-xs text-gray-500 truncate">
                        {owner.email}
                    </span>
                    <span className="text-xs text-gray-400">{owner.phoneNumber}</span>
                </div>
            ),
        },
        {
            title: "FECHA DE PAGO",
            dataIndex: "paymentDate",
            key: "paymentDate",
            width: 130,
            render: (date: string) => (
                <div className="flex items-center text-sm">
                    <Calendar size={14} className="text-blue-500 mr-1" />
                    <div className="flex flex-col">
                        <span className="font-medium">
                            {dayjs(date).format("DD/MM/YYYY")}
                        </span>
                        <span className="text-xs text-gray-500">
                            {dayjs(date).format("HH:mm")}
                        </span>
                    </div>
                </div>
            ),
        },
        {
            title: "MÉTODO",
            dataIndex: "paymentMethod",
            key: "method",
            width: 120,
            render: (method: DashboardLastPaymentItem["paymentMethod"]) => (
                <div className="flex items-center">
                    <CreditCard size={14} className="text-purple-500 mr-1" />
                    <Tag color={method ? "blue" : "default"}>
                        {method?.name || "Sin método"}
                    </Tag>
                </div>
            ),
        },
        {
            title: "MONTO",
            key: "amount",
            width: 120,
            render: (record: DashboardLastPaymentItem) => (
                <div className="flex items-center">
                    <DollarSign size={16} className="text-green-600 mr-1" />
                    <div className="flex flex-col">
                        <span className="font-medium text-gray-900">
                            {record.currency === "usd" ? "$" : "S/. "}
                            {parseFloat(record.amount).toLocaleString()}
                        </span>
                        <span className="text-xs text-gray-500 uppercase">
                            {record.currency}
                        </span>
                    </div>
                </div>
            ),
        },
        {
            title: "ESTADO",
            key: "status",
            width: 100,
            render: (record: DashboardLastPaymentItem) => (
                <div className="flex flex-col items-start">
                    <Tag color={record.isPaid ? "success" : "warning"}>
                        {record.isPaid ? "Pagado" : "Pendiente"}
                    </Tag>
                    {record.isFirstPayment && (
                        <Tag color="purple" className="mt-1 text-xs">
                            Primer pago
                        </Tag>
                    )}
                </div>
            ),
        },
        {
            title: "CLIENTE",
            key: "customerInfo",
            width: 80,
            render: (record: DashboardLastPaymentItem) => (
                <div className="flex items-center">
                    <User size={14} className="text-gray-500 mr-1" />
                    <div className="flex flex-col">
                        <span className="text-xs text-gray-600 capitalize">
                            {record.order.stage}
                        </span>
                        <span className="text-xs text-gray-400">
                            {dayjs(record.createdAt).format("DD/MM")}
                        </span>
                    </div>
                </div>
            ),
        },
    ];

    return (
        <Card
            title={
                <div className="flex items-center justify-between">
                    <div className="flex items-center">
                        <Activity className="mr-2 h-5 w-5 text-blue-500" />
                        <span>Pagos Recientes</span>
                    </div>
                    <div className="flex items-center space-x-2">
                        <span className="text-sm text-gray-500">
                            Mostrando {paymentsData?.data?.length || 0} de{" "}
                            {paymentsData?.totalReturned || 0}
                        </span>
                        <Button
                            type="link"
                            size="small"
                            icon={<ExternalLink size={14} />}
                            onClick={handleViewAllPayments}
                        >
                            Ver todos
                        </Button>
                    </div>
                </div>
            }
            className={className}
        >
            {isLoading ? (
                <Skeleton active paragraph={{ rows: 8 }} />
            ) : (
                <div>
                    <Table
                        dataSource={paymentsData?.data || []}
                        columns={paymentColumns}
                        pagination={false}
                        size="small"
                        scroll={{ x: 800 }}
                        rowKey="pid"
                        className="recent-payments-table"
                    />

                    {(!paymentsData?.data || paymentsData.data.length === 0) &&
                        !isLoading && (
                            <div className="text-center py-8 text-gray-500">
                                <Activity
                                    size={48}
                                    className="mx-auto text-gray-300 mb-4"
                                />
                                <p>No hay pagos recientes disponibles</p>
                                <Button
                                    type="link"
                                    onClick={handleViewAllPayments}
                                    className="mt-2"
                                >
                                    Ir a la lista completa de pagos
                                </Button>
                            </div>
                        )}
                </div>
            )}
        </Card>
    );
}
