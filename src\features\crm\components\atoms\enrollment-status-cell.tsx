import { Tag, Tooltip } from "antd";
import { UserCheck, UserX, Users, AlertTriangle } from "lucide-react";

interface EnrollmentStatusCellProps {
    hasContact: boolean;
    needsConciliation: boolean;
    alreadyLead: boolean;
}

export default function EnrollmentStatusCell({
    hasContact,
    needsConciliation,
    alreadyLead,
}: EnrollmentStatusCellProps) {
    return (
        <div className="flex flex-col gap-1">
            {/* Contact Status */}
            <div className="flex items-center gap-1">
                {hasContact ? (
                    <Tooltip title="Tiene contacto registrado">
                        <Tag
                            color="green"
                            icon={<UserCheck className="w-3 h-3" />}
                            className="flex items-center gap-1 text-xs"
                        >
                            Con Contacto
                        </Tag>
                    </Tooltip>
                ) : (
                    <Tooltip title="No tiene contacto registrado">
                        <Tag
                            color="red"
                            icon={<UserX className="w-3 h-3" />}
                            className="flex items-center gap-1 text-xs"
                        >
                            Sin Contacto
                        </Tag>
                    </Tooltip>
                )}
            </div>

            {/* Additional Status */}
            <div className="flex flex-wrap gap-1">
                {needsConciliation && (
                    <Tooltip title="Necesita conciliación">
                        <Tag
                            color="orange"
                            icon={<AlertTriangle className="w-3 h-3" />}
                            className="flex items-center gap-1 text-xs"
                        >
                            Conciliación
                        </Tag>
                    </Tooltip>
                )}

                {alreadyLead && (
                    <Tooltip title="Ya es un lead">
                        <Tag
                            color="blue"
                            icon={<Users className="w-3 h-3" />}
                            className="flex items-center gap-1 text-xs"
                        >
                            Lead
                        </Tag>
                    </Tooltip>
                )}
            </div>
        </div>
    );
}
