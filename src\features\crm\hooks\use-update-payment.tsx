import { useMutation, useQueryClient } from "@tanstack/react-query";
import { App } from "antd";
import { updatePayment } from "../services/portals/payment";
import { PaymentUpdateRequest } from "../types/payment";

export const useUpdatePayment = () => {
    const queryClient = useQueryClient();
    const { notification } = App.useApp();

    return useMutation({
        mutationFn: ({
            pid,
            payment,
        }: {
            pid: string;
            payment: PaymentUpdateRequest;
        }) => updatePayment(pid, payment),
        onSuccess: () => {
            notification.success({
                message: "Pago actualizado",
                description: "El pago se ha actualizado correctamente",
                duration: 3,
            });
            // Invalidate both the specific payment and the payments list
            queryClient.invalidateQueries({ queryKey: ["payments"] });
            queryClient.invalidateQueries({ queryKey: ["payment"] });
        },
        onError: (error: Error) => {
            notification.error({
                message: "Error al actualizar el pago",
                description: error.message || "Ha ocurrido un error inesperado",
                duration: 5,
            });
        },
    });
};
