import { useNavigate, useParams } from "react-router-dom";
import { Button, Form, message, Typography } from "antd";
import { useMutation, useQuery } from "@tanstack/react-query";

const { Title } = Typography;

import CmsLayout from "@layouts/cms/CmsLayout";
import WelcomeBar from "@components/shared/molecules/WelcomeBar";
import Spinner from "@components/shared/atoms/Spinner";
import { retrieveBlogTag, updateBlogTag } from "@services/portals/cms/blogs/tag";
import { BlogTag, UpdateBlogTagBody } from "@myTypes/blog";
import { onErrorMessage, onSuccessMessage } from "@lib/message";
import BlogTagForm from "@components/cms/molecules/BlogTagForm";
import { detectChanges } from "@lib/form-helpers";

export default function BlogTagEditPage() {
    const [messageApi, messageContextHolder] = message.useMessage();
    const { id } = useParams<{ id: string }>();
    const navigate = useNavigate();
    const [form] = Form.useForm<UpdateBlogTagBody>();

    const { data, isLoading, refetch } = useQuery({
        queryKey: ["blogTag", id],
        queryFn: () => retrieveBlogTag(id!),
        enabled: !!id,
    });

    const updateMutation = useMutation({
        mutationFn: (values: UpdateBlogTagBody) => updateBlogTag(id!, values),
        onSuccess: () => {
            refetch();
            onSuccessMessage("Etiqueta actualizada correctamente", messageApi);
        },
        onError: () => {
            onErrorMessage("Error al actualizar la etiqueta", messageApi);
        },
    });

    const handleFormFinish = (values: UpdateBlogTagBody) => {
        if (!data || !id) return;

        const fieldsToCompare: (keyof BlogTag)[] = [
            "name",
            "slug",
            "description",
            "badgeColor",
        ];

        if (detectChanges({ data, values, fieldsToCompare })) {
            updateMutation.mutate(values);
        }
    };

    return (
        <>
            {messageContextHolder}
            <CmsLayout>
                <div className="w-full h-full space-y-5">
                    <WelcomeBar helperText="Visualiza & Edita los campos necesarios" />

                    <div className="bg-white-full p-5 rounded-lg shadow-sm">
                        <div className="flex justify-between items-center mb-5">
                            <Title level={4} className="m-0">
                                Editar etiqueta
                            </Title>
                        </div>

                        {isLoading ? (
                            <div className="flex justify-center items-center h-64">
                                <Spinner />
                            </div>
                        ) : (
                            <>
                                <BlogTagForm
                                    form={form}
                                    initialValues={data || undefined}
                                    onFinish={handleFormFinish}
                                />
                                <div className="flex justify-end gap-3 mt-4">
                                    <Button
                                        size="large"
                                        onClick={() => navigate("/cms/blog/tags")}
                                    >
                                        Cancelar
                                    </Button>
                                    <Button
                                        type="primary"
                                        size="large"
                                        onClick={() => form.submit()}
                                        loading={updateMutation.isPending}
                                    >
                                        Guardar
                                    </Button>
                                </div>
                            </>
                        )}
                    </div>
                </div>
            </CmsLayout>
        </>
    );
}
