import { <PERSON><PERSON>, Date<PERSON>icker, Drawer, Radio, Select, Space, InputNumber } from "antd";
import {
    PAYMENT_STATUSES,
    PaymentCurrency,
    PaymentCurrencyLabels,
} from "@/features/crm/types/payment";
import { useSearchParams } from "react-router-dom";
import { useCallback, useEffect, useState } from "react";
import dayjs, { Dayjs } from "dayjs";
import "dayjs/locale/es";
import SelectStaffUser from "../molecules/select-staff-user";
import SelectPaymentMethod from "../molecules/select-payment-method";

dayjs.locale("es");

// Opciones para filtro de fecha
const dateFilterOptions = [
    { value: "createdAt", label: "Fecha de creación" },
    { value: "paymentDate", label: "Fecha de pago" },
    { value: "scheduledPaymentDate", label: "Fecha programada" },
];

interface PaymentsFiltersProps {
    isOpen: boolean;
    onClose: () => void;
}

export default function PaymentsFilters({ isOpen, onClose }: PaymentsFiltersProps) {
    const [searchParams, setSearchParams] = useSearchParams();

    // Local filter states
    const [selectedStatuses, setSelectedStatuses] = useState<string[]>([]);
    const [selectedCurrency, setSelectedCurrency] = useState<string>("all");
    const [selectedCreatedBy, setSelectedCreatedBy] = useState<string>("");
    const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string>("");
    const [isFirstPayment, setIsFirstPayment] = useState<string>("all");
    const [amountRange, setAmountRange] = useState<{ min?: number; max?: number }>({});
    const [dateRange, setDateRange] = useState<[Dayjs, Dayjs] | null>(null);
    const [selectedDateFilters, setSelectedDateFilters] = useState<string[]>([
        "createdAt",
    ]);

    // Initialize filters from URL
    useEffect(() => {
        const statuses = searchParams.get("status");
        const currency = searchParams.get("currency") || "all";
        const startDate = searchParams.get("startDate");
        const endDate = searchParams.get("endDate");
        const dateFilters = searchParams.get("filterDateBy");
        const createdBy = searchParams.get("createdBy");
        const paymentMethod = searchParams.get("paymentMethod");
        const firstPayment = searchParams.get("isFirstPayment") || "all";
        const minAmount = searchParams.get("minAmount");
        const maxAmount = searchParams.get("maxAmount");

        if (statuses) {
            setSelectedStatuses(statuses.split(","));
        } else {
            setSelectedStatuses(PAYMENT_STATUSES);
        }

        setSelectedCurrency(currency);

        if (startDate && endDate) {
            setDateRange([dayjs(startDate), dayjs(endDate)]);
        }

        if (dateFilters) {
            setSelectedDateFilters(dateFilters.split(","));
        } else {
            setSelectedDateFilters(["createdAt"]);
        }

        if (createdBy) {
            setSelectedCreatedBy(createdBy);
        } else {
            setSelectedCreatedBy("");
        }

        if (paymentMethod) {
            setSelectedPaymentMethod(paymentMethod);
        } else {
            setSelectedPaymentMethod("");
        }

        setIsFirstPayment(firstPayment);

        setAmountRange({
            min: minAmount ? Number(minAmount) : undefined,
            max: maxAmount ? Number(maxAmount) : undefined,
        });
    }, [searchParams]);

    const handleApplyFilters = useCallback(() => {
        setSearchParams((prev) => {
            // Clear previous filters
            prev.delete("status");
            prev.delete("currency");
            prev.delete("startDate");
            prev.delete("endDate");
            prev.delete("filterDateBy");
            prev.delete("createdBy");
            prev.delete("paymentMethod");
            prev.delete("isFirstPayment");
            prev.delete("minAmount");
            prev.delete("maxAmount");

            // Apply status filters (como string separado por comas)
            if (
                selectedStatuses.length > 0 &&
                selectedStatuses.length < PAYMENT_STATUSES.length
            ) {
                prev.set("status", selectedStatuses.join(","));
            }

            // Apply currency filter
            if (selectedCurrency !== "all") {
                prev.set("currency", selectedCurrency);
            }

            if (dateRange && dateRange[0] && dateRange[1]) {
                prev.set("startDate", dateRange[0].format("YYYY-MM-DD"));
                prev.set("endDate", dateRange[1].format("YYYY-MM-DD"));

                // Add date filter types
                if (selectedDateFilters.length > 0) {
                    prev.set("filterDateBy", selectedDateFilters.join(","));
                }
            }

            if (selectedCreatedBy) {
                prev.set("createdBy", selectedCreatedBy);
            }

            if (selectedPaymentMethod) {
                prev.set("paymentMethod", selectedPaymentMethod);
            }

            if (isFirstPayment !== "all") {
                prev.set("isFirstPayment", isFirstPayment);
            }

            if (amountRange.min !== undefined) {
                prev.set("minAmount", amountRange.min.toString());
            }

            if (amountRange.max !== undefined) {
                prev.set("maxAmount", amountRange.max.toString());
            }

            // Reset to page 1
            prev.set("page", "1");

            return prev;
        });
        onClose();
    }, [
        selectedStatuses,
        selectedCurrency,
        dateRange,
        selectedDateFilters,
        selectedCreatedBy,
        selectedPaymentMethod,
        isFirstPayment,
        amountRange,
        setSearchParams,
        onClose,
    ]);

    const handleClearFilters = useCallback(() => {
        setSelectedStatuses(PAYMENT_STATUSES);
        setSelectedCurrency("all");
        setDateRange(null);
        setSelectedDateFilters(["createdAt"]);
        setSelectedCreatedBy("");
        setSelectedPaymentMethod("");
        setIsFirstPayment("all");
        setAmountRange({});

        setSearchParams((prev) => {
            prev.delete("status");
            prev.delete("currency");
            prev.delete("startDate");
            prev.delete("endDate");
            prev.delete("filterDateBy");
            prev.delete("createdBy");
            prev.delete("paymentMethod");
            prev.delete("isFirstPayment");
            prev.delete("minAmount");
            prev.delete("maxAmount");
            prev.set("page", "1");
            return prev;
        });
        onClose();
    }, [setSearchParams, onClose]);

    return (
        <Drawer
            title="Aplicar filtros"
            placement="right"
            closable={true}
            onClose={onClose}
            open={isOpen}
            width={480}
        >
            <div className="space-y-4">
                <div>
                    <h4 className="font-medium mb-2">Creado por</h4>
                    <SelectStaffUser
                        className="w-full"
                        placeholder="Seleccionar usuario"
                        onChange={(value) => setSelectedCreatedBy(value)}
                        value={selectedCreatedBy}
                    />
                </div>

                <div>
                    <h4 className="font-medium mb-2">Moneda</h4>
                    <Radio.Group
                        value={selectedCurrency}
                        onChange={(e) => setSelectedCurrency(e.target.value)}
                    >
                        <Space direction="horizontal">
                            <Radio value="all">Todas</Radio>
                            <Radio value={PaymentCurrency.USD}>
                                {PaymentCurrencyLabels[PaymentCurrency.USD]} (USD)
                            </Radio>
                            <Radio value={PaymentCurrency.PEN}>
                                {PaymentCurrencyLabels[PaymentCurrency.PEN]} (PEN)
                            </Radio>
                        </Space>
                    </Radio.Group>
                </div>

                <div>
                    <h4 className="font-medium mb-2">Primer pago</h4>
                    <Radio.Group
                        value={isFirstPayment}
                        onChange={(e) => setIsFirstPayment(e.target.value)}
                    >
                        <Space direction="horizontal">
                            <Radio value="all">Todos</Radio>
                            <Radio value="true">Sí</Radio>
                            <Radio value="false">No</Radio>
                        </Space>
                    </Radio.Group>
                </div>

                <div>
                    <h4 className="font-medium mb-2">Método de pago</h4>
                    <SelectPaymentMethod
                        className="w-full"
                        placeholder="Seleccionar método"
                        onChange={(value) => setSelectedPaymentMethod(value)}
                        value={selectedPaymentMethod}
                    />
                </div>

                <div>
                    <h4 className="font-medium mb-2">Rango de fecha</h4>
                    <div className="space-y-3">
                        <div>
                            <label className="text-sm text-gray-600 mb-1 block">
                                Filtrar por tipo de fecha
                            </label>
                            <Select
                                mode="multiple"
                                style={{ width: "100%" }}
                                placeholder="Seleccionar tipos de fecha"
                                value={selectedDateFilters}
                                onChange={setSelectedDateFilters}
                                options={dateFilterOptions}
                                allowClear={false}
                            />
                        </div>
                        <div>
                            <label className="text-sm text-gray-600 mb-1 block">
                                Seleccionar rango
                            </label>
                            <DatePicker.RangePicker
                                style={{ width: "100%" }}
                                placeholder={["Fecha inicio", "Fecha fin"]}
                                value={dateRange}
                                onChange={(dates) =>
                                    setDateRange(dates as [Dayjs, Dayjs] | null)
                                }
                                presets={[
                                    {
                                        label: "Hoy",
                                        value: [
                                            dayjs().startOf("day"),
                                            dayjs().endOf("day"),
                                        ],
                                    },
                                    {
                                        label: "Esta semana",
                                        value: [
                                            dayjs().startOf("week"),
                                            dayjs().endOf("week"),
                                        ],
                                    },
                                    {
                                        label: "Este mes",
                                        value: [
                                            dayjs().startOf("month"),
                                            dayjs().endOf("month"),
                                        ],
                                    },
                                    {
                                        label: "Último mes",
                                        value: [
                                            dayjs()
                                                .subtract(1, "month")
                                                .startOf("month"),
                                            dayjs().subtract(1, "month").endOf("month"),
                                        ],
                                    },
                                    {
                                        label: "Este año",
                                        value: [
                                            dayjs().startOf("year"),
                                            dayjs().endOf("year"),
                                        ],
                                    },
                                ]}
                            />
                        </div>
                    </div>
                </div>

                <div>
                    <h4 className="font-medium mb-2">Rango de monto</h4>
                    <div className="flex items-center gap-2">
                        <div>
                            <label className="text-sm text-gray-600 mb-1 block">
                                Monto mínimo
                            </label>
                            <InputNumber
                                style={{ width: "100%" }}
                                placeholder="Monto mínimo"
                                value={amountRange.min}
                                onChange={(value) =>
                                    setAmountRange((prev) => ({
                                        ...prev,
                                        min: value || undefined,
                                    }))
                                }
                                min={0}
                                precision={2}
                            />
                        </div>
                        <div>
                            <label className="text-sm text-gray-600 mb-1 block">
                                Monto máximo
                            </label>
                            <InputNumber
                                style={{ width: "100%" }}
                                placeholder="Monto máximo"
                                value={amountRange.max}
                                onChange={(value) =>
                                    setAmountRange((prev) => ({
                                        ...prev,
                                        max: value || undefined,
                                    }))
                                }
                                min={0}
                                precision={2}
                            />
                        </div>
                    </div>
                </div>

                <div className="pt-4 space-y-2">
                    <Button type="primary" block onClick={handleApplyFilters}>
                        Aplicar filtros
                    </Button>
                    <Button block onClick={handleClearFilters}>
                        Limpiar filtros
                    </Button>
                </div>
            </div>
        </Drawer>
    );
}
