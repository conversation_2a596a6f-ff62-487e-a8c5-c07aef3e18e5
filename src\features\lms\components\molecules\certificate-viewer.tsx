import { useState, useRef, useEffect } from "react";
import { Spin, Button, Typography } from "antd";
import { ZoomIn, ZoomOut, Download } from "lucide-react";
import { pdfjs, Document, Page } from "react-pdf";
import "react-pdf/dist/Page/AnnotationLayer.css";
import "react-pdf/dist/Page/TextLayer.css";

pdfjs.GlobalWorkerOptions.workerSrc = "/pdf.worker.min.js";

const { Text } = Typography;

interface CertificateViewerProps {
    fileUrl: string;
    fileName?: string;
}

export default function CertificateViewer({
    fileUrl,
    fileName = "Certificado",
}: CertificateViewerProps) {
    const [numPages, setNumPages] = useState<number | null>(null);
    const [currentPage, setCurrentPage] = useState(1);
    const [scale, setScale] = useState(1);
    const [containerWidth, setContainerWidth] = useState(0);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const containerRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        if (!containerRef.current) return;

        const updateWidth = () => {
            if (containerRef.current) {
                setContainerWidth(containerRef.current.clientWidth - 32); // 32px for padding
            }
        };

        const resizeObserver = new ResizeObserver(updateWidth);
        resizeObserver.observe(containerRef.current);
        updateWidth(); // Initial call

        return () => resizeObserver.disconnect();
    }, []);

    const onDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {
        setNumPages(numPages);
        setIsLoading(false);
        setError(null);
    };

    const onDocumentLoadError = (error: Error) => {
        console.error("Error loading PDF:", error);
        setError("Error al cargar el documento PDF");
        setIsLoading(false);
    };

    const goToPrevPage = () => {
        setCurrentPage((prev) => Math.max(prev - 1, 1));
    };

    const goToNextPage = () => {
        setCurrentPage((prev) => Math.min(prev + 1, numPages || 1));
    };

    const zoomIn = () => {
        setScale((prev) => Math.min(prev + 0.2, 3));
    };

    const zoomOut = () => {
        setScale((prev) => Math.max(prev - 0.2, 0.5));
    };

    const resetZoom = () => {
        setScale(1);
    };

    if (error) {
        return (
            <div className="flex flex-col items-center justify-center h-full p-8 text-center">
                <Text className="text-red-600 font-medium">{error}</Text>
                <Text className="text-gray-500 text-sm mt-2">
                    Intenta descargar el archivo o contacta con soporte técnico
                </Text>
                <Button
                    type="primary"
                    href={fileUrl}
                    target="_blank"
                    className="mt-4"
                    icon={<Download size={16} />}
                >
                    Descargar PDF
                </Button>
            </div>
        );
    }

    return (
        <div ref={containerRef} className="h-full flex flex-col">
            {/* Controls */}
            <div className="flex items-center justify-between p-3 bg-gray-50 border-b border-gray-200 rounded-t-lg">
                <div className="flex items-center gap-2">
                    <Button
                        size="small"
                        onClick={goToPrevPage}
                        disabled={currentPage <= 1}
                    >
                        ←
                    </Button>
                    <Text className="text-sm font-medium">
                        {numPages ? `${currentPage} / ${numPages}` : ""}
                    </Text>
                    <Button
                        size="small"
                        onClick={goToNextPage}
                        disabled={currentPage >= (numPages || 1)}
                    >
                        →
                    </Button>
                </div>

                <div className="flex items-center gap-2">
                    <Button size="small" onClick={zoomOut} disabled={scale <= 0.5}>
                        <ZoomOut size={14} />
                    </Button>
                    <Text className="text-xs text-gray-600 min-w-[40px] text-center">
                        {Math.round(scale * 100)}%
                    </Text>
                    <Button size="small" onClick={zoomIn} disabled={scale >= 3}>
                        <ZoomIn size={14} />
                    </Button>
                    <Button size="small" onClick={resetZoom}>
                        Ajustar
                    </Button>
                </div>

                <Button
                    size="small"
                    type="primary"
                    href={fileUrl}
                    target="_blank"
                    icon={<Download size={14} />}
                >
                    Descargar
                </Button>
            </div>

            {/* PDF Viewer */}
            <div className="flex-1 overflow-auto bg-gray-100 p-4">
                {isLoading && (
                    <div className="flex flex-col items-center justify-center h-full">
                        <Spin size="large" />
                        <Text className="mt-4 text-gray-500">
                            Cargando previsualización...
                        </Text>
                    </div>
                )}

                <div className="flex justify-center">
                    <Document
                        file={fileUrl}
                        onLoadSuccess={onDocumentLoadSuccess}
                        onLoadError={onDocumentLoadError}
                        loading={null}
                    >
                        <div className="shadow-lg">
                            <Page
                                pageNumber={currentPage}
                                scale={scale}
                                width={
                                    containerWidth > 0
                                        ? Math.min(containerWidth, 800)
                                        : undefined
                                }
                                renderTextLayer={false}
                                renderAnnotationLayer={false}
                            />
                        </div>
                    </Document>
                </div>
            </div>

            {/* Footer Info */}
            {numPages && (
                <div className="p-2 bg-gray-50 border-t border-gray-200 rounded-b-lg text-center">
                    <Text className="text-xs text-gray-500">
                        {fileName} • {numPages} página{numPages > 1 ? "s" : ""}
                    </Text>
                </div>
            )}
        </div>
    );
}
