import { Typography, Tooltip } from "antd";
import { Package, ExternalLink } from "lucide-react";
import { Link } from "react-router-dom";
import { EnrollmentOffering, OrderItem } from "../../types/enrollment";

const { Text } = Typography;

interface EnrollmentOfferingCellProps {
    offering: EnrollmentOffering;
    orderItem?: OrderItem;
}

export default function EnrollmentOfferingCell({
    offering,
    orderItem,
}: EnrollmentOfferingCellProps) {
    const { name, longName, codeName } = offering;
    const displayName = longName || name;
    const truncatedName =
        displayName.length > 40 ? `${displayName.slice(0, 40)}...` : displayName;

    return (
        <div className="space-y-2">
            <div className="flex items-center gap-2">
                <Package size={14} className="text-blue-600" />
                <Tooltip title={displayName.length > 40 ? displayName : undefined}>
                    <Text className="font-medium text-sm text-gray-900">
                        {truncatedName}
                    </Text>
                </Tooltip>
                {codeName && (
                    <Text className="text-xs text-gray-500">({codeName})</Text>
                )}
            </div>

            {orderItem && (
                <div className="flex items-center justify-between">
                    <Text className="text-xs text-gray-600">
                        Orden: #{orderItem.order.slice(-6)}
                    </Text>

                    <Tooltip title="Ver orden completa">
                        <Link
                            to={`/crm/orders/${orderItem.order}`}
                            className="flex items-center gap-1 text-blue-600 hover:text-blue-700 text-xs"
                            onClick={(e) => e.stopPropagation()}
                        >
                            <ExternalLink size={12} />
                        </Link>
                    </Tooltip>
                </div>
            )}
        </div>
    );
}
