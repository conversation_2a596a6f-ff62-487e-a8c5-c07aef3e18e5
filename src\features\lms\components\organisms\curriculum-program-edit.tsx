import { useState, useEffect, useCallback, useMemo } from "react";
import { useSearchParams } from "react-router-dom";
import {
    Button,
    Typography,
    Space,
    Collapse,
    Input,
    Form,
    Modal,
    List,
    Tag,
    Popconfirm,
    Empty,
    Tooltip,
} from "antd";
import {
    Plus,
    BookOpen,
    Edit3,
    Trash2,
    GripVertical,
    FileText,
    XIcon,
    ChevronDown,
    ChevronUp,
} from "lucide-react";

import type { RetrieveProgram } from "@/features/lms/types/program";
import type { Course, Module, Topic } from "@myTypes/offering";
import { useUpdateProgram } from "../../hooks/use-program";

const { Title, Text } = Typography;
const { Panel } = Collapse;

interface CurriculumProgramEditProps {
    oid: string;
    data: RetrieveProgram;
    onRefetch: () => void;
}

interface ModuleFormValues {
    title: string;
}

interface CourseFormValues {
    title: string;
}

interface TopicFormValues {
    title: string;
}

export default function CurriculumProgramEdit({
    oid,
    data,
}: CurriculumProgramEditProps) {
    const [searchParams, setSearchParams] = useSearchParams();
    const [modules, setModules] = useState<Module[]>(data.modules || []);
    const [isModuleModalOpen, setIsModuleModalOpen] = useState(false);
    const [isCourseModalOpen, setIsCourseModalOpen] = useState(false);
    const [isTopicModalOpen, setIsTopicModalOpen] = useState(false);

    const { mutate: updateProgram, isPending: isUpdatingProgram } = useUpdateProgram();

    // Parse expanded modules from URL params
    const expandedModules = useMemo(() => {
        const expanded = searchParams.get("expanded");
        if (!expanded) return new Set<string>();
        try {
            return new Set(JSON.parse(expanded));
        } catch {
            return new Set<string>();
        }
    }, [searchParams]);

    // Update URL params when expanded modules change
    const updateExpandedModules = useCallback(
        (newExpanded: Set<string>) => {
            const params = new URLSearchParams(searchParams);
            if (newExpanded.size > 0) {
                params.set("expanded", JSON.stringify([...newExpanded]));
            } else {
                params.delete("expanded");
            }
            setSearchParams(params);
        },
        [searchParams, setSearchParams],
    );

    // Expand all modules
    const expandAll = useCallback(() => {
        const allModuleIds = modules.map(
            (module, index) => module.omid || `module-${index}`,
        );
        updateExpandedModules(new Set(allModuleIds));
    }, [modules, updateExpandedModules]);

    // Collapse all modules
    const collapseAll = useCallback(() => {
        updateExpandedModules(new Set());
    }, [updateExpandedModules]);

    const [editingModule, setEditingModule] = useState<
        (Module & { index: number }) | null
    >(null);

    const [editingCourse, setEditingCourse] = useState<{
        moduleIndex: number;
        course: Course | null;
        courseIndex?: number;
    }>({ moduleIndex: -1, course: null });

    const [editingTopic, setEditingTopic] = useState<{
        moduleIndex: number;
        courseIndex: number;
        topic: Topic | null;
        topicIndex?: number;
    }>({ moduleIndex: -1, courseIndex: -1, topic: null });

    const [moduleForm] = Form.useForm<ModuleFormValues>();
    const [courseForm] = Form.useForm<CourseFormValues>();
    const [topicForm] = Form.useForm<TopicFormValues>();

    // Auto-focus on modal inputs when opened
    useEffect(() => {
        if (isModuleModalOpen) {
            setTimeout(() => {
                const input = document.querySelector(
                    "#module-title-input",
                ) as HTMLInputElement;
                if (input) {
                    input.focus();
                    input.select();
                }
            }, 100);
        }
    }, [isModuleModalOpen]);

    useEffect(() => {
        if (isCourseModalOpen) {
            setTimeout(() => {
                const input = document.querySelector(
                    "#course-title-input",
                ) as HTMLInputElement;
                if (input) {
                    input.focus();
                    input.select();
                }
            }, 100);
        }
    }, [isCourseModalOpen]);

    useEffect(() => {
        if (isTopicModalOpen) {
            setTimeout(() => {
                const input = document.querySelector(
                    "#topic-title-input",
                ) as HTMLInputElement;
                if (input) {
                    input.focus();
                    input.select();
                }
            }, 100);
        }
    }, [isTopicModalOpen]);

    // Module handlers
    const handleAddModule = () => {
        setEditingModule(null);
        moduleForm.resetFields();
        setIsModuleModalOpen(true);
    };

    const handleEditModule = (module: Module, index: number) => {
        setEditingModule({ ...module, index });
        moduleForm.setFieldsValue({ title: module.title });
        setIsModuleModalOpen(true);
    };

    const handleModuleSubmit = (values: ModuleFormValues) => {
        if (editingModule && "index" in editingModule) {
            // Edit existing module
            const newModules = [...modules];
            newModules[editingModule.index] = {
                ...newModules[editingModule.index],
                title: values.title,
            };
            setModules(newModules);
        } else {
            // Add new module
            const newModule: Module = {
                omid: `temp-${Date.now()}`,
                title: values.title,
                courses: [],
            };
            setModules([...modules, newModule]);
        }
        setIsModuleModalOpen(false);
        setEditingModule(null);
    };

    const handleDeleteModule = (index: number) => {
        const newModules = modules.filter((_, i) => i !== index);
        setModules(newModules);
    };

    // Course handlers
    const handleAddCourse = (moduleIndex: number) => {
        setEditingCourse({ moduleIndex, course: null });
        courseForm.resetFields();
        setIsCourseModalOpen(true);
    };

    const handleEditCourse = (
        moduleIndex: number,
        course: Course,
        courseIndex: number,
    ) => {
        setEditingCourse({ moduleIndex, course, courseIndex });
        courseForm.setFieldsValue({ title: course.title });
        setIsCourseModalOpen(true);
    };

    const handleCourseSubmit = (values: CourseFormValues) => {
        const newModules = [...modules];
        if (editingCourse.courseIndex !== undefined) {
            // Edit existing course
            newModules[editingCourse.moduleIndex].courses[editingCourse.courseIndex] = {
                ...newModules[editingCourse.moduleIndex].courses[
                    editingCourse.courseIndex
                ],
                title: values.title,
            };
        } else {
            // Add new course
            const newCourse: Course = {
                mcid: `temp-${Date.now()}`,
                title: values.title,
                topics: [],
            };
            newModules[editingCourse.moduleIndex].courses.push(newCourse);
        }
        setModules(newModules);
        setIsCourseModalOpen(false);
        setEditingCourse({ moduleIndex: -1, course: null });
    };

    const handleDeleteCourse = (moduleIndex: number, courseIndex: number) => {
        const newModules = [...modules];
        newModules[moduleIndex].courses = newModules[moduleIndex].courses.filter(
            (_, i) => i !== courseIndex,
        );
        setModules(newModules);
    };

    // Topic handlers
    const handleAddTopic = (moduleIndex: number, courseIndex: number) => {
        setEditingTopic({ moduleIndex, courseIndex, topic: null });
        topicForm.resetFields();
        setIsTopicModalOpen(true);
    };

    const handleEditTopic = (
        moduleIndex: number,
        courseIndex: number,
        topic: Topic,
        topicIndex: number,
    ) => {
        setEditingTopic({ moduleIndex, courseIndex, topic, topicIndex });
        topicForm.setFieldsValue({ title: topic.title });
        setIsTopicModalOpen(true);
    };

    const handleTopicSubmit = (values: TopicFormValues) => {
        const newModules = [...modules];
        if (editingTopic.topicIndex !== undefined) {
            // Edit existing topic
            newModules[editingTopic.moduleIndex].courses[
                editingTopic.courseIndex
            ].topics[editingTopic.topicIndex] = {
                ...newModules[editingTopic.moduleIndex].courses[
                    editingTopic.courseIndex
                ].topics[editingTopic.topicIndex],
                title: values.title,
            };
        } else {
            // Add new topic
            const newTopic: Topic = {
                tid: `temp-${Date.now()}`,
                title: values.title,
            };
            newModules[editingTopic.moduleIndex].courses[
                editingTopic.courseIndex
            ].topics.push(newTopic);
        }
        setModules(newModules);
        setIsTopicModalOpen(false);
        setEditingTopic({ moduleIndex: -1, courseIndex: -1, topic: null });
    };

    const handleDeleteTopic = (
        moduleIndex: number,
        courseIndex: number,
        topicIndex: number,
    ) => {
        const newModules = [...modules];
        newModules[moduleIndex].courses[courseIndex].topics = newModules[
            moduleIndex
        ].courses[courseIndex].topics.filter((_, i) => i !== topicIndex);
        setModules(newModules);
    };

    // Clean temporary IDs before sending to backend
    const cleanModulesForSave = useCallback((modulesToClean: Module[]): Module[] => {
        return modulesToClean.map((module) => ({
            ...module,
            omid: module.omid?.startsWith("temp-") ? undefined : module.omid,
            courses: module.courses.map((course) => ({
                ...course,
                mcid: course.mcid?.startsWith("temp-") ? undefined : course.mcid,
                topics: course.topics.map((topic) => ({
                    ...topic,
                    tid: topic.tid?.startsWith("temp-") ? undefined : topic.tid,
                })),
            })),
        }));
    }, []);

    const handleSaveCurriculum = useCallback(() => {
        const cleanedModules = cleanModulesForSave(modules);
        updateProgram({ oid, data: { modules: cleanedModules } });
    }, [modules, cleanModulesForSave, updateProgram, oid]);

    return (
        <div className="grid grid-cols-1 lg:grid-cols-6 gap-y-6 lg:gap-6">
            <div className="bg-white-full col-span-1 lg:col-span-4 p-5 rounded-lg shadow-sm">
                <p className="text-gray-400 font-semibold text-sm mb-4">
                    GESTIÓN DE CONTENIDO
                </p>
                <div className="mb-6">
                    <Title level={4} className="mb-1">
                        Módulos
                    </Title>
                    <Text type="secondary">
                        Gestiona los módulos, cursos y temas del programa
                    </Text>
                </div>

                <div className="flex justify-between items-center mb-1">
                    <Button
                        type="primary"
                        size="middle"
                        icon={<Plus size={16} />}
                        onClick={handleAddModule}
                    >
                        Agregar Módulo
                    </Button>
                    {/* Toggle All/Collapse All buttons */}
                    <div className="flex justify-end gap-2 mb-4">
                        <Button
                            type="text"
                            size="small"
                            icon={<ChevronDown size={16} />}
                            onClick={expandAll}
                        >
                            Desplegar todos
                        </Button>
                        <Button
                            type="text"
                            size="small"
                            icon={<ChevronUp size={16} />}
                            onClick={collapseAll}
                        >
                            Colapsar todos
                        </Button>
                    </div>
                </div>
                {modules.length === 0 ? (
                    <Empty
                        description="No hay módulos agregados"
                        image={Empty.PRESENTED_IMAGE_SIMPLE}
                    >
                        <Button
                            type="primary"
                            icon={<Plus size={16} />}
                            onClick={handleAddModule}
                        >
                            Agregar Primer Módulo
                        </Button>
                    </Empty>
                ) : (
                    <Collapse
                        className="curriculum-collapse"
                        activeKey={Array.from(expandedModules) as string[]}
                        onChange={(keys) => {
                            const keyArray = Array.isArray(keys) ? keys : [keys];
                            updateExpandedModules(
                                new Set(keyArray.filter(Boolean) as string[]),
                            );
                        }}
                    >
                        {modules.map((module, moduleIndex) => {
                            const moduleKey = module.omid || `module-${moduleIndex}`;
                            return (
                                <Panel
                                    key={moduleKey}
                                    header={
                                        <div className="flex items-center justify-between w-full">
                                            <div className="flex items-center gap-3">
                                                <GripVertical
                                                    size={16}
                                                    className="text-gray-400"
                                                />
                                                <BookOpen
                                                    size={16}
                                                    className="text-blue-500"
                                                />
                                                <span className="font-medium">
                                                    Módulo {moduleIndex + 1}:{" "}
                                                    {module.title}
                                                </span>
                                                <Tag color="blue">
                                                    {module.courses?.length || 0} cursos
                                                </Tag>
                                            </div>
                                            <Space onClick={(e) => e.stopPropagation()}>
                                                <Button
                                                    type="text"
                                                    size="small"
                                                    icon={<Edit3 size={14} />}
                                                    onClick={() =>
                                                        handleEditModule(
                                                            module,
                                                            moduleIndex,
                                                        )
                                                    }
                                                />
                                                <Popconfirm
                                                    title="¿Eliminar módulo?"
                                                    description="Esta acción no se puede deshacer"
                                                    onConfirm={() =>
                                                        handleDeleteModule(moduleIndex)
                                                    }
                                                    okText="Eliminar"
                                                    cancelText="Cancelar"
                                                >
                                                    <Button
                                                        type="text"
                                                        size="small"
                                                        danger
                                                        icon={<Trash2 size={14} />}
                                                    />
                                                </Popconfirm>
                                            </Space>
                                        </div>
                                    }
                                >
                                    <div className="pl-6 space-y-4">
                                        <div className="flex justify-between items-center">
                                            <Text strong>Cursos del Módulo</Text>
                                            <Button
                                                type="dashed"
                                                size="small"
                                                icon={<Plus size={14} />}
                                                onClick={() =>
                                                    handleAddCourse(moduleIndex)
                                                }
                                            >
                                                Agregar Curso
                                            </Button>
                                        </div>

                                        {module.courses?.length === 0 ? (
                                            <Empty
                                                description="No hay cursos en este módulo"
                                                image={Empty.PRESENTED_IMAGE_SIMPLE}
                                                className="py-4"
                                            />
                                        ) : (
                                            <List
                                                dataSource={module.courses}
                                                renderItem={(course, courseIndex) => (
                                                    <List.Item
                                                        key={course.mcid || courseIndex}
                                                        className="p-4 mb-3"
                                                        actions={[
                                                            <Button
                                                                type="text"
                                                                size="small"
                                                                icon={
                                                                    <Edit3 size={14} />
                                                                }
                                                                onClick={() =>
                                                                    handleEditCourse(
                                                                        moduleIndex,
                                                                        course,
                                                                        courseIndex,
                                                                    )
                                                                }
                                                            />,
                                                            <Popconfirm
                                                                title="¿Eliminar curso?"
                                                                onConfirm={() =>
                                                                    handleDeleteCourse(
                                                                        moduleIndex,
                                                                        courseIndex,
                                                                    )
                                                                }
                                                            >
                                                                <Button
                                                                    type="text"
                                                                    size="small"
                                                                    danger
                                                                    icon={
                                                                        <Trash2
                                                                            size={14}
                                                                        />
                                                                    }
                                                                />
                                                            </Popconfirm>,
                                                        ]}
                                                    >
                                                        <List.Item.Meta
                                                            avatar={
                                                                <FileText
                                                                    size={20}
                                                                    className="text-green-500"
                                                                />
                                                            }
                                                            title={
                                                                <div className="flex items-center gap-2">
                                                                    <span>
                                                                        {course.title}
                                                                    </span>
                                                                    <Tag color="green">
                                                                        {course.topics
                                                                            ?.length ||
                                                                            0}{" "}
                                                                        temas
                                                                    </Tag>
                                                                </div>
                                                            }
                                                            description={
                                                                <div className="space-y-2">
                                                                    <div className="flex justify-between items-center">
                                                                        <Text type="secondary">
                                                                            Temas del
                                                                            curso
                                                                        </Text>
                                                                        <Button
                                                                            type="link"
                                                                            size="small"
                                                                            onClick={() =>
                                                                                handleAddTopic(
                                                                                    moduleIndex,
                                                                                    courseIndex,
                                                                                )
                                                                            }
                                                                        >
                                                                            + Agregar
                                                                            tema
                                                                        </Button>
                                                                    </div>
                                                                    {course.topics
                                                                        ?.length ===
                                                                    0 ? (
                                                                        <Text
                                                                            type="secondary"
                                                                            className="text-sm"
                                                                        >
                                                                            No hay temas
                                                                            agregados
                                                                        </Text>
                                                                    ) : (
                                                                        <div className="flex flex-col gap-1 w-max">
                                                                            {course.topics?.map(
                                                                                (
                                                                                    topic,
                                                                                    topicIndex,
                                                                                ) => (
                                                                                    <Tag
                                                                                        key={
                                                                                            topic.tid ||
                                                                                            topicIndex
                                                                                        }
                                                                                        rootClassName="flex items-center gap-1 bg-none border-none justify-between"
                                                                                        closable
                                                                                        closeIcon={
                                                                                            <Tooltip title="Eliminar tema">
                                                                                                <XIcon
                                                                                                    size={
                                                                                                        16
                                                                                                    }
                                                                                                    className="text-red-500"
                                                                                                />
                                                                                            </Tooltip>
                                                                                        }
                                                                                        onClose={() =>
                                                                                            handleDeleteTopic(
                                                                                                moduleIndex,
                                                                                                courseIndex,
                                                                                                topicIndex,
                                                                                            )
                                                                                        }
                                                                                        onClick={() =>
                                                                                            handleEditTopic(
                                                                                                moduleIndex,
                                                                                                courseIndex,
                                                                                                topic,
                                                                                                topicIndex,
                                                                                            )
                                                                                        }
                                                                                        className="cursor-pointer"
                                                                                    >
                                                                                        {
                                                                                            topic.title
                                                                                        }
                                                                                    </Tag>
                                                                                ),
                                                                            )}
                                                                        </div>
                                                                    )}
                                                                </div>
                                                            }
                                                        />
                                                    </List.Item>
                                                )}
                                            />
                                        )}
                                    </div>
                                </Panel>
                            );
                        })}
                    </Collapse>
                )}
            </div>

            {/* Actions Column */}
            <div className="col-span-1 lg:col-span-2 space-y-6">
                <div className="bg-white-full p-5 rounded-lg shadow-sm">
                    <p className="text-gray-400 font-semibold text-sm mb-4">ACCIONES</p>
                    <div className="space-y-3">
                        <Button
                            type="primary"
                            size="large"
                            onClick={handleSaveCurriculum}
                            disabled={isUpdatingProgram}
                            loading={isUpdatingProgram}
                            style={{ fontSize: 16 }}
                        >
                            Guardar
                        </Button>
                    </div>
                </div>
            </div>

            {/* Module Modal */}
            <Modal
                title={editingModule ? "Editar Módulo" : "Agregar Módulo"}
                open={isModuleModalOpen}
                onCancel={() => setIsModuleModalOpen(false)}
                footer={null}
            >
                <Form form={moduleForm} onFinish={handleModuleSubmit} layout="vertical">
                    <Form.Item
                        name="title"
                        label="Título del Módulo"
                        rules={[{ required: true, message: "El título es requerido" }]}
                    >
                        <Input
                            id="module-title-input"
                            placeholder="Ej: Fundamentos de Programación"
                        />
                    </Form.Item>
                    <div className="flex justify-end gap-2">
                        <Button onClick={() => setIsModuleModalOpen(false)}>
                            Cancelar
                        </Button>
                        <Button type="primary" htmlType="submit">
                            {editingModule ? "Actualizar" : "Agregar"}
                        </Button>
                    </div>
                </Form>
            </Modal>

            {/* Course Modal */}
            <Modal
                title={editingCourse.course ? "Editar Curso" : "Agregar Curso"}
                open={isCourseModalOpen}
                onCancel={() => setIsCourseModalOpen(false)}
                footer={null}
            >
                <Form form={courseForm} onFinish={handleCourseSubmit} layout="vertical">
                    <Form.Item
                        name="title"
                        label="Título del Curso"
                        rules={[{ required: true, message: "El título es requerido" }]}
                    >
                        <Input
                            id="course-title-input"
                            autoFocus
                            placeholder="Ej: Introducción a JavaScript"
                        />
                    </Form.Item>
                    <div className="flex justify-end gap-2">
                        <Button onClick={() => setIsCourseModalOpen(false)}>
                            Cancelar
                        </Button>
                        <Button type="primary" htmlType="submit">
                            {editingCourse.course ? "Actualizar" : "Agregar"}
                        </Button>
                    </div>
                </Form>
            </Modal>

            {/* Topic Modal */}
            <Modal
                title={editingTopic.topic ? "Editar Tema" : "Agregar Tema"}
                open={isTopicModalOpen}
                onCancel={() => setIsTopicModalOpen(false)}
                footer={null}
            >
                <Form form={topicForm} onFinish={handleTopicSubmit} layout="vertical">
                    <Form.Item
                        name="title"
                        label="Título del Tema"
                        rules={[{ required: true, message: "El título es requerido" }]}
                    >
                        <Input
                            id="topic-title-input"
                            placeholder="Ej: Variables y tipos de datos"
                        />
                    </Form.Item>
                    <div className="flex justify-end gap-2">
                        <Button onClick={() => setIsTopicModalOpen(false)}>
                            Cancelar
                        </Button>
                        <Button type="primary" htmlType="submit">
                            {editingTopic.topic ? "Actualizar" : "Agregar"}
                        </Button>
                    </div>
                </Form>
            </Modal>
        </div>
    );
}
