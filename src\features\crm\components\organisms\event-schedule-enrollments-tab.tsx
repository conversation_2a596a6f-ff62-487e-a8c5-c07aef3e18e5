import { useSearchParams } from "react-router-dom";
import { Badge, Input, Pagination, Typography, Button, Tooltip } from "antd";
import { Search, Download } from "lucide-react";
import { RetrieveEventSchedule } from "../../types/event-schedule";
import { useEventScheduleEnrollments } from "../../hooks/use-event-schedule-enrollment";
import {
    DEFAULT_PAGE,
    DEFAULT_PAGE_SIZE,
    ListEventScheduleEnrollmentsQueryParams,
} from "../../types/event-schedule-enrollment";
import EventScheduleEnrollmentsTable from "./event-schedule-enrollments-table";
import EnrollmentStats from "../molecules/enrollment-stats";

const { Text } = Typography;

export default function EventScheduleEnrollmentsTab({
    eventSchedule,
}: {
    eventSchedule: RetrieveEventSchedule;
}) {
    const [searchParams, setSearchParams] = useSearchParams();

    // Get params from URL
    const page = Number(searchParams.get("page")) || DEFAULT_PAGE;
    const pageSize = Number(searchParams.get("pageSize")) || DEFAULT_PAGE_SIZE;
    const search = searchParams.get("search") || undefined;
    const hasContact =
        searchParams.get("hasContact") === "true"
            ? true
            : searchParams.get("hasContact") === "false"
              ? false
              : undefined;
    const needsConciliation =
        searchParams.get("needsConciliation") === "true" ? true : undefined;
    const alreadyLead = searchParams.get("alreadyLead") === "true" ? true : undefined;

    const queryParams: ListEventScheduleEnrollmentsQueryParams = {
        page,
        pageSize,
        search,
        hasContact,
        needsConciliation,
        alreadyLead,
    };

    // Fetch enrollments
    const { enrollments, count, isLoading, isError } = useEventScheduleEnrollments({
        esid: eventSchedule.esid,
        queryParams,
    });

    // Handle search
    const handleSearch = (value: string) => {
        setSearchParams((prev) => {
            if (value.trim()) {
                prev.set("search", value.trim());
            } else {
                prev.delete("search");
            }
            prev.set("page", "1"); // Reset to first page
            return prev;
        });
    };

    // Handle pagination
    const handlePaginationChange = (newPage: number, newPageSize: number) => {
        setSearchParams((prev) => {
            prev.set("page", newPage.toString());
            prev.set("pageSize", newPageSize.toString());
            return prev;
        });
    };

    // Handle export (placeholder)
    const handleExport = () => {
        // TODO: Implement export functionality
        console.log("Export enrollments");
    };

    if (isError) {
        return (
            <div className="flex items-center justify-center h-64">
                <Text type="danger">
                    Error al cargar las inscripciones. Por favor, intenta nuevamente.
                </Text>
            </div>
        );
    }

    return (
        <div className="space-y-4">
            {/* Stats Section */}
            <EnrollmentStats enrollments={enrollments} totalCount={count} />

            {/* Controls Section */}
            <div className="bg-white p-4 rounded-lg shadow-sm">
                <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
                    {/* Title and Count */}
                    <div className="flex items-center gap-2">
                        <Text className="text-black-medium text-xl font-semibold">
                            Inscripciones
                        </Text>
                        <Badge count={count} color="blue" size="default" />
                    </div>

                    {/* Search and Actions */}
                    <div className="flex flex-col sm:flex-row gap-3 w-full lg:w-auto">
                        <Input.Search
                            placeholder="Buscar por nombre, email, universidad..."
                            onSearch={handleSearch}
                            defaultValue={search}
                            enterButton={<Search size={16} />}
                            allowClear
                            className="w-full sm:w-64"
                            size="large"
                        />

                        <div className="gap-2 hidden">
                            <Tooltip title="Exportar a Excel">
                                <Button
                                    icon={<Download size={16} />}
                                    onClick={handleExport}
                                    disabled={count === 0}
                                >
                                    Exportar
                                </Button>
                            </Tooltip>
                        </div>
                    </div>
                </div>
            </div>

            {/* Table Section */}
            <div className="bg-white rounded-lg shadow-sm">
                <EventScheduleEnrollmentsTable
                    enrollments={enrollments}
                    loading={isLoading}
                />
            </div>

            {/* Pagination Section */}
            {count > 0 && (
                <div className="flex justify-between items-center p-4 bg-white rounded-lg shadow-sm">
                    <Text type="secondary">
                        {enrollments.length} de {count} inscripciones
                    </Text>
                    <Pagination
                        current={page}
                        pageSize={pageSize}
                        total={count}
                        onChange={handlePaginationChange}
                        showSizeChanger
                        showQuickJumper
                        showTotal={(total, range) =>
                            `${range[0]}-${range[1]} de ${total} inscripciones`
                        }
                    />
                </div>
            )}
        </div>
    );
}
