import { formatDate } from "@lib/helpers";
import { PaymentListItem } from "../../types/payment";
import { Calendar, Check, Clock, XOctagon } from "lucide-react";

type PaymentStatusCellProps = {
    isPaid: boolean;
    payment: PaymentListItem;
};

export default function PaymentStatusCell({ isPaid, payment }: PaymentStatusCellProps) {
    const { paymentDate, isLost, scheduledPaymentDate } = payment;
    const formattedPaymentDate = formatDate(
        isPaid ? paymentDate : scheduledPaymentDate,
    );

    // If payment is lost, show lost status with priority
    if (isLost) {
        return (
            <div className="flex flex-col gap-2">
                <div className="flex items-center">
                    <span className="text-xs font-medium px-3 py-1 rounded-full bg-red-100 text-red-800 border border-red-200">
                        <div className="flex items-center gap-1">
                            <XOctagon size={14} strokeWidth={2.5} />
                            <span>Perdido</span>
                        </div>
                    </span>
                </div>
                <div className="flex items-start gap-2">
                    <Calendar size={18} className="text-red-600" strokeWidth={1.75} />
                    <div className="flex flex-col">
                        <span className="text-xs font-medium text-gray-500">
                            Fecha prevista:
                        </span>
                        <span className="text-sm font-medium text-gray-800">
                            {formattedPaymentDate}
                        </span>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="flex flex-col gap-2">
            <div className="flex items-center">
                <span
                    className={`text-xs font-medium px-3 py-1 rounded-full ${
                        isPaid
                            ? "bg-green-100 text-green-800 border border-green-200"
                            : "bg-amber-50 text-amber-800 border border-amber-200"
                    }`}
                >
                    {isPaid ? (
                        <div className="flex items-center gap-1">
                            <Check size={14} strokeWidth={2.5} />
                            <span>Pagado</span>
                        </div>
                    ) : (
                        <div className="flex items-center gap-1">
                            <Clock size={14} strokeWidth={2.5} />
                            <span>Pendiente</span>
                        </div>
                    )}
                </span>
            </div>

            <div className="flex items-start gap-2">
                <Calendar
                    size={18}
                    className={isPaid ? "text-green-600" : "text-amber-600"}
                    strokeWidth={1.75}
                />
                <div className="flex flex-col">
                    <span className="text-xs font-medium text-gray-500">
                        {isPaid ? "Fecha de pago:" : "Fecha prevista:"}
                    </span>
                    <span className="text-sm font-medium text-gray-800">
                        {formattedPaymentDate}
                    </span>
                </div>
            </div>
        </div>
    );
}
