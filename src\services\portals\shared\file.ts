import { File } from "@myTypes/file";
import { portalsApi } from "@services/portals";
import { PaginatedResponse, PaginationParams } from "@myTypes/base";

export type ListFilesQuery = PaginationParams & {
    search?: string;
    sortBy?: string;
    order?: "asc" | "desc";
    isUsed?: boolean;
};

export type FileUploadOptions = {
    width?: number;
    height?: number;
    description?: string;
    isUsed?: boolean;
    outputFormat?: "WEBP" | "JPEG" | "PNG";
};

export type FileUpdateOptions = FileUploadOptions;

export const listFiles = async (
    params: ListFilesQuery = {},
): Promise<PaginatedResponse<File>> => {
    const response = await portalsApi.get("shared/files", {
        params,
    });
    return response.data;
};

export const uploadFile = async (
    file: Blob,
    options: FileUploadOptions = {},
): Promise<File> => {
    const formData = new FormData();
    formData.append("file", file);

    // Añadir opciones adicionales al FormData
    Object.entries(options).forEach(([key, value]) => {
        if (value !== undefined) {
            formData.append(key, String(value));
        }
    });

    const response = await portalsApi.post("shared/files", formData, {
        headers: {
            "Content-Type": "multipart/form-data",
        },
    });
    return response.data;
};

export const retrieveFile = async (fid: string): Promise<File> => {
    const response = await portalsApi.get(`shared/files/${fid}`);
    return response.data;
};

export const updateFile = async (
    fid: string,
    file?: Blob,
    options: FileUpdateOptions = {},
): Promise<File> => {
    const formData = new FormData();

    if (file) {
        formData.append("file", file);
    }

    Object.entries(options).forEach(([key, value]) => {
        if (value !== undefined) {
            formData.append(key, String(value));
        }
    });

    const response = await portalsApi.put(`shared/files/${fid}`, formData, {
        headers: {
            "Content-Type": "multipart/form-data",
        },
    });
    return response.data;
};

export const deleteFile = async (fid: string): Promise<void> => {
    await portalsApi.delete(`shared/files/${fid}`);
};
