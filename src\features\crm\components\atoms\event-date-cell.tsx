import { formatDateTime } from "@lib/helpers";
import { Calendar } from "lucide-react";
import { Typography } from "antd";

const { Text } = Typography;

type EventDateCellProps = {
    date: string;
    label?: string;
};

export default function EventDateCell({ date, label = "Fecha" }: EventDateCellProps) {
    const formattedDate = formatDateTime(date);

    return (
        <div className="flex items-start gap-2">
            <Calendar size={16} className="text-gray-500 mt-0.5" strokeWidth={1.75} />
            <div className="flex flex-col">
                <Text type="secondary" className="text-xs">
                    {label}:
                </Text>
                <Text className="text-sm font-medium">{formattedDate}</Text>
            </div>
        </div>
    );
}
