import React from "react";
import { Row, Col } from "antd";
import EventStatsCards from "@/features/crm/components/molecules/events/event-stats-cards";
import { useDashboardEventsSummary } from "@/features/crm/hooks/use-dashboard-events";
import type { DashboardEventQueryParams } from "@/features/crm/types/dashboard/events";

interface EventsSummarySection {
    queryParams: DashboardEventQueryParams;
}

const EventsSummarySection: React.FC<EventsSummarySection> = ({ queryParams }) => {
    const { data: summaryData, isLoading } = useDashboardEventsSummary(queryParams);

    return (
        <Row gutter={[16, 16]}>
            <Col xs={24}>
                <EventStatsCards
                    stats={summaryData?.stats}
                    needsConciliation={summaryData?.needsConciliation}
                    alliancesEnrollments={summaryData?.alliancesEnrollments}
                    conversion={summaryData?.conversion}
                    isLoading={isLoading}
                />
            </Col>
        </Row>
    );
};

export default EventsSummarySection;
