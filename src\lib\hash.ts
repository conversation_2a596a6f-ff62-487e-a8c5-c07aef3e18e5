import type { NormalOption } from "object-hash";
import hash from "object-hash";

const HASH_OPTIONS: NormalOption = {
    algorithm: "sha1",
    encoding: "hex",
};

export function objectHash(obj: Record<string, unknown>): string {
    // Manejar casos especiales
    if (obj === null || obj === undefined) return "null";
    if (typeof obj !== "object") return String(obj);

    return hash(obj, HASH_OPTIONS);
}

export function areObjectsDifferent(
    objA: Record<string, unknown>,
    objB: Record<string, unknown>,
): boolean {
    return objectHash(objA) !== objectHash(objB);
}
