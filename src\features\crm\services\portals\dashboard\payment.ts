import { portalsApi } from "@services/portals";
import type {
    DashboardPaymentQueryParams,
    DashboardPaymentSummaryData,
    DashboardPaymentHistoricalQueryParams,
    DashboardPaymentHistoricalData,
    DashboardPaymentCurrencyDistributionData,
    DashboardPaymentMethodHistoricalData,
    DashboardPaymentMethodDistributionData,
    DashboardLastPaymentsData,
} from "../../../types/dashboard/payment";

export const getPaymentDashboardSummary = async (
    params: DashboardPaymentQueryParams = {},
): Promise<DashboardPaymentSummaryData> => {
    const { data } = await portalsApi.get("/crm/dashboard/payments/summary", {
        params,
    });
    return data;
};

export const getPaymentDashboardHistorical = async (
    params: DashboardPaymentHistoricalQueryParams,
): Promise<DashboardPaymentHistoricalData> => {
    const { data } = await portalsApi.get("/crm/dashboard/payments/historical-paids", {
        params,
    });
    return data;
};

export const getPaymentDashboardCurrencyDistribution = async (
    params: DashboardPaymentQueryParams = {},
): Promise<DashboardPaymentCurrencyDistributionData> => {
    const { data } = await portalsApi.get(
        "/crm/dashboard/payments/currency-distribution",
        {
            params,
        },
    );
    return data;
};

export const getPaymentDashboardHistoricalMethods = async (
    params: DashboardPaymentHistoricalQueryParams,
): Promise<DashboardPaymentMethodHistoricalData> => {
    const { data } = await portalsApi.get(
        "/crm/dashboard/payments/historical-payment-methods",
        {
            params,
        },
    );
    return data;
};

export const getPaymentDashboardMethodsDistribution = async (
    params: DashboardPaymentQueryParams = {},
): Promise<DashboardPaymentMethodDistributionData> => {
    const { data } = await portalsApi.get(
        "/crm/dashboard/payments/payment-methods-distribution",
        {
            params,
        },
    );
    return data;
};

export const getPaymentDashboardLastPayments = async (
    params: DashboardPaymentQueryParams = {},
): Promise<DashboardLastPaymentsData> => {
    const { data } = await portalsApi.get("/crm/dashboard/payments/last-payments", {
        params,
    });
    return data;
};
