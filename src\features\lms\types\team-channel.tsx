// Team channels (For chat groups associated to the program)
export type TeamChannel = {
    id: string;
    subject: string;
    subjectTime: number;
    description?: string;
    pictureUrl?: string;
    size: number; // Number of members
    creation: number;
};

export type UseTeamChannelsQuery = Partial<{
    search: string;
}>;

export type TeamChannelQueryParams = {
    page?: number;
    pageSize?: number;
    sortBy?: string;
    order?: "asc" | "desc";
} & UseTeamChannelsQuery;
