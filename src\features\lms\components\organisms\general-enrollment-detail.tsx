import { message, <PERSON><PERSON>, <PERSON><PERSON>, Tooltip } from "antd";
import {
    User,
    Package,
    ExternalLink,
    Mail,
    Phone,
    BookOpen,
    ShoppingCart,
    ArrowRight,
    Calendar,
    Settings,
    CheckCircle2,
    XCircle,
    Send,
    Award,
    Clock,
    Eye,
} from "lucide-react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { updateEnrollment } from "../../services/portals/enrollment";
import { EnrollmentRetrieve, EnrollmentUpdate } from "../../types/enrollment";
import { Link } from "react-router-dom";

import { useState } from "react";
import { formatDateTime } from "@lib/helpers";

interface GeneralEnrollmentDetailProps {
    enrollment: EnrollmentRetrieve;
}

export default function GeneralEnrollmentDetail({
    enrollment,
}: GeneralEnrollmentDetailProps) {
    const queryClient = useQueryClient();
    const [activeModalVisible, setActiveModalVisible] = useState(false);
    const [certificateModalVisible, setCertificateModalVisible] = useState(false);
    const [sentModalVisible, setSentModalVisible] = useState(false);

    const updateMutation = useMutation({
        mutationFn: ({ eid, data }: { eid: string; data: EnrollmentUpdate }) =>
            updateEnrollment(eid, data),
        onSuccess: () => {
            message.success("Matrícula actualizada exitosamente");
            queryClient.invalidateQueries({
                queryKey: ["enrollments", enrollment.eid],
            });
            queryClient.invalidateQueries({ queryKey: ["enrollments"] });
        },
        onError: () => {
            message.error("Error al actualizar la matrícula");
        },
    });

    const handleActiveChange = () => {
        const updateData: EnrollmentUpdate = { isActive: !enrollment.isActive };
        updateMutation.mutate({ eid: enrollment.eid, data: updateData });
        setActiveModalVisible(false);
    };

    const handleCertificateChange = () => {
        const updateData: EnrollmentUpdate = {
            certificateIssued: !enrollment.certificateIssued,
        };
        updateMutation.mutate({ eid: enrollment.eid, data: updateData });
        setCertificateModalVisible(false);
    };

    const handleCertificateSentChange = () => {
        const updateData: EnrollmentUpdate = {
            certificateSent: !enrollment.certificateSent,
        };
        updateMutation.mutate({ eid: enrollment.eid, data: updateData });
        setSentModalVisible(false);
    };

    const getStatusConfig = (
        status: boolean,
        type: "active" | "certificate" | "sent",
    ) => {
        const configs = {
            active: {
                true: {
                    color: "bg-green-100 text-green-800 border-green-200",
                    icon: CheckCircle2,
                    text: "Activo",
                    bgClass: "bg-green-50",
                },
                false: {
                    color: "bg-red-100 text-red-800 border-red-200",
                    icon: XCircle,
                    text: "Inactivo",
                    bgClass: "bg-red-50",
                },
            },
            certificate: {
                true: {
                    color: "bg-blue-100 text-blue-800 border-blue-200",
                    icon: Award,
                    text: "Emitido",
                    bgClass: "bg-blue-50",
                },
                false: {
                    color: "bg-orange-100 text-orange-800 border-orange-200",
                    icon: Clock,
                    text: "Pendiente",
                    bgClass: "bg-orange-50",
                },
            },
            sent: {
                true: {
                    color: "bg-purple-100 text-purple-800 border-purple-200",
                    icon: Send,
                    text: "Enviado",
                    bgClass: "bg-purple-50",
                },
                false: {
                    color: "bg-gray-100 text-gray-800 border-gray-200",
                    icon: Clock,
                    text: "No enviado",
                    bgClass: "bg-gray-50",
                },
            },
        };
        return configs[type][status.toString() as "true" | "false"];
    };

    return (
        <div className="space-y-8">
            <div className="grid grid-cols-1 xl:grid-cols-3 gap-4">
                {/* Main Content */}
                <div className="xl:col-span-2 space-y-4">
                    {/* Student Information */}
                    <div className="bg-white-full rounded-xl border border-gray-200 overflow-hidden">
                        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-200">
                            <div className="flex items-center gap-3">
                                <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                                    <User size={20} className="text-white-full" />
                                </div>
                                <div>
                                    <h3 className="text-md font-semibold text-gray-900">
                                        Información del Estudiante
                                    </h3>
                                    <p className="text-sm text-gray-600">
                                        Datos de contacto y perfil
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div className="p-2">
                            <div className="bg-gray-50 rounded-xl p-2 border border-gray-100">
                                <div className="flex items-start justify-between">
                                    <div className="flex-1">
                                        <Link
                                            to={`/crm/contacts/${enrollment.user.uid}`}
                                            className="inline-flex items-center gap-2 text-md font-semibold text-blue-600 hover:text-blue-800 transition-colors group"
                                        >
                                            {enrollment.user.fullName ||
                                                `${enrollment.user.firstName} ${enrollment.user.lastName}`}
                                            <ExternalLink
                                                size={16}
                                                className="group-hover:translate-x-0.5 transition-transform"
                                            />
                                        </Link>

                                        <div className="mt-4 space-y-3">
                                            {enrollment.user.email && (
                                                <div className="flex items-center gap-3 text-gray-600">
                                                    <div className="w-8 h-8 bg-gray-200 rounded-lg flex items-center justify-center">
                                                        <Mail size={16} />
                                                    </div>
                                                    <span className="text-sm font-medium">
                                                        {enrollment.user.email}
                                                    </span>
                                                </div>
                                            )}
                                            {enrollment.user.phoneNumber && (
                                                <div className="flex items-center gap-3 text-gray-600">
                                                    <div className="w-8 h-8 bg-gray-200 rounded-lg flex items-center justify-center">
                                                        <Phone size={16} />
                                                    </div>
                                                    <span className="text-sm font-medium">
                                                        {enrollment.user.phoneNumber}
                                                    </span>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Program and Order Information */}
                    <div className="bg-white-full rounded-xl border border-gray-200 overflow-hidden">
                        <div className="bg-gradient-to-r from-green-50 to-emerald-50 px-6 py-4 border-b border-gray-200">
                            <div className="flex items-center gap-3">
                                <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                                    <Package size={20} className="text-white-full" />
                                </div>
                                <div>
                                    <h3 className="text-md font-semibold text-gray-900">
                                        Programa y Pedido
                                    </h3>
                                    <p className="text-sm text-gray-600">
                                        Detalles del programa e información de compra
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div className="p-2">
                            <div className="bg-gradient-to-br from-blue-50 via-white to-orange-50 rounded-xl p-8 border border-gray-100">
                                {/* Program Section */}
                                <div className="flex items-start gap-4 mb-6">
                                    <div className="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center flex-shrink-0">
                                        <BookOpen
                                            size={20}
                                            className="text-white-full"
                                        />
                                    </div>
                                    <div className="flex-1 min-w-0">
                                        <div className="flex items-center gap-3 mb-3">
                                            <h4 className="text-md font-semibold text-gray-900 line-clamp-2">
                                                {enrollment.offering.longName ||
                                                    enrollment.offering.name}
                                            </h4>
                                            {enrollment.offering.codeName && (
                                                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                    {enrollment.offering.codeName}
                                                </span>
                                            )}
                                        </div>
                                        <p className="text-sm text-gray-600 font-mono">
                                            ID: {enrollment.offering.oid}
                                        </p>
                                    </div>
                                </div>

                                {/* Connection Visual */}
                                <div className="flex justify-center my-6">
                                    <div className="flex items-center gap-3">
                                        <div className="w-8 h-0.5 bg-gradient-to-r from-blue-300 to-orange-300"></div>
                                        <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                            <ArrowRight
                                                size={16}
                                                className="text-gray-600"
                                            />
                                        </div>
                                        <div className="w-8 h-0.5 bg-gradient-to-r from-blue-300 to-orange-300"></div>
                                    </div>
                                </div>

                                {/* Order Section */}
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-4">
                                        <div className="w-12 h-12 bg-orange-500 rounded-xl flex items-center justify-center flex-shrink-0">
                                            <ShoppingCart
                                                size={20}
                                                className="text-white-full"
                                            />
                                        </div>
                                        <div>
                                            <h4 className="text-md font-semibold text-gray-900">
                                                Orden #
                                                {enrollment.orderItem.order.slice(-8)}
                                            </h4>
                                            <p className="text-sm text-gray-600 font-mono">
                                                Item ID: {enrollment.orderItem.id}
                                            </p>
                                        </div>
                                    </div>
                                    <Link
                                        to={`/crm/orders/${enrollment.orderItem.order}`}
                                        className="inline-flex items-center gap-2 bg-orange-500 hover:bg-orange-600 text-white-full px-4 py-2.5 rounded-lg transition-colors font-medium text-sm group"
                                    >
                                        <Eye size={16} />
                                        Ver en CRM
                                        <ExternalLink
                                            size={14}
                                            className="group-hover:translate-x-0.5 transition-transform"
                                        />
                                    </Link>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Sidebar */}
                <div className="space-y-8">
                    {/* Status Cards */}
                    <div className="bg-white-full rounded-xl border border-gray-200 overflow-hidden">
                        <div className="bg-gradient-to-r from-purple-50 to-pink-50 px-6 py-4 border-b border-gray-200">
                            <div className="flex items-center gap-3">
                                <div className="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center">
                                    <Settings size={20} className="text-white-full" />
                                </div>
                                <div>
                                    <h3 className="text-md font-semibold text-gray-900">
                                        Estado y Configuraciones
                                    </h3>
                                </div>
                            </div>
                        </div>

                        <div className="p-2 space-y-4">
                            {/* Active Status */}
                            <div className="bg-gray-50 rounded-lg p-4 border border-gray-100">
                                <div className="flex items-center justify-between mb-3">
                                    <span className="text-sm font-medium text-gray-700">
                                        Estado de Matrícula
                                    </span>
                                    <Button
                                        size="small"
                                        onClick={() => setActiveModalVisible(true)}
                                        loading={updateMutation.isPending}
                                        className="text-xs"
                                    >
                                        Cambiar
                                    </Button>
                                </div>
                                {(() => {
                                    const config = getStatusConfig(
                                        enrollment.isActive,
                                        "active",
                                    );
                                    const IconComponent = config.icon;
                                    return (
                                        <div
                                            className={`inline-flex items-center gap-2 px-3 py-2 rounded-lg border ${config.color}`}
                                        >
                                            <IconComponent size={16} />
                                            <span className="text-sm font-medium">
                                                {config.text}
                                            </span>
                                        </div>
                                    );
                                })()}
                            </div>

                            {/* Certificate Status */}
                            <div className="bg-gray-50 rounded-lg p-4 border border-gray-100">
                                <div className="flex items-center justify-between mb-3">
                                    <span className="text-sm font-medium text-gray-700">
                                        Estado del Certificado
                                    </span>
                                    <Button
                                        size="small"
                                        onClick={() => setCertificateModalVisible(true)}
                                        loading={updateMutation.isPending}
                                        className="text-xs"
                                    >
                                        Cambiar
                                    </Button>
                                </div>
                                {(() => {
                                    const config = getStatusConfig(
                                        enrollment.certificateIssued,
                                        "certificate",
                                    );
                                    const IconComponent = config.icon;
                                    return (
                                        <div
                                            className={`inline-flex items-center gap-2 px-3 py-2 rounded-lg border ${config.color}`}
                                        >
                                            <IconComponent size={16} />
                                            <span className="text-sm font-medium">
                                                {config.text}
                                            </span>
                                        </div>
                                    );
                                })()}
                            </div>

                            {/* Certificate Sent Status */}
                            <div className="bg-gray-50 rounded-lg p-4 border border-gray-100">
                                <div className="flex items-center justify-between mb-3">
                                    <span className="text-sm font-medium text-gray-700">
                                        Certificado Enviado
                                    </span>
                                    <Button
                                        size="small"
                                        onClick={() => setSentModalVisible(true)}
                                        loading={updateMutation.isPending}
                                        className="text-xs"
                                        disabled={!enrollment.certificateIssued}
                                    >
                                        Cambiar
                                    </Button>
                                </div>
                                {(() => {
                                    const config = getStatusConfig(
                                        enrollment.certificateSent || false,
                                        "sent",
                                    );
                                    const IconComponent = config.icon;
                                    return (
                                        <div
                                            className={`inline-flex items-center gap-2 px-3 py-2 rounded-lg border ${config.color} ${!enrollment.certificateIssued ? "opacity-50" : ""}`}
                                        >
                                            <IconComponent size={16} />
                                            <span className="text-sm font-medium">
                                                {config.text}
                                            </span>
                                        </div>
                                    );
                                })()}
                                {!enrollment.certificateIssued && (
                                    <Tooltip title="El certificado debe estar emitido para poder marcarlo como enviado">
                                        <p className="text-xs text-gray-500 mt-2">
                                            Requiere certificado emitido
                                        </p>
                                    </Tooltip>
                                )}
                            </div>
                        </div>
                    </div>

                    {/* Enhanced Dates */}
                    <div className="bg-white-full rounded-xl border border-gray-200 overflow-hidden">
                        <div className="bg-gradient-to-r from-indigo-50 to-blue-50 px-6 py-4 border-b border-gray-200">
                            <div className="flex items-center gap-3">
                                <div className="w-10 h-10 bg-indigo-500 rounded-lg flex items-center justify-center">
                                    <Calendar size={20} className="text-white-full" />
                                </div>
                                <h3 className="text-md font-semibold text-gray-900">
                                    Fechas Importantes
                                </h3>
                            </div>
                        </div>

                        <div className="p-2 space-y-4">
                            <div className="bg-blue-50 rounded-lg p-4 border-l-4 border-blue-500">
                                <div className="flex items-center gap-3 mb-2">
                                    <Calendar size={16} className="text-blue-600" />
                                    <span className="font-medium text-blue-900 text-sm">
                                        Creado
                                    </span>
                                </div>
                                <p className="text-sm text-blue-700">
                                    {formatDateTime(enrollment.createdAt)}
                                </p>
                            </div>

                            <div className="bg-green-50 rounded-lg p-4 border-l-4 border-green-500">
                                <div className="flex items-center gap-3 mb-2">
                                    <Calendar size={16} className="text-green-600" />
                                    <span className="font-medium text-green-900 text-sm">
                                        Última Actualización
                                    </span>
                                </div>
                                <p className="text-sm text-green-700">
                                    {formatDateTime(enrollment.updatedAt)}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Modals */}
            <Modal
                title="Cambiar Estado de Matrícula"
                open={activeModalVisible}
                onCancel={() => setActiveModalVisible(false)}
                footer={[
                    <Button key="cancel" onClick={() => setActiveModalVisible(false)}>
                        Cancelar
                    </Button>,
                    <Button
                        key="confirm"
                        type="primary"
                        onClick={handleActiveChange}
                        loading={updateMutation.isPending}
                        danger={enrollment.isActive}
                    >
                        {enrollment.isActive ? "Desactivar" : "Activar"} Matrícula
                    </Button>,
                ]}
            >
                <div className="py-4">
                    <p className="text-gray-600 mb-4">
                        ¿Estás seguro de que deseas{" "}
                        {enrollment.isActive ? "desactivar" : "activar"} esta matrícula?
                    </p>
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                        <p className="text-sm text-yellow-800">
                            Esta acción afectará el acceso del estudiante al programa.
                        </p>
                    </div>
                </div>
            </Modal>

            <Modal
                title="Cambiar Estado del Certificado"
                open={certificateModalVisible}
                onCancel={() => setCertificateModalVisible(false)}
                footer={[
                    <Button
                        key="cancel"
                        onClick={() => setCertificateModalVisible(false)}
                    >
                        Cancelar
                    </Button>,
                    <Button
                        key="confirm"
                        type="primary"
                        onClick={handleCertificateChange}
                        loading={updateMutation.isPending}
                    >
                        {enrollment.certificateIssued
                            ? "Marcar como Pendiente"
                            : "Marcar como Emitido"}
                    </Button>,
                ]}
            >
                <div className="py-4">
                    <p className="text-gray-600 mb-4">
                        ¿Estás seguro de que deseas cambiar el estado del certificado?
                    </p>
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                        <p className="text-sm text-blue-800">
                            El certificado será marcado como{" "}
                            {enrollment.certificateIssued ? "pendiente" : "emitido"}.
                        </p>
                    </div>
                </div>
            </Modal>

            <Modal
                title="Cambiar Estado de Envío"
                open={sentModalVisible}
                onCancel={() => setSentModalVisible(false)}
                footer={[
                    <Button key="cancel" onClick={() => setSentModalVisible(false)}>
                        Cancelar
                    </Button>,
                    <Button
                        key="confirm"
                        type="primary"
                        onClick={handleCertificateSentChange}
                        loading={updateMutation.isPending}
                        disabled={!enrollment.certificateIssued}
                    >
                        {enrollment.certificateSent
                            ? "Marcar como No Enviado"
                            : "Marcar como Enviado"}
                    </Button>,
                ]}
            >
                <div className="py-4">
                    <p className="text-gray-600 mb-4">
                        ¿Estás seguro de que deseas cambiar el estado de envío del
                        certificado?
                    </p>
                    {!enrollment.certificateIssued ? (
                        <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                            <p className="text-sm text-red-800">
                                El certificado debe estar emitido antes de poder
                                marcarlo como enviado.
                            </p>
                        </div>
                    ) : (
                        <div className="bg-purple-50 border border-purple-200 rounded-lg p-3">
                            <p className="text-sm text-purple-800">
                                El certificado será marcado como{" "}
                                {enrollment.certificateSent ? "no enviado" : "enviado"}.
                            </p>
                        </div>
                    )}
                </div>
            </Modal>
        </div>
    );
}
