import CrmLayout from "@/features/crm/layout";
import WelcomeBar from "@components/shared/molecules/WelcomeBar";
import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    Drawer,
    Input,
    Modal,
    Pagination,
    Tabs,
    Typography,
} from "antd";
import { Plus, SlidersHorizontal } from "lucide-react";
import { useState, useMemo } from "react";
import { useSearchParams } from "react-router-dom";
import {
    TemplateStatus,
    DEFAULT_PAGE,
    DEFAULT_PAGE_SIZE,
    ListTemplatesQueryParams,
} from "@/features/crm/types/template";
import { useTemplates } from "@/features/crm/hooks/use-template";
import TemplatesTable from "@/features/crm/components/organisms/templates-table";
import CreateTemplateForm from "@/features/crm/components/organisms/create-template-form";

const { Search } = Input;

export default function TemplatesListPage() {
    const [searchParams, setSearchParams] = useSearchParams();
    const page = Number(searchParams.get("page")) || DEFAULT_PAGE;
    const pageSize = Number(searchParams.get("pageSize")) || DEFAULT_PAGE_SIZE;
    const search = searchParams.get("search") || undefined;
    const status = searchParams.get("status") || "";

    const queryParams: ListTemplatesQueryParams = useMemo(
        () => ({
            page: Number(page),
            pageSize: Number(pageSize),
            ...(search ? { search } : {}),
            ...(status ? { status: status as TemplateStatus } : {}),
        }),
        [page, pageSize, search, status],
    );

    const { templates, isLoading, COUNT: totalCount } = useTemplates(queryParams);

    const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
    const [isFilterDrawerOpen, setIsFilterDrawerOpen] = useState(false);

    const handleSetSearchQuery = (value: string) => {
        setSearchParams((prev) => {
            if (value) {
                prev.set("search", value);
            } else {
                prev.delete("search");
            }
            prev.set("page", "1");
            return prev;
        });
    };

    const handleTabChange = (key: string) => {
        setSearchParams((prev) => {
            if (key === "all") {
                prev.delete("status");
            } else {
                prev.set("status", key);
            }
            prev.set("page", "1");
            return prev;
        });
    };

    const clearFilters = () => {
        setSearchParams((prev) => {
            prev.delete("status");
            prev.set("page", "1");
            return prev;
        });
    };

    // Tab items for template status
    const tabItems = [
        {
            key: "all",
            label: "Todos",
            children: null,
        },
        {
            key: TemplateStatus.APPROVED,
            label: "Aprobados",
            children: null,
        },
        {
            key: TemplateStatus.DRAFT,
            label: "Borradores",
            children: null,
        },
        {
            key: TemplateStatus.IN_REVIEW,
            label: "En revisión",
        },
        {
            key: TemplateStatus.REJECTED,
            label: "Rechazados",
        },
    ];

    const handlePageChange = (newPage: number, newPageSize?: number) => {
        setSearchParams((prev) => {
            prev.set("page", newPage.toString());
            if (newPageSize) {
                prev.set("pageSize", newPageSize.toString());
            }
            return prev;
        });
    };

    return (
        <CrmLayout>
            <Modal
                centered
                className="max-w-4xl w-full"
                open={isCreateModalOpen}
                title={
                    <div className="w-full flex justify-center text-2xl py-4">
                        Agregar nueva plantilla
                    </div>
                }
                footer={false}
                onCancel={() => setIsCreateModalOpen(false)}
            >
                <CreateTemplateForm
                    closeModal={() => setIsCreateModalOpen(false)}
                    onFinish={() => setIsCreateModalOpen(false)}
                />
            </Modal>
            <div className="w-full h-full space-y-5">
                <div className="flex justify-between items-center">
                    <WelcomeBar helperText="Gestiona aquí las plantillas para mensajes de WhatsApp" />
                    <div className="flex gap-3">
                        <Button
                            type="primary"
                            size="large"
                            icon={<Plus size={16} />}
                            onClick={() => setIsCreateModalOpen(true)}
                        >
                            Agregar plantilla
                        </Button>
                    </div>
                </div>

                <div className="p-5 bg-white rounded-lg space-y-5">
                    <div className="flex justify-between items-center">
                        <Typography.Title level={3} className="!mb-0">
                            Plantillas
                        </Typography.Title>
                        <div className="flex items-center gap-3">
                            <Search
                                size="large"
                                enterButton
                                allowClear
                                placeholder="Buscar plantillas..."
                                onSearch={handleSetSearchQuery}
                                defaultValue={search}
                            />
                            <div className="relative">
                                <Button
                                    icon={<SlidersHorizontal size={16} />}
                                    onClick={() => setIsFilterDrawerOpen(true)}
                                >
                                    Filtros
                                </Button>

                                {/* filtros */}
                                <Drawer
                                    open={isFilterDrawerOpen}
                                    onClose={() => setIsFilterDrawerOpen(false)}
                                    placement="right"
                                >
                                    <div>Filtros</div>
                                </Drawer>

                                {status && (
                                    <Badge
                                        count={1}
                                        size="small"
                                        className="absolute -top-1 -right-1"
                                    />
                                )}
                            </div>
                        </div>
                    </div>

                    {/* Active Filters Summary */}
                    {false && (
                        <div className="flex flex-wrap items-center gap-2 p-3 bg-blue-50 rounded-lg border border-blue-200">
                            <Typography.Text
                                type="secondary"
                                className="text-sm font-medium"
                            >
                                Filtros activos:
                            </Typography.Text>
                            <Badge
                                count="Estado"
                                color="blue"
                                style={{ backgroundColor: "#1890ff" }}
                            />
                            <Button
                                type="link"
                                size="small"
                                onClick={clearFilters}
                                className="text-blue-600 hover:text-blue-800 p-0 h-auto"
                            >
                                Limpiar todos
                            </Button>
                        </div>
                    )}

                    {/* Tabs for template status */}
                    <Tabs
                        activeKey={status || "all"}
                        onChange={handleTabChange}
                        items={tabItems}
                        className="templates-tabs"
                    />

                    {/* Templates Table */}
                    <TemplatesTable templates={templates || []} isLoading={isLoading} />

                    {/* Pagination */}
                    {templates && totalCount > 0 && (
                        <div className="flex justify-between items-center mt-4">
                            <Typography.Text className="text-sm text-gray-500">
                                Mostrando {templates.length} de {totalCount} plantillas
                            </Typography.Text>
                            <Pagination
                                current={page}
                                pageSize={pageSize}
                                total={totalCount}
                                onChange={handlePageChange}
                                showSizeChanger
                                showQuickJumper
                                pageSizeOptions={["10", "20", "50", "100"]}
                                showTotal={(total, range) =>
                                    `${range[0]}-${range[1]} de ${total} plantillas`
                                }
                            />
                        </div>
                    )}
                </div>
            </div>
        </CrmLayout>
    );
}
