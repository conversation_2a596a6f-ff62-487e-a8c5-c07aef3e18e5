import { Typography, Tooltip } from "antd";
import { Mail, Phone, User } from "lucide-react";

const { Text } = Typography;

interface EnrollmentContactCellProps {
    fullName: string;
    email: string;
    phoneNumber: string;
    uid?: string;
}

export default function EnrollmentContactCell({
    fullName,
    email,
    phoneNumber,
    uid,
}: EnrollmentContactCellProps) {
    return (
        <div className="flex flex-col gap-1">
            {/* Full Name */}
            <div className="flex items-center gap-2">
                <User className="w-4 h-4 text-gray-500" />
                <Text strong className="text-sm">
                    {fullName || "N/A"}
                </Text>
            </div>

            {/* Email */}
            {email && (
                <div className="flex items-center gap-2">
                    <Mail className="w-4 h-4 text-blue-500" />
                    <Tooltip title={email}>
                        <Text
                            copyable={{ text: email }}
                            className="text-xs text-gray-600 max-w-[180px] truncate"
                        >
                            {email}
                        </Text>
                    </Tooltip>
                </div>
            )}

            {/* Phone Number */}
            {phoneNumber && (
                <div className="flex items-center gap-2">
                    <Phone className="w-4 h-4 text-green-500" />
                    <Tooltip title={phoneNumber}>
                        <Text
                            copyable={{ text: phoneNumber }}
                            className="text-xs text-gray-600"
                        >
                            {phoneNumber}
                        </Text>
                    </Tooltip>
                </div>
            )}

            {/* User ID (if available) */}
            {uid && (
                <div className="flex items-center gap-2">
                    <span className="text-xs text-gray-400">ID: {uid.slice(-6)}</span>
                </div>
            )}
        </div>
    );
}
