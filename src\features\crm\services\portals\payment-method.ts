import { PaginatedResponse } from "@myTypes/base";
import {
    PaymentMethod,
    CreatePaymentMethod,
} from "@/features/crm/types/payment-method";
import { portalsApi } from "@services/portals";

const DEFAULT_PAGE = 1;
const DEFAULT_PAGE_SIZE = 100;

export const getPaymentMethods = async (): Promise<
    PaginatedResponse<PaymentMethod>
> => {
    const response = await portalsApi.get("crm/payment-methods", {
        params: {
            page: DEFAULT_PAGE,
            pageSize: DEFAULT_PAGE_SIZE,
        },
    });
    return response.data;
};

export const createPaymentMethod = async (
    data: CreatePaymentMethod,
): Promise<PaymentMethod> => {
    const response = await portalsApi.post("crm/payment-methods", data);
    return response.data;
};
