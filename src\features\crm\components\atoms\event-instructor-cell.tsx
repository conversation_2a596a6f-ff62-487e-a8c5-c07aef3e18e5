import { Event } from "../../types/event";
import { User } from "lucide-react";

type EventInstructorCellProps = {
    instructor: Event["instructor"];
    offering: Event["offering"];
};

export default function EventInstructorCell({ instructor }: EventInstructorCellProps) {
    return (
        <div className="px-3 py-2">
            {instructor ? (
                <div className="flex items-center gap-2">
                    <div className="flex-shrink-0 w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                        <User size={16} className="text-indigo-600" strokeWidth={2} />
                    </div>
                    <div className="min-w-0 flex-1">
                        <span className="text-sm font-medium text-gray-900 truncate block">
                            {instructor.fullName}
                        </span>
                    </div>
                </div>
            ) : (
                <div className="flex items-center gap-2 text-gray-400">
                    <div className="flex-shrink-0 w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                        <User size={16} className="text-gray-400" strokeWidth={1.5} />
                    </div>
                    <span className="text-sm italic">Sin instructor asignado</span>
                </div>
            )}
        </div>
    );
}
