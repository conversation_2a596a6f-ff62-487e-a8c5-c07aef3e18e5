import { AxiosInstance, AxiosResponse, InternalAxiosRequestConfig } from "axios";
import axios from "axios";
import { camelizeKeys, decamelize, decamelize<PERSON><PERSON>s } from "humps";

export class AxiosInstanceCreator {
    protected apiBaseUrl: string;
    protected getTokenFn: () => string | null;

    constructor(apiBaseUrl: string, getTokenFn: () => string | null) {
        this.apiBaseUrl = apiBaseUrl;
        this.getTokenFn = getTokenFn;
    }

    public createApi(): AxiosInstance {
        const axiosInstance = axios.create({
            baseURL: this.apiBaseUrl,
        });

        axiosInstance.interceptors.request.use(
            (config: InternalAxiosRequestConfig) => {
                const token = this.getTokenFn();
                if (token) {
                    config.headers.Authorization = `Token ${token}`;
                }

                if (config.data) {
                    if (config.data instanceof FormData) {
                        config.data = this.decamelizeFormData(config.data);
                    } else {
                        if (typeof config.data === "string") {
                            config.data = JSON.parse(config.data);
                        }
                        config.data = decamelizeKeys(config.data, { separator: "_" });
                    }
                }
                if (config.params) {
                    if (typeof config.params === "string") {
                        config.params = JSON.parse(config.params);
                    }
                    config.params = decamelizeKeys(config.params, { separator: "_" });
                }
                return config;
            },
            (error) => {
                return Promise.reject(error);
            },
        );

        axiosInstance.interceptors.response.use(
            (response: AxiosResponse) => {
                if (response.data) {
                    const params = response.config.params;
                    if (params) {
                        const camelizedParams = camelizeKeys(params);
                        const originalValues: Record<string, unknown> = {};

                        // Verificar si campos por excluir
                        const fieldsToExclude = camelizedParams.excludeFromCamelize;
                        if (fieldsToExclude?.length) {
                            fieldsToExclude.forEach(
                                (field: keyof typeof response.data) => {
                                    if (field in response.data) {
                                        // Guardar valor original
                                        originalValues[field as string] =
                                            response.data[field];
                                    }
                                },
                            );
                        }

                        response.data = camelizeKeys(response.data);

                        // restaurar valores de campos excluidos
                        if (fieldsToExclude?.length) {
                            Object.entries(originalValues).forEach(([field, value]) => {
                                response.data[field] = value;
                            });
                        }
                    } else {
                        response.data = camelizeKeys(response.data);
                    }
                }
                return response;
            },
            (error) => {
                console.error("Error in axios instance:", error);
                return Promise.reject(error);
            },
        );

        return axiosInstance;
    }

    private decamelizeFormData(formData: FormData): FormData {
        const decamelizedFormData = new FormData();
        formData.forEach((value, key) => {
            const decamelizedKey = decamelize(key, { separator: "_" });
            if (value instanceof File) {
                decamelizedFormData.append(decamelizedKey, value, value.name);
            } else {
                decamelizedFormData.append(decamelizedKey, value);
            }
        });
        return decamelizedFormData;
    }
}
