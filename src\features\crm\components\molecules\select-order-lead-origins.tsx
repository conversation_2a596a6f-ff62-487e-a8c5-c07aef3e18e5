import {
    Button,
    Divider,
    Form,
    Input,
    Modal,
    Select,
    SelectProps,
    Spin,
    Tooltip,
} from "antd";
import { Plus, ChevronLeft, ChevronRight } from "lucide-react";
import React, { useState } from "react";
import { useCreateLeadOrigin, useLeadOrigins } from "../../hooks/use-lead-origins";
import { ListLeadOrigin } from "../../types/lead-origins";
import FormLabel from "../atoms/FormLabel";
import queryClient from "@lib/queryClient";
import { useDebounce } from "@hooks/use-debounce";

const { TextArea } = Input;

type LeadOriginCreateForm = {
    name: string;
    description?: string;
};

interface SelectOrderLeadOriginsProps extends Omit<SelectProps<string[]>, "options"> {}
export default function SelectOrderLeadOrigins({
    value,
    onChange,
    ...restProps
}: SelectOrderLeadOriginsProps) {
    const [modalOpen, setModalOpen] = useState(false);
    // pagination & search state
    const [page, setPage] = useState(1);
    const [search, setSearch] = useState("");
    const debouncedSearch = useDebounce(search, 400);
    const pageSize = 20;
    const [open, setOpen] = useState(false);

    const { leadOrigins, COUNT, isLoading } = useLeadOrigins({
        page,
        pageSize,
        query: { search: debouncedSearch || undefined },
    });

    const { mutate: createLeadOriginMutate, isPending } = useCreateLeadOrigin({
        onSuccess: () => {
            setModalOpen(false);
            form.resetFields();
        },
    });

    // build known labels map from TanStack Query cache (compute per render)
    const knownMap = (() => {
        type LOData = { count: number; results: ListLeadOrigin[] };
        const map = new Map<string, string>();
        const entries = queryClient.getQueriesData<LOData>({
            queryKey: ["lead-origins"],
        });
        entries.forEach((tuple: [unknown, LOData | undefined]) => {
            const data = tuple[1];
            const results = data?.results as Array<ListLeadOrigin> | undefined;
            results?.forEach((lo: ListLeadOrigin) => map.set(lo.loid, lo.name));
        });
        return map;
    })();

    const pageOptions = leadOrigins.map((lo: ListLeadOrigin) => ({
        value: lo.loid,
        label: lo.name,
    }));

    const selectedOptions = (() => {
        const ids = (value || []) as string[];
        return ids
            .filter(
                (id) =>
                    !pageOptions.some(
                        (o: { value: string; label: string }) => o.value === id,
                    ),
            )
            .map((id) => ({ value: id, label: knownMap.get(id) ?? id }));
    })();

    const options = (() => {
        // merge selected options first so tags have labels
        const map = new Map<string, { value: string; label: string }>();
        selectedOptions.forEach((o: { value: string; label: string }) =>
            map.set(o.value, o),
        );
        pageOptions.forEach((o: { value: string; label: string }) =>
            map.set(o.value, o),
        );
        return Array.from(map.values());
    })();

    const start = (page - 1) * pageSize + 1;
    const end = Math.min(page * pageSize, COUNT || 0);
    const hasPrev = page > 1;
    const hasNext = page * pageSize < (COUNT || 0);

    const [form] = Form.useForm();

    const handleCreateLeadOriginFinish = (values: LeadOriginCreateForm) => {
        const { name, description } = values;
        createLeadOriginMutate({ name, description });
    };

    return (
        <>
            <Modal
                title={
                    <div className="text-lg font-semibold text-center">
                        Crear Fuente de Lead
                    </div>
                }
                footer={false}
                open={modalOpen}
                centered
                onCancel={() => {
                    setModalOpen(false);
                }}
            >
                <Form
                    name="create-lead-origin-form"
                    form={form}
                    layout="vertical"
                    onFinish={handleCreateLeadOriginFinish}
                    initialValues={{
                        name: "",
                    }}
                >
                    <Form.Item<LeadOriginCreateForm>
                        name="name"
                        label={<FormLabel>Nombre</FormLabel>}
                        rules={[
                            {
                                required: true,
                                message:
                                    "Por favor, ingresa el nombre de la fuente de lead",
                            },
                        ]}
                    >
                        <Input placeholder="Ej. Facebook Ads" />
                    </Form.Item>
                    <Form.Item<LeadOriginCreateForm>
                        name="description"
                        label={<FormLabel>Descripción</FormLabel>}
                    >
                        <TextArea placeholder="Ej. Campaña de Facebook Ads para el programa de inglés" />
                    </Form.Item>
                    <div className="flex justify-end">
                        <Button
                            className="mr-2"
                            onClick={() => {
                                setModalOpen(false);
                                form.resetFields();
                            }}
                        >
                            Cancelar
                        </Button>
                        <Button type="primary" htmlType="submit" loading={isPending}>
                            Crear
                        </Button>
                    </div>
                </Form>
            </Modal>
            <Select
                {...restProps}
                value={value}
                onChange={onChange}
                options={options}
                mode="multiple"
                allowClear
                placeholder="Selecciona las fuentes del lead"
                showSearch
                open={open}
                onDropdownVisibleChange={(v: boolean) => {
                    setOpen(v);
                    if (v) {
                        if (search) setSearch("");
                        if (page !== 1) setPage(1);
                    }
                }}
                filterOption={false}
                onSearch={(val: string) => {
                    if (val !== search) {
                        setSearch(val);
                        if (page !== 1) setPage(1);
                    }
                }}
                notFoundContent={
                    isLoading ? (
                        <Spin size="small" />
                    ) : (
                        <div className="px-2 py-1 text-xs text-gray-400">
                            Sin resultados
                        </div>
                    )
                }
                dropdownRender={(menu: React.ReactNode) => (
                    <>
                        {menu}
                        <Divider className="my-1" />
                        <div
                            className="flex justify-between items-center px-2 pb-2 gap-2"
                            onMouseDown={(e: React.MouseEvent<HTMLDivElement>) =>
                                e.preventDefault()
                            }
                        >
                            <span className="text-xs text-gray-600">
                                Mostrando {COUNT ? `${start}-${end}` : 0} de{" "}
                                {COUNT || 0}
                            </span>
                            <div className="flex items-center gap-2">
                                <Tooltip title="Anterior">
                                    <Button
                                        size="small"
                                        shape="circle"
                                        icon={<ChevronLeft size={14} />}
                                        onClick={() =>
                                            setPage((p: number) => Math.max(1, p - 1))
                                        }
                                        disabled={!hasPrev || isLoading}
                                    />
                                </Tooltip>
                                <Tooltip title="Siguiente">
                                    <Button
                                        size="small"
                                        shape="circle"
                                        type="primary"
                                        icon={<ChevronRight size={14} />}
                                        onClick={() => setPage((p: number) => p + 1)}
                                        disabled={!hasNext || isLoading}
                                        loading={isLoading}
                                    />
                                </Tooltip>
                            </div>
                        </div>
                        <Divider className="my-1" />
                        <div
                            className="flex justify-between items-center px-2"
                            onMouseDown={(e: React.MouseEvent<HTMLDivElement>) =>
                                e.preventDefault()
                            }
                        >
                            <p className="text-sm text-gray-700 font-medium">
                                ¿No encuentras la fuente del lead que buscas?
                            </p>
                            <Button
                                size="small"
                                type="primary"
                                icon={<Plus size={12} />}
                                onClick={() => setModalOpen(true)}
                            >
                                Agregar
                            </Button>
                        </div>
                    </>
                )}
            />
        </>
    );
}
