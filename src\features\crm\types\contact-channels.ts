type AuditBaseType = {
    createdAt: string;
    updatedAt: string;
};

export type BaseContactChannel = {
    name: string;
};

export type ListContactChannel = {
    lsid: string;
} & BaseContactChannel &
    AuditBaseType;

export type RetrieveContactChannel = {
    lsid: string;
    description?: string;
} & AuditBaseType;

export type ContactChannelCreate = {
    description?: string;
} & BaseContactChannel;
