import { useParams, Navigate } from "react-router-dom";
import { useActivity } from "@/features/crm/hooks/use-activity";
import CrmLayout from "@/features/crm/layout";
import EditActivityForm from "@/features/crm/components/organisms/edit-activity-form";
import WelcomeBar from "@components/shared/molecules/WelcomeBar";
import { Breadcrumb, Tabs, TabsProps, Typography, Spin } from "antd";
import { Link } from "react-router-dom";
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";

const { Text } = Typography;

function ActivityDetail({ aid }: { aid: string }) {
    const navigate = useNavigate();
    const { activity, isLoading, isError } = useActivity(aid);

    useEffect(() => {
        if (isError) {
            navigate("/crm/activities");
        }
    }, [isError, navigate]);

    const tabItems: TabsProps["items"] = [
        {
            key: "general",
            label: "Detalles",
            children: activity ? <EditActivityForm activity={activity} /> : <Spin />,
        },
    ];

    return (
        <div className="w-full h-full space-y-5 max-w-7xl">
            <div className="flex justify-between items-center">
                <WelcomeBar helperText="Visualiza & Edita los campos necesarios" />
            </div>
            {isLoading ? (
                <div className="flex justify-center items-center py-8">
                    <Spin size="large" />
                </div>
            ) : (
                <>
                    <div className="p-5 bg-white-full rounded-lg space-y-5 shadow-sm">
                        <div className="flex items-center">
                            <Breadcrumb
                                separator=">"
                                items={[
                                    {
                                        title: (
                                            <Link
                                                to="/crm/activities"
                                                className="text-base"
                                            >
                                                Actividades
                                            </Link>
                                        ),
                                    },
                                    {
                                        title: (
                                            <Link
                                                to={`/crm/activities/${aid}`}
                                                className="text-base"
                                            >
                                                {activity?.title || "Sin título"}
                                                {" #"}
                                                {activity?.aid.slice(-6)}
                                            </Link>
                                        ),
                                    },
                                    {
                                        title: (
                                            <Text className="text-base">
                                                Ver & Editar
                                            </Text>
                                        ),
                                    },
                                ]}
                            />
                        </div>
                    </div>
                    <div className="space-y-5">
                        <Tabs items={tabItems} />
                    </div>
                </>
            )}
        </div>
    );
}

export default function ActivityDetailPage() {
    const { aid } = useParams<{ aid: string }>();

    if (!aid) {
        return <Navigate to="/crm/activities" replace />;
    }

    return (
        <CrmLayout>
            <ActivityDetail aid={aid} />
        </CrmLayout>
    );
}
