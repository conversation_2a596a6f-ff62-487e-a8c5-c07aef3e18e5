import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ge } from "antd";
import { Plus } from "lucide-react";
import { useActivities } from "../../hooks/use-activity";
import { RetrieveOrder } from "../../types/order";
import { ActivityQueryParams } from "../../types/activity";
import ActivitiesTable from "./activities-table";
import CreateActivityForm from "./create-activity-form";

const { Text } = Typography;

export default function OrderActivitiesTabs({ order }: { order: RetrieveOrder }) {
    const [isCreateActivityModalOpen, setIsCreateActivityModalOpen] =
        useState<boolean>(false);

    const queryParams: ActivityQueryParams = {
        order: order.oid,
    };
    const { activities } = useActivities({ queryParams });

    const handleCreateActivityModalOpen = () => {
        setIsCreateActivityModalOpen(true);
    };

    const handleCreateActivityModalClose = () => {
        setIsCreateActivityModalOpen(false);
        // La lista se refrescará automáticamente gracias a la invalidación de queries en useCreateActivity
    };

    return (
        <div className="space-y-4">
            {/* Header con botón para agregar actividad */}
            <div className="flex justify-between items-center">
                <Text className="text-black-medium text-xl font-semibold">
                    Actividades{" "}
                    <Badge count={activities.length} color="green" size="default" />
                </Text>
                <Button
                    type="primary"
                    size="large"
                    icon={<Plus />}
                    onClick={handleCreateActivityModalOpen}
                >
                    Agregar Actividad
                </Button>
            </div>

            {/* Modal para crear actividad */}
            <Modal
                centered
                open={isCreateActivityModalOpen}
                onCancel={handleCreateActivityModalClose}
                footer={false}
                title="Agregar nueva actividad"
                width={800}
            >
                <CreateActivityForm
                    onFinish={handleCreateActivityModalClose}
                    preselectedOrder={order}
                />
            </Modal>

            {/* Tabla de actividades */}
            <ActivitiesTable initialData={activities} />
        </div>
    );
}
