import { <PERSON><PERSON>, <PERSON>confirm, PopconfirmProps } from "antd";
import { XCircleIcon } from "lucide-react";
import React, { useState } from "react";

interface DeleteConfirmProps extends PopconfirmProps {
    customTrigger: React.ReactElement | string;
    onConfirm: <T>() => Promise<T | void>;
}

export function DeleteConfirm({
    onConfirm,
    customTrigger,
    ...props
}: DeleteConfirmProps) {
    const [open, setOpen] = useState(false);
    const [confirmLoading, setConfirmLoading] = useState(false);

    async function handleOk() {
        setConfirmLoading(true);

        try {
            await onConfirm();
            setOpen(false);
        } catch (error) {
            console.error(error);
        } finally {
            setConfirmLoading(false);
        }
    }

    const showPopconfirm = () => {
        setOpen(true);
    };

    function handleCancel() {
        setOpen(false);
    }

    return (
        <Popconfirm
            {...props}
            icon={
                <div className="flex items-center justify-center mt-0.5 mr-1">
                    <XCircleIcon className="w-4 h-4 text-state-red-full" />
                </div>
            }
            placement="top"
            open={open}
            onConfirm={handleOk}
            okButtonProps={{ loading: confirmLoading }}
            onCancel={handleCancel}
        >
            {typeof customTrigger === "string" ? (
                <Button type="primary" onClick={showPopconfirm}>
                    {customTrigger}
                </Button>
            ) : // inyectar en el customTrigger onclick el showPopconfirm
            React.isValidElement(customTrigger) ? (
                React.cloneElement(customTrigger as React.ReactElement, {
                    onClick: showPopconfirm,
                })
            ) : null}
        </Popconfirm>
    );
}
