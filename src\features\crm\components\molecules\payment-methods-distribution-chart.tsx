import { Card, Skeleton } from "antd";
import { CreditCard } from "lucide-react";
import { Re<PERSON>ons<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Cell, Too<PERSON><PERSON>, Legend } from "recharts";

import {
    useDashboardPaymentsMethodsDistribution,
    createDashboardPaymentsQueryParams,
} from "../../hooks/use-dashboard-payments";
import { useSearchParams } from "react-router-dom";

interface PaymentMethodsDistributionChartProps {
    className?: string;
}

export default function PaymentMethodsDistributionChart({
    className,
}: PaymentMethodsDistributionChartProps) {
    const [searchParams] = useSearchParams();

    // Create query parameters for methods distribution data
    const queryParams = createDashboardPaymentsQueryParams(searchParams);

    // Fetch methods distribution data
    const { data: methodsData, isLoading } =
        useDashboardPaymentsMethodsDistribution(queryParams);

    // Transform data for chart - filter out duplicates and take top 8
    const uniqueMethods = methodsData?.data?.reduce(
        (acc, item) => {
            const key = item.paymentMethod.name;
            if (!acc[key] || acc[key].count < item.count) {
                acc[key] = item;
            }
            return acc;
        },
        {} as Record<string, (typeof methodsData.data)[0]>,
    );

    const chartData = Object.values(uniqueMethods || {})
        .sort((a, b) => b.count - a.count)
        .slice(0, 8)
        .map((item) => ({
            name:
                item.paymentMethod.name === "No Payment Method"
                    ? "Sin método"
                    : item.paymentMethod.name,
            value: item.count,
            percentage: item.percentage,
            pmid: item.paymentMethod.pmid,
        }));

    // Colors for different payment methods
    const colors = [
        "#1890ff",
        "#52c41a",
        "#fa8c16",
        "#722ed1",
        "#eb2f96",
        "#13c2c2",
        "#faad14",
        "#f5222d",
    ];

    interface TooltipProps {
        active?: boolean;
        payload?: Array<{
            payload: {
                name: string;
                value: number;
                percentage: number;
                pmid: string | null;
            };
        }>;
    }

    const CustomTooltip = ({ active, payload }: TooltipProps) => {
        if (active && payload && payload.length) {
            const data = payload[0].payload;
            return (
                <div
                    style={{
                        backgroundColor: "white",
                        padding: "12px",
                        border: "1px solid #d9d9d9",
                        borderRadius: "8px",
                        boxShadow:
                            "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
                        minWidth: "180px",
                        zIndex: 1000,
                    }}
                >
                    <p
                        style={{
                            fontWeight: 600,
                            color: "#262626",
                            marginBottom: "8px",
                            fontSize: "14px",
                        }}
                    >
                        {data.name}
                    </p>
                    <div style={{ marginBottom: "4px" }}>
                        <span style={{ fontSize: "12px", color: "#595959" }}>
                            Cantidad:{" "}
                        </span>
                        <span style={{ fontWeight: 500, color: "#262626" }}>
                            {data.value.toLocaleString()} pagos
                        </span>
                    </div>
                    <div>
                        <span style={{ fontSize: "12px", color: "#595959" }}>
                            Porcentaje:{" "}
                        </span>
                        <span style={{ fontWeight: 500, color: "#262626" }}>
                            {data.percentage.toFixed(1)}%
                        </span>
                    </div>
                </div>
            );
        }
        return null;
    };

    return (
        <Card
            title={
                <div className="flex items-center">
                    <CreditCard className="mr-2 h-5 w-5 text-blue-500" />
                    <span>Por Método de Pago</span>
                </div>
            }
            className={className}
            extra={
                <div className="text-sm text-gray-500">
                    Total: {methodsData?.totalPayments?.toLocaleString() || 0} pagos
                </div>
            }
        >
            {isLoading ? (
                <Skeleton active paragraph={{ rows: 6 }} />
            ) : (
                <div>
                    <div style={{ width: "100%", height: 300 }}>
                        <ResponsiveContainer>
                            <PieChart>
                                <Pie
                                    data={chartData}
                                    cx="50%"
                                    cy="50%"
                                    outerRadius={80}
                                    dataKey="value"
                                    label={(entry) => `${entry.percentage.toFixed(1)}%`}
                                    labelLine={false}
                                >
                                    {chartData.map((_, index) => (
                                        <Cell
                                            key={`cell-${index}`}
                                            fill={colors[index % colors.length]}
                                        />
                                    ))}
                                </Pie>
                                <Tooltip content={<CustomTooltip />} />
                                <Legend />
                            </PieChart>
                        </ResponsiveContainer>
                    </div>

                    {chartData.length === 0 && !isLoading && (
                        <div className="text-center py-8 text-gray-500">
                            No hay datos disponibles para el período seleccionado
                        </div>
                    )}
                </div>
            )}
        </Card>
    );
}
