import { <PERSON>, Button, message, Typography, Space, Tooltip } from "antd";
import { ExternalLink, Copy, Link2 } from "lucide-react";

const { Text, Link } = Typography;

interface GeneralEnrollmentUrlProps {
    /**
     * The general enrollment URL for the event
     */
    enrollmentUrl: string;
    /**
     * Whether the event is marked as general
     */
    isGeneral: boolean;
    /**
     * Custom class name for styling
     */
    className?: string;
}

/**
 * Component that displays a general enrollment URL when the event is marked as general
 * Shows the URL with actions to open externally and copy to clipboard
 */
export default function GeneralEnrollmentUrl({
    enrollmentUrl,
    isGeneral,
    className,
}: GeneralEnrollmentUrlProps) {
    /**
     * Copies the enrollment URL to clipboard and shows success message
     */
    const handleCopyToClipboard = async () => {
        try {
            await navigator.clipboard.writeText(enrollmentUrl);
            message.success("URL de inscripción copiada al portapapeles");
        } catch (error) {
            message.error("Error al copiar la URL");
        }
    };

    /**
     * Opens the enrollment URL in a new tab
     */
    const handleOpenExternal = () => {
        window.open(enrollmentUrl, "_blank", "noopener,noreferrer");
    };

    /**
     * Truncates long URLs for better display
     */
    const truncateUrl = (url: string, maxLength: number = 60) => {
        if (url.length <= maxLength) return url;
        return `${url.substring(0, maxLength)}...`;
    };

    // Don't render if not a general event or no URL
    if (!isGeneral || !enrollmentUrl) {
        return null;
    }

    return (
        <div className={className}>
            <Text strong className="text-gray-400 text-sm mb-2 block">
                ENLACE GENERAL DE INSCRIPCIÓN
            </Text>
            <Card
                size="small"
                className="border border-blue-200 bg-blue-50"
                bodyStyle={{ padding: "12px 16px" }}
            >
                <Space direction="vertical" size={4} className="w-full">
                    <Space align="center" className="w-full justify-between">
                        <Space align="center">
                            <Link2 size={16} className="text-blue-600" />
                            <Text strong className="text-blue-800">
                                Enlace de Inscripción General
                            </Text>
                        </Space>
                        <Space>
                            <Tooltip title="Abrir en nueva pestaña">
                                <Button
                                    type="text"
                                    size="small"
                                    icon={<ExternalLink size={14} />}
                                    onClick={handleOpenExternal}
                                    className="text-blue-600 hover:bg-blue-100"
                                />
                            </Tooltip>
                            <Tooltip title="Copiar al portapapeles">
                                <Button
                                    type="text"
                                    size="small"
                                    icon={<Copy size={14} />}
                                    onClick={handleCopyToClipboard}
                                    className="text-blue-600 hover:bg-blue-100"
                                />
                            </Tooltip>
                        </Space>
                    </Space>
                    <Link
                        href={enrollmentUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 text-sm break-all"
                    >
                        {truncateUrl(enrollmentUrl)}
                    </Link>
                </Space>
            </Card>
        </div>
    );
}
