import {
    Popover,
    Button,
    PopoverProps,
    Form,
    Select,
    Checkbox,
    DatePicker,
} from "antd";
import Filter from "@assets/icons/huge/filter.svg?react";
import type { FilterPanelItem } from "@myTypes/filter";
import { useMutateSearchParams } from "@hooks/use-mutate-search-params";
import { useCallback } from "react";
import { parseFilterToRecord } from "@lib/filters";

interface FiltersProps<T> extends PopoverProps {
    filters: FilterPanelItem<T>[];
    initialValues?: Partial<T>;
}

export default function Filters<T>({
    filters,
    initialValues,
    ...popoverProps
}: FiltersProps<T>) {
    const [form] = Form.useForm<T>();

    const { mutateManySearchParams } = useMutateSearchParams();

    const handleFilterChange = useCallback((changedValueObj: Partial<T>) => {
        if (!changedValueObj) return;
        const newParams = parseFilterToRecord(changedValueObj);
        mutateManySearchParams(newParams);
    }, [ mutateManySearchParams]);

    // Render filter input based on filter type
    const renderFilterInput = (filter: FilterPanelItem<T>) => {
        switch (filter.type) {
            case "checkbox": {
                return <Checkbox />;
            }

            case "date": {
                return <DatePicker {...filter.datepickerprops} />;
            }

            case "rangepicker": {
                return <DatePicker.RangePicker {...filter.datepickerprops} />;
            }

            case "select": {
                const { options, optionsQuery, ...selectProps } = filter.selectprops;

                if (options === "custom" && optionsQuery) {
                    const { data, isLoading, isFetching } = optionsQuery();
                    return (
                        <Select
                            {...selectProps}
                            allowClear={selectProps.allowClear || true}
                            loading={isLoading || isFetching}
                            options={data}
                        />
                    );
                }

                return (
                    <Select
                        {...selectProps}
                        options={options}
                        allowClear={selectProps.allowClear || true}
                    />
                );
            }

            default: {
                throw new Error("Invalid filter type");
            }
        }
    };

    // Render filter component
    // TODO: Add support for drawer layout
    return (
        <Popover
            {...popoverProps}
            content={
                <div className="p-2 space-y-3 min-w-96">
                    <div className="uppercase text-black-medium font-medium">
                        Filtrar registros
                    </div>
                    <Form
                        form={form}
                        layout="vertical"
                        className="space-y-2"
                        initialValues={initialValues || undefined}
                        onValuesChange={(changed) => handleFilterChange(changed)}
                    >
                        {filters.map((filter, indx) => (
                            <Form.Item
                                {...filter}
                                valuePropName={
                                    filter.type === "checkbox" ? "checked" : "value"
                                }
                                key={indx}
                                className="w-full"
                            >
                                {renderFilterInput(filter)}
                            </Form.Item>
                        ))}
                    </Form>
                </div>
            }
            trigger={["click"]}
            placement="bottomRight"
        >
            <Button icon={<Filter />} size="large" type="text" />
        </Popover>
    );
}
