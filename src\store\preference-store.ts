import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";

export type PreferenceState = {
    currentApp: string;
    tasksToPoll?: Record<string, string>;
};

export type PreferenceActions = {
    setCurrentApp: (currentApp: string) => void;
};

export type PreferenceStore = PreferenceState & PreferenceActions;

const initialState: PreferenceState = {
    currentApp: "erp",
};

export const usePreferenceStore = create<PreferenceStore>()(
    persist(
        (set) => ({
            ...initialState,
            setCurrentApp: (currentApp: string) => {
                set((state) => ({ ...state, currentApp }));
            },
        }),
        {
            name: "preference-storage",
            storage: createJSONStorage(() => localStorage),
        },
    ),
);
