import React from 'react';

interface Heading {
  id: string;
  text: string;
  level: number;
}

interface TableOfContentsProps {
  headings: Heading[];
}

/**
 * Component to display a table of contents based on headings
 */
const TableOfContents: React.FC<TableOfContentsProps> = ({ headings }) => {
  const scrollToHeading = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  function removeOrdinalNumberFromHeading(heading: string): string {
    const match = heading.match(/^(\d+)\.\s(.*)$/);
    return match ? match[2] : heading;
  }

  return (
    <div className="blog-toc">
      <ul className="blog-toc-list">
        {headings.map((heading) => (
          <li 
            key={heading.id}
            className={`blog-toc-item blog-toc-level-${heading.level}`}
            onClick={() => scrollToHeading(heading.id)}
          >
            {removeOrdinalNumberFromHeading(heading.text)}
          </li>
        ))}
      </ul>
    </div>
  );
};

export default TableOfContents;
