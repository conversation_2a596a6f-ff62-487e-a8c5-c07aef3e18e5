import { Typography, Tooltip } from "antd";
import { BookOpen, School, Calendar } from "lucide-react";
import { ContactListItem } from "@/features/crm/types/contact";

const { Text } = Typography;

type EducationInfoCellProps = {
    contact: ContactListItem;
    showLabels?: boolean;
};

const EducationInfoCell = ({ contact, showLabels = true }: EducationInfoCellProps) => {
    const { educationalInstitution, major, term } = contact;

    // If no education info exists, return a placeholder
    if (!educationalInstitution && !major && !term) {
        return (
            <Text type="secondary" italic>
                No hay información educativa
            </Text>
        );
    }

    return (
        <div className="flex flex-col gap-1.5">
            {/* Educational Institution */}
            {educationalInstitution && (
                <div className="flex items-start gap-1.5">
                    <School
                        size={14}
                        className="text-blue-500 mt-0.5"
                        strokeWidth={1.75}
                    />
                    <div className="flex flex-col">
                        {showLabels && (
                            <Text className="text-xs text-gray-500 font-medium">
                                Institución
                            </Text>
                        )}
                        <Tooltip title={educationalInstitution.name}>
                            <Text className="text-sm truncate max-w-[200px]">
                                {educationalInstitution.name}
                            </Text>
                        </Tooltip>
                    </div>
                </div>
            )}

            {/* Major/Area of Study */}
            {major && (
                <div className="flex items-start gap-1.5">
                    <BookOpen
                        size={14}
                        className="text-blue-500 mt-0.5"
                        strokeWidth={1.75}
                    />
                    <div className="flex flex-col">
                        {showLabels && (
                            <Text className="text-xs text-gray-500 font-medium">
                                Carrera/Área
                            </Text>
                        )}
                        <Tooltip title={major}>
                            <Text className="text-sm truncate max-w-[200px]">
                                {major}
                            </Text>
                        </Tooltip>
                    </div>
                </div>
            )}

            {/* Term/Semester */}
            {term && (
                <div className="flex items-start gap-1.5">
                    <Calendar
                        size={14}
                        className="text-blue-500 mt-0.5"
                        strokeWidth={1.75}
                    />
                    <div className="flex flex-col">
                        {showLabels && (
                            <Text className="text-xs text-gray-500 font-medium">
                                Semestre/Periodo
                            </Text>
                        )}
                        <Text className="text-sm">{term}</Text>
                    </div>
                </div>
            )}
        </div>
    );
};

export default EducationInfoCell;
