import { PaginatedResponse } from "@myTypes/base";
import { portalsApi } from "@services/portals";
import { ContactChannelCreate, ListContactChannel } from "../../types/contact-channels";

export type ListContactChannelsParams = {
    page?: number;
    pageSize?: number;
    search?: string;
};

export const getContactChannels = async (
    params: ListContactChannelsParams = {},
): Promise<PaginatedResponse<ListContactChannel>> => {
    const response = await portalsApi.get("crm/contact-channels", {
        params,
    });
    return response.data;
};

export const createContactChannel = async (contactchannel: ContactChannelCreate) => {
    const response = await portalsApi.post("crm/contact-channels", contactchannel);
    return response.data;
};
