import React from "react";
import { Card, Select, DatePicker, Input, Button } from "antd";
import { ContactOcupation, ContactOcupationLabel } from "@/features/crm/types/contact";
import { SearchIcon, RefreshCwIcon } from "lucide-react";
import locale from "antd/es/date-picker/locale/es_ES";

const { RangePicker } = DatePicker;
const { Option } = Select;

import { Dayjs } from "dayjs";

interface FilterValues {
    search?: string;
    dateRange?: [Dayjs, Dayjs] | null;
    ocupation?: ContactOcupation[];
    active?: boolean | null;
}

interface FilterCardProps {
    onFilterChange: (filters: FilterValues) => void;
}

const FilterCard: React.FC<FilterCardProps> = ({ onFilterChange }) => {
    const [filters, setFilters] = React.useState<FilterValues>({});

    const handleFilterChange = <T extends keyof FilterValues>(
        key: T,
        value: FilterValues[T],
    ) => {
        const newFilters = {
            ...filters,
            [key]: value,
        };
        setFilters(newFilters);
        onFilterChange(newFilters);
    };

    const handleReset = () => {
        setFilters({});
        onFilterChange({});
    };

    return (
        <Card className="shadow-md mb-6">
            <div className="flex flex-wrap gap-4">
                <div className="flex-grow min-w-[200px]">
                    <p className="text-sm text-gray-500 mb-1">Buscar</p>
                    <Input
                        placeholder="Nombre, email o teléfono"
                        prefix={<SearchIcon className="h-4 w-4 text-gray-400" />}
                        value={filters.search}
                        onChange={(e) => handleFilterChange("search", e.target.value)}
                        allowClear
                    />
                </div>

                <div className="flex-grow min-w-[200px]">
                    <p className="text-sm text-gray-500 mb-1">Fecha de registro</p>
                    <RangePicker
                        className="w-full"
                        locale={locale}
                        onChange={(dates) => {
                            if (dates && dates[0] && dates[1]) {
                                handleFilterChange("dateRange", [dates[0], dates[1]]);
                            } else {
                                handleFilterChange("dateRange", null);
                            }
                        }}
                        value={filters.dateRange ? filters.dateRange : null}
                    />
                </div>

                <div className="min-w-[150px]">
                    <p className="text-sm text-gray-500 mb-1">Ocupación</p>
                    <Select
                        mode="multiple"
                        placeholder="Seleccionar"
                        className="w-full"
                        value={filters.ocupation}
                        onChange={(value) => handleFilterChange("ocupation", value)}
                        allowClear
                    >
                        {Object.entries(ContactOcupationLabel).map(([value, label]) => (
                            <Option key={value} value={value}>
                                {label}
                            </Option>
                        ))}
                    </Select>
                </div>

                <div className="min-w-[150px]">
                    <p className="text-sm text-gray-500 mb-1">Estado</p>
                    <Select
                        placeholder="Seleccionar"
                        className="w-full"
                        value={filters.active}
                        onChange={(value) => handleFilterChange("active", value)}
                        allowClear
                    >
                        <Option value={true}>Activo</Option>
                        <Option value={false}>Inactivo</Option>
                    </Select>
                </div>

                <div className="flex items-end">
                    <Button
                        type="default"
                        icon={<RefreshCwIcon className="h-4 w-4" />}
                        onClick={handleReset}
                    >
                        Reiniciar
                    </Button>
                </div>
            </div>
        </Card>
    );
};

export default FilterCard;
