import React from "react";
import { Card, Row, Col, Statistic, Progress, Divider } from "antd";
import { TrendingUp, TrendingDown, Calendar } from "lucide-react";
import type {
    CurrentMonthPerformance,
    CurrentMonthPerformanceMetric,
} from "../../types/dashboard/orders";

interface SalesPerformanceCardProps {
    title: string;
    data: CurrentMonthPerformance | undefined;
    icon?: React.ReactNode;
}

export default function SalesPerformanceCard({
    title,
    data,
    icon = <Calendar className="h-5 w-5 text-blue-500" />,
}: SalesPerformanceCardProps) {
    // Helper function to render trend indicators
    const renderTrend = ({
        change,
        tendency,
        isPercentage = false,
        percentageChange,
    }: {
        change: number;
        tendency: CurrentMonthPerformanceMetric["tendency"];
        isPercentage?: boolean;
        percentageChange?: number;
    }) => {
        const formattedChange = isPercentage
            ? `${change > 0 ? "+" : ""}${change.toFixed(2)}%`
            : `${change > 0 ? "+" : ""}${change.toFixed(2)}`;

        if (tendency === "up") {
            return (
                <div className="flex items-center text-green-500 gap-2">
                    <div className="flex items-center">
                        <TrendingUp size={16} className="mr-1" />
                        <span>{formattedChange}</span>
                    </div>
                    {percentageChange && (
                        <span className="text-xs text-gray-500">
                            {`(${percentageChange}%)`}
                        </span>
                    )}
                </div>
            );
        } else if (tendency === "down") {
            return (
                <div className="flex items-center text-red-500 gap-2">
                    <div className="flex items-center">
                        <TrendingDown size={16} className="mr-1" />
                        <span>{formattedChange}</span>
                    </div>
                    {percentageChange && (
                        <span className="text-xs text-gray-500">
                            {`(${percentageChange}%)`}
                        </span>
                    )}
                </div>
            );
        }

        return <span className="text-gray-500">Sin cambio</span>;
    };

    return (
        <Card
            title={
                <div className="flex items-center gap-2">
                    {icon}
                    <span>{title}</span>
                </div>
            }
            className="shadow-md"
        >
            <div className="text-lg font-medium mb-4">
                {data?.period || "Sin datos disponibles"}
            </div>

            <Row gutter={[16, 16]}>
                <Col span={8}>
                    <Statistic
                        title="Ventas"
                        value={data?.sales.current || "0.00"}
                        precision={0}
                        valueStyle={{ color: "#3f8600" }}
                        prefix="S/. "
                        formatter={(value) => `${Number(value).toLocaleString()}`}
                    />
                    <div className="mt-2">
                        {data &&
                            renderTrend({
                                change: data.sales.current - data.sales.previous,
                                tendency: data.sales.tendency,
                                percentageChange: data.sales.percentageChange,
                            })}
                    </div>
                </Col>

                <Col span={8}>
                    <Statistic
                        title="Órdenes"
                        value={data?.orders.current || "0.00"}
                        valueStyle={{ color: "#1890ff" }}
                    />
                    <div className="mt-2">
                        {data &&
                            renderTrend({
                                change: data.orders.current - data.orders.previous,
                                tendency: data.orders.tendency,
                                percentageChange: data.orders.percentageChange,
                            })}
                    </div>
                </Col>

                <Col span={8}>
                    <Statistic
                        title="Conversión"
                        value={data?.conversion.current || "0.00"}
                        suffix="%"
                        precision={1}
                        valueStyle={{ color: "#722ed1" }}
                    />
                    <div className="mt-2">
                        {data &&
                            renderTrend({
                                change: data.conversion.percentageChange,
                                tendency: data.conversion.tendency,
                                isPercentage: true,
                            })}
                    </div>
                </Col>
            </Row>

            <Divider orientation="left">Progreso de ventas</Divider>

            <div className="mb-4">
                <div className="flex justify-between mb-1">
                    <span className="text-sm text-gray-500">Meta mensual</span>
                    <span className="text-sm font-medium">
                        S/ {data?.salesProgress.target.toLocaleString()}
                    </span>
                </div>
                <Progress
                    percent={
                        data
                            ? Math.min(
                                  100,
                                  (data.salesProgress.totalRevenue /
                                      data.salesProgress.target) *
                                      100,
                              )
                            : 0
                    }
                    size="small"
                    strokeColor={{
                        "0%": "#108ee9",
                        "100%": "#87d068",
                    }}
                    showInfo={false}
                />
                {data && (
                    <div className="text-xs flex justify-between items-center mt-1 text-gray-500">
                        <span>
                            S/ {data.salesProgress.totalRevenue.toLocaleString()}
                        </span>
                        <span>
                            {(
                                (data.salesProgress.totalRevenue /
                                    data.salesProgress.target) *
                                100
                            ).toFixed(2)}
                            % completado
                        </span>
                    </div>
                )}
            </div>
        </Card>
    );
}
