import { PaginatedResponse } from "@myTypes/base";
import {
    CreateEducationalInstitutionValues,
    EducationalInstitution,
} from "@/features/crm/types/educational-institution";
import { portalsApi } from "@services/portals";

const DEFAULT_PAGE = 1;
const DEFAULT_PAGE_SIZE = 150;

export const getEducationalInstitutions = async (): Promise<
    PaginatedResponse<EducationalInstitution>
> => {
    const response = await portalsApi.get("crm/educational-institutions", {
        params: {
            page: DEFAULT_PAGE,
            pageSize: DEFAULT_PAGE_SIZE,
        },
    });
    return response.data;
};

export const createEducationalInstitution = async (
    data: CreateEducationalInstitutionValues,
): Promise<EducationalInstitution> => {
    const response = await portalsApi.post("crm/educational-institutions", data);
    return response.data;
};