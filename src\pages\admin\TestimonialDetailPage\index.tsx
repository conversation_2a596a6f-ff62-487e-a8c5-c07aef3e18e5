import { useEffect } from "react";
import { Link, useNavigate, useParams } from "react-router-dom";
import { <PERSON><PERSON><PERSON>rumb, Button, Image, Tag, Typography } from "antd";

const { Text } = Typography;

import AdminLayout from "@layouts/admin/AdminLayout";
import Edit from "@assets/icons/general/edit-stroke.svg?react";
import Trash from "@assets/icons/huge/trash-white.svg?react";
import { useMutation, useQuery } from "@tanstack/react-query";
import {
    deleteTestimonial,
    retrieveTestimonial,
} from "@services/portals/cms/testimonial";
import Spinner from "@components/shared/atoms/Spinner";
import { AxiosError } from "axios";
import { TestimonialStatusEnum } from "@myTypes/testimonial";

export default function TestimonialDetailPage() {
    const navigate = useNavigate();
    const { tid } = useParams<{ tid: string }>();

    const {
        isLoading,
        isError,
        data,
        error: _error,
    } = useQuery({
        queryKey: ["testimonial", tid],
        queryFn: () => retrieveTestimonial(tid),
        enabled: tid !== undefined,
    });

    const mutation = useMutation({
        mutationFn: () => deleteTestimonial(tid as string),
    });

    useEffect(() => {
        if (isError) {
            const error = _error as AxiosError;
            if (error.response?.status === 404) {
                navigate("/admin/testimonial");
            }
        }
    }, [isError, _error, navigate]);

    const handleOnEdit = () => {
        navigate(`/admin/testimonial/${tid}/edit`);
    };

    const handleOnDelete = () => {
        mutation.mutate();
        navigate("/admin/testimonial");
    };

    return (
        <>
            <AdminLayout>
                <div className="max-w-7xl w-full h-full space-y-5">
                    <div className="flex justify-between items-center">
                        <div>
                            <h1 className="text-2xl text-black-full">
                                Bienvenido,{" "}
                                <span className="text-blue-full font-semibold">
                                    Gerardo
                                </span>
                            </h1>
                            <Text className="text-sm text-gray-500 font-medium">
                                Gestiona aquí los Testimonios para la Website
                            </Text>
                        </div>
                        <div className="flex gap-3">
                            <Button
                                type="primary"
                                size="large"
                                style={{ fontSize: 16 }}
                                icon={<Edit />}
                                disabled={isError}
                                onClick={handleOnEdit}
                            >
                                Editar
                            </Button>
                            <Button
                                type="primary"
                                size="large"
                                style={{ fontSize: 16 }}
                                icon={<Trash />}
                                danger
                                disabled={isError}
                                onClick={handleOnDelete}
                            >
                                Eliminar
                            </Button>
                        </div>
                    </div>
                    {isLoading ? (
                        <Spinner />
                    ) : (
                        <>
                            <div className="p-5 bg-white-full rounded-lg space-y-5">
                                <div className="flex items-center">
                                    <Breadcrumb
                                        separator=">"
                                        items={[
                                            {
                                                title: (
                                                    <Link
                                                        to="/admin/testimonial"
                                                        className="text-base"
                                                    >
                                                        Testimonios
                                                    </Link>
                                                ),
                                            },
                                            {
                                                title: (
                                                    <Link
                                                        to={`/admin/testimonial/${tid}`}
                                                        className="text-base text-blue-medium"
                                                    >
                                                        {data?.authorName !== undefined
                                                            ? data.authorName
                                                            : "Testimonio"}
                                                    </Link>
                                                ),
                                            },
                                            {
                                                title: (
                                                    <Text className="text-base">
                                                        Detalle
                                                    </Text>
                                                ),
                                            },
                                        ]}
                                    />
                                </div>
                            </div>
                            <div className="p-5 bg-white-full rounded-lg space-y-5 lg:flex lg:justify-between">
                                <div className="space-y-2">
                                    <p className="text-gray-400 font-semibold text-sm">
                                        INFORMACIÓN PERSONAL
                                    </p>
                                    <div className="space-y-4">
                                        <div>
                                            <p className="text-sm text-black-medium font-semibold">
                                                Nombres y Apellidos
                                            </p>
                                            <p className="text-black-full">
                                                {data?.authorName}
                                            </p>
                                        </div>
                                        <div>
                                            <p className="text-sm text-black-medium font-semibold">
                                                Título o Rol
                                            </p>
                                            <p className="text-black-full">
                                                {data?.authorTitle}
                                            </p>
                                        </div>
                                        <div className="max-w-96">
                                            <p className="text-sm text-black-medium font-semibold">
                                                Cita
                                            </p>
                                            <p className="text-black-full">
                                                {data?.content}
                                            </p>
                                        </div>
                                        <div>
                                            <p className="text-sm text-black-medium font-semibold">
                                                Estado
                                            </p>
                                            <Tag
                                                className="rounded-full px-3"
                                                bordered={false}
                                                color={
                                                    data?.status ===
                                                    TestimonialStatusEnum.PUBLISHED
                                                        ? "green"
                                                        : "red"
                                                }
                                            >
                                                {data?.status ===
                                                TestimonialStatusEnum.PUBLISHED
                                                    ? "Publicado"
                                                    : "Borrador"}
                                            </Tag>
                                        </div>
                                        <div className="max-w-96">
                                            <p className="text-sm text-black-medium font-semibold">
                                                Orden
                                            </p>
                                            <p className="text-black-full">
                                                {data?.order}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div className="space-y-2 lg:text-end lg:pl-4 lg:border-l-2 border-gray-100">
                                    <p className="text-gray-400 font-semibold text-sm">
                                        CONTENIDO MULTIMEDIA
                                    </p>
                                    <div>
                                        <p className="text-sm text-black-medium font-semibold">
                                            Foto de Perfil
                                        </p>
                                        <Image
                                            src={`${data?.authorPhoto?.url}`}
                                            alt={data?.authorName}
                                            width={120}
                                            height={"auto"}
                                        />
                                    </div>
                                </div>
                            </div>
                        </>
                    )}
                </div>
            </AdminLayout>
        </>
    );
}
