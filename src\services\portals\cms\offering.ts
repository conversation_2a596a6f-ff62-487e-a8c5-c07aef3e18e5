import {
    CreateOfferingBody,
    Offering,
    PartialUpdateOfferingBody,
} from "@myTypes/offering";
import { portalsApi } from "@services/portals";

export type ListOfferingQuery = {
    page?: number;
    pageSize?: number;
    search?: string;
    sortBy?: string;
    order?: "asc" | "desc";
};

export const listOffering = async (params: ListOfferingQuery) => {
    const response = await portalsApi.get("cms/offerings", {
        params,
    });
    return response.data;
};

export const createOffering = async (data: CreateOfferingBody) => {
    const response = await portalsApi.post("cms/offerings", data);
    return response.data;
};

export const retrieveOffering = async (oid: string): Promise<Offering> => {
    const response = await portalsApi.get(`cms/offerings/${oid}`);
    return response.data;
};

export const deleteOffering = async (oid: string) => {
    const response = await portalsApi.delete(`cms/offerings/${oid}`);
    return response.data;
};

export const updateOffering = async (oid: string, data: PartialUpdateOfferingBody) => {
    const formData = new FormData();
    const { thumbnailFile, objectives, modules, instructors, ...payload } = data;

    Object.entries(payload).forEach(([key, value]) => {
        if (value) {
            formData.append(key, value as string);
        }
    });

    if (thumbnailFile) {
        formData.append("thumbnailFile", thumbnailFile[0] as unknown as Blob);
    }

    if (objectives) {
        formData.append("objectives", JSON.stringify(objectives));
    }

    if (modules) {
        formData.append("modules", JSON.stringify(modules));
    }

    if (instructors) {
        formData.append("instructors", JSON.stringify(instructors));
    }

    const response = await portalsApi.patch(`cms/offerings/${oid}`, formData, {
        headers: {
            "Content-Type": "multipart/form-data",
        },
    });
    return response.data;
};
