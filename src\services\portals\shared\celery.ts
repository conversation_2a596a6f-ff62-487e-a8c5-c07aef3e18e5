import { CheckTaskStatusResponse } from "@/core/types/celery";
import { portalsApi } from "..";

type CheckTaskStatusProgressDetails = {
    message: string;
};

type CheckTaskStatusSuccessDetails = {
    message: string;
    credential: string;
    enrollment: string;
};

export const checkTaskStatus = async (
    taskId: string,
): Promise<
    CheckTaskStatusResponse<
        CheckTaskStatusProgressDetails | CheckTaskStatusSuccessDetails
    >
> => {
    const res = await portalsApi.get(`/shared/celery/task-status/${taskId}`);
    return res.data;
};
