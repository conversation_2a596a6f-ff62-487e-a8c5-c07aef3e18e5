import { App, <PERSON><PERSON>, Di<PERSON>r, Form, Input, Modal, Select, SelectProps } from "antd";
import { usePartnerships } from "../../hooks/use-partnership";
import { useState } from "react";
import SelectEducationalInstitution from "./select-educational-institution";
import TextArea from "antd/es/input/TextArea";
import { useMutation } from "@tanstack/react-query";
import { PartnershipCreateForm } from "../../types/partnership";
import { createPartnership } from "../../services/portals/partnership";
import { Plus } from "lucide-react";
import queryClient from "@lib/queryClient";

interface SelectPartnershipProps extends Omit<SelectProps, "options"> {
    value?: string[];
    onChange?: (value: string[]) => void;
}
export default function SelectPartnership({
    value,
    onChange,
    ...restProps
}: SelectPartnershipProps) {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [form] = Form.useForm();

    const { message } = App.useApp();
    const { partnerships } = usePartnerships();
    const { mutate: createPartnershipMutate, isPending } = useMutation({
        mutationFn: (newPartnership: PartnershipCreateForm) =>
            createPartnership(newPartnership),
        onSuccess: () => {
            message.success("Alianza creada correctamente");
            setIsModalOpen(false);
            form.resetFields();
            queryClient.invalidateQueries({ queryKey: ["partnerships"] });
        },
        onError: () => {
            message.error("Error al crear la alianza");
        },
    });

    const partnershipOptions: SelectProps["options"] = partnerships?.map(
        (partnership) => ({
            value: partnership.pid,
            label: partnership.name,
            title: `${partnership.name} #${partnership.pid?.slice(-6)}`,
        }),
    );

    const handleChange = (selectedValue: string[]) => {
        if (onChange) {
            onChange(selectedValue);
        }
    };

    const handleCreatePartnership = async (values: PartnershipCreateForm) => {
        createPartnershipMutate(values);
    };

    return (
        <>
            <Select
                {...restProps}
                value={value}
                mode="multiple"
                optionFilterProp="label"
                allowClear
                showSearch
                placeholder="Buscar y seleccionar alianzas..."
                optionRender={(option) => (
                    <div className="flex justify-between items-center">
                        <span>{option.label}</span>
                        <span className="text-xs text-gray-600">
                            #{option.value?.toString().slice(-6)}
                        </span>
                    </div>
                )}
                filterOption={(input, option) => {
                    const label = option?.label?.toString().toLowerCase() || "";
                    const title = option?.title?.toString().toLowerCase() || "";
                    const searchTerm = input.toLowerCase();
                    return label.includes(searchTerm) || title.includes(searchTerm);
                }}
                onChange={handleChange}
                options={partnershipOptions}
                dropdownRender={(menu) => (
                    <>
                        {menu}
                        <Divider className="my-1" />
                        <div className="flex justify-between items-center px-2">
                            <p className="text-sm text-gray-700 font-medium">
                                ¿No encuentras la alianza?
                            </p>
                            <Button
                                size="small"
                                type="primary"
                                icon={<Plus size={12} />}
                                onClick={() => setIsModalOpen(true)}
                            >
                                Agregar
                            </Button>
                        </div>
                    </>
                )}
            />
            <Modal
                title="Crear nueva alianza"
                open={isModalOpen}
                onCancel={() => {
                    setIsModalOpen(false);
                    form.resetFields();
                }}
                footer={null}
                confirmLoading={false}
            >
                <Form form={form} onFinish={handleCreatePartnership} layout="vertical">
                    <Form.Item<PartnershipCreateForm>
                        name="name"
                        label="Nombre de la alianza"
                        rules={[
                            {
                                required: true,
                                message: "Por favor ingrese el nombre de la alianza",
                            },
                        ]}
                    >
                        <Input placeholder="Ingrese el nombre de la alianza" />
                    </Form.Item>
                    <Form.Item<PartnershipCreateForm>
                        name="institution"
                        label="Institución Educativa"
                        rules={[
                            {
                                required: true,
                                message:
                                    "Por favor seleccione una institución educativa",
                            },
                        ]}
                    >
                        <SelectEducationalInstitution />
                    </Form.Item>
                    <Form.Item<PartnershipCreateForm>
                        name="description"
                        label="Descripción"
                    >
                        <TextArea placeholder="Ingrese la descripción de la alianza" />
                    </Form.Item>
                    <Form.Item className="mb-0 text-right">
                        <Button
                            type="default"
                            onClick={() => {
                                setIsModalOpen(false);
                                form.resetFields();
                            }}
                            className="mr-2"
                            disabled={isPending}
                        >
                            Cancelar
                        </Button>
                        <Button type="primary" htmlType="submit" loading={isPending}>
                            Guardar
                        </Button>
                    </Form.Item>
                </Form>
            </Modal>
        </>
    );
}
