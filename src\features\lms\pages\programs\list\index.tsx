import { useMemo } from "react";
import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    ConfigProvider,
    Dropdown,
    Empty,
    Input,
    Pagination,
    Radio,
    Table,
    Tag,
    Typography,
    Tooltip,
} from "antd";
import type { TableProps } from "antd";
import { useSearchParams, useNavigate, Link } from "react-router-dom";
import {
    Grid2x2,
    Hash,
    MoreVertical,
    Plus,
    Rows3,
    Users,
    BookOpen,
    Calendar,
} from "lucide-react";
import dayjs from "dayjs";

import LmsLayout from "@/features/lms/layout";
import WelcomeBar from "@components/shared/molecules/WelcomeBar";
import Spinner from "@components/shared/atoms/Spinner";
import { usePrograms } from "@/features/lms/hooks/use-program";
import { Program, ProgramQueryParams } from "@/features/lms/types/program";
import {
    OfferingStage,
    OfferingStageLabel,
    OfferingStageColor,
    OfferingTypeLabel,
    OfferingTypeColor,
    OfferingType,
} from "@myTypes/offering";

import DeleteStroke from "@assets/icons/huge/delete-stroke.svg?react";
import EditStroke from "@assets/icons/huge/edit-stroke.svg?react";

const { Search } = Input;
const { Text } = Typography;

const STATUS_FILTER_TAGS = [
    {
        value: "all",
        label: "Todos",
        icon: "Users",
    },
    {
        value: "enrollment",
        label: "Matrículas Abiertas",
        icon: "BookOpen",
        stage: OfferingStage.ENROLLMENT,
    },
    {
        value: "enrollment_closed",
        label: "Matrículas Cerradas",
        icon: "Calendar",
        stage: OfferingStage.ENROLLMENT_CLOSED,
    },
];

enum DisplayMode {
    LIST = "list",
    CARDS = "cards",
}

const PAGE_SIZE = 10;
const DEFAULT_PAGE = 1;

const INITIAL_COLUMNS: TableProps<Program>["columns"] = [
    {
        title: "ID",
        dataIndex: "oid",
        key: "oid",
        width: 80,
        render: (oid: string) => (
            <Tooltip title="Ver detalles del programa">
                <Link
                    to={`/lms/programs/${oid}`}
                    className="font-semibold text-blue-full flex items-center gap-1"
                >
                    <Hash size={14} strokeWidth={2.5} className="text-gray-500" />
                    {oid.slice(-6)}
                </Link>
            </Tooltip>
        ),
    },
    {
        title: "PROGRAMA",
        dataIndex: "name",
        key: "name",
        render: (_: string, record: Program) => (
            <Link
                to={`/lms/programs/${record.oid}`}
                className="text-blue-full font-medium underline"
            >
                <div>
                    <div className="font-semibold">
                        {record.longName || record.name}
                    </div>
                    {record.codeName && (
                        <div className="text-xs text-gray-500">({record.codeName})</div>
                    )}
                </div>
            </Link>
        ),
    },
    {
        title: "FECHAS",
        key: "dates",
        render: (_: string, record: Program) => {
            const startDate = dayjs(record.startDate);
            const endDate = dayjs(record.endDate);
            const dateRange = `${startDate.format("DD MMM")} - ${endDate.format("DD MMM YYYY")}`;

            return (
                <div>
                    <div className="text-sm">{dateRange}</div>
                    {record.schedule && (
                        <div className="text-xs text-gray-500">{record.schedule}</div>
                    )}
                </div>
            );
        },
    },
    {
        title: "TIPO",
        dataIndex: "type",
        key: "type",
        render: (type: OfferingType) => (
            <Tag
                bordered={false}
                color={OfferingTypeColor[type]}
                className="rounded-full px-3"
            >
                {OfferingTypeLabel[type]}
            </Tag>
        ),
    },
    {
        title: "ETAPA",
        dataIndex: "stage",
        key: "stage",
        render: (stage: OfferingStage) => (
            <Tag
                bordered={false}
                color={OfferingStageColor[stage]}
                className="rounded-full px-3"
            >
                {OfferingStageLabel[stage]}
            </Tag>
        ),
    },
    {
        title: "DURACIÓN",
        dataIndex: "duration",
        key: "duration",
        render: (duration: string) => <Text className="text-sm">{duration}</Text>,
    },
];

export default function ProgramsListPage() {
    const [searchParams, setSearchParams] = useSearchParams();
    const navigate = useNavigate();

    const mainFilterTag = searchParams.get("mainFilterTag") || "all";
    const display = searchParams.get("display") || DisplayMode.LIST;
    const page = searchParams.get("page")
        ? Number(searchParams.get("page"))
        : DEFAULT_PAGE;
    const pageSize = searchParams.get("pageSize")
        ? Number(searchParams.get("pageSize"))
        : PAGE_SIZE;
    const search = searchParams.get("search");

    const queryParams: ProgramQueryParams = useMemo(
        () => ({
            page: Number(page),
            limit: Number(pageSize),
            ...(search ? { search } : {}),
            ...(mainFilterTag !== "all"
                ? {
                      stage: STATUS_FILTER_TAGS.find(
                          (tag) => tag.value === mainFilterTag,
                      )?.stage,
                  }
                : {}),
        }),
        [page, pageSize, mainFilterTag, search],
    );

    const { programs, isLoading, count: TOTAL_PROGRAMS } = usePrograms({ queryParams });

    const onSearch = (value: string) => {
        setSearchParams((prev) => {
            if (value.trim()) {
                prev.set("search", value.trim());
            } else {
                prev.delete("search");
            }
            prev.set("page", "1");
            return prev;
        });
    };

    const handleSetDisplayMode = (value: DisplayMode) => {
        setSearchParams((prev) => {
            prev.set("display", value);
            return prev;
        });
    };

    const handleSetPage = (value: number, pageSize: number) => {
        setSearchParams((prev) => {
            prev.set("page", value.toString());
            prev.set("pageSize", pageSize.toString());
            return prev;
        });
    };

    const handleSetMainFilterTag = (value: string) => {
        setSearchParams((prev) => {
            prev.set("mainFilterTag", value);
            prev.set("page", "1");
            return prev;
        });
    };

    const handleRowAction = (key: string, record: Program) => {
        if (key === "edit") {
            navigate(`/lms/programs/${record.oid}`);
        }
        if (key === "delete") {
            // TODO: Implement delete functionality
            console.log("Delete program:", record.oid);
        }
    };

    const actionColumns: TableProps<Program>["columns"] = [
        {
            title: "ACCIONES",
            key: "actions",
            render: (record: Program) => (
                <Dropdown
                    trigger={["click"]}
                    menu={{
                        items: [
                            {
                                key: "edit",
                                label: (
                                    <div className="flex items-center gap-2 text-blue-full">
                                        <EditStroke className="w-5 h-5" /> Editar
                                    </div>
                                ),
                            },
                            {
                                key: "delete",
                                label: (
                                    <div className="flex items-center gap-2 text-state-red-full">
                                        <DeleteStroke className="w-5 h-5" /> Eliminar
                                    </div>
                                ),
                            },
                        ],
                        onClick: ({ key }) => {
                            handleRowAction(key, record);
                        },
                    }}
                    placement="bottomRight"
                >
                    <Button
                        icon={<MoreVertical className="w-5 h-5" />}
                        type="text"
                        size="small"
                    />
                </Dropdown>
            ),
        },
    ];

    const ProgramCard = ({ program }: { program: Program }) => {
        const startDate = dayjs(program.startDate);
        const endDate = dayjs(program.endDate);
        const dateRange = `${startDate.format("DD [de] MMMM")} - ${endDate.format("DD [de] MMMM")}`;

        return (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
                {/* Thumbnail */}
                <div className="h-32 bg-gradient-to-r from-blue-500 to-blue-full relative">
                    {program.thumbnail?.url ? (
                        <img
                            src={program.thumbnail.url}
                            alt={program.name}
                            className="w-full h-full object-cover"
                        />
                    ) : (
                        <div className="w-full h-full flex items-center justify-center">
                            <BookOpen
                                size={32}
                                className="text-white-full opacity-80"
                            />
                        </div>
                    )}
                    <div className="absolute top-2 right-2">
                        <Tag
                            bordered={false}
                            color={OfferingTypeColor[program.type]}
                            className="rounded-full px-2 py-1 text-xs"
                        >
                            {OfferingTypeLabel[program.type]}
                        </Tag>
                    </div>
                </div>

                {/* Content */}
                <div className="p-4 space-y-3">
                    <div>
                        <Link
                            to={`/lms/programs/${program.oid}`}
                            className="text-lg font-semibold text-gray-900 hover:text-blue-600 line-clamp-2"
                        >
                            {program.longName || program.name}
                        </Link>
                        {program.codeName && (
                            <div className="text-sm text-gray-500">
                                ({program.codeName})
                            </div>
                        )}
                    </div>

                    <div className="space-y-2 text-sm text-gray-600">
                        <div className="flex items-center gap-2">
                            <Calendar size={14} />
                            <span>{dateRange}</span>
                        </div>
                        {program.schedule && (
                            <div className="flex items-center gap-2">
                                <span className="w-3.5 h-3.5 rounded-full bg-gray-400 flex items-center justify-center">
                                    <span className="w-1.5 h-1.5 bg-white rounded-full"></span>
                                </span>
                                <span>{program.schedule}</span>
                            </div>
                        )}
                    </div>

                    <div className="flex justify-between items-center pt-2 border-t border-gray-100">
                        <Tag
                            bordered={false}
                            color={OfferingStageColor[program.stage]}
                            className="rounded-full px-3"
                        >
                            {OfferingStageLabel[program.stage]}
                        </Tag>
                        <Dropdown
                            trigger={["click"]}
                            menu={{
                                items: [
                                    {
                                        key: "edit",
                                        label: (
                                            <div className="flex items-center gap-2 text-blue-full">
                                                <EditStroke className="w-4 h-4" />{" "}
                                                Editar
                                            </div>
                                        ),
                                    },
                                    {
                                        key: "delete",
                                        label: (
                                            <div className="flex items-center gap-2 text-state-red-full">
                                                <DeleteStroke className="w-4 h-4" />{" "}
                                                Eliminar
                                            </div>
                                        ),
                                    },
                                ],
                                onClick: ({ key }) => {
                                    handleRowAction(key, program);
                                },
                            }}
                            placement="bottomRight"
                        >
                            <Button
                                icon={<MoreVertical className="w-4 h-4" />}
                                type="text"
                                size="small"
                                className="opacity-60 hover:opacity-100"
                            />
                        </Dropdown>
                    </div>
                </div>
            </div>
        );
    };

    return (
        <LmsLayout>
            <div className="w-full h-full space-y-5">
                <div className="flex justify-between items-center">
                    <WelcomeBar helperText="Gestiona aquí los programas del sistema de aprendizaje" />
                    <div className="flex gap-3">
                        <Button
                            type="primary"
                            size="large"
                            style={{ fontSize: 16 }}
                            icon={<Plus />}
                            onClick={() => {
                                navigate("/lms/programs/create");
                            }}
                        >
                            Agregar
                        </Button>
                    </div>
                </div>

                <div className="p-5 bg-white-full rounded-lg space-y-5">
                    <div className="flex flex-col lg:flex-row justify-between items-center gap-4">
                        <Text className="text-black-medium text-2xl font-semibold">
                            Programas{" "}
                            <Badge count={TOTAL_PROGRAMS} color="blue" size="default" />
                        </Text>

                        <div className="flex items-center gap-3">
                            <Search
                                size="large"
                                placeholder="Buscar por nombre o código"
                                onSearch={onSearch}
                                enterButton
                                allowClear
                                className="max-w-screen-sm"
                                defaultValue={search || ""}
                            />

                            <Radio.Group
                                value={display}
                                onChange={(e) => handleSetDisplayMode(e.target.value)}
                                style={{ display: "flex" }}
                            >
                                <Radio.Button
                                    value={DisplayMode.LIST}
                                    style={{
                                        display: "flex",
                                        alignItems: "center",
                                        justifyContent: "center",
                                        marginRight: "-1px",
                                    }}
                                >
                                    <Tooltip title="Ver como tabla">
                                        <Rows3 size={16} />
                                    </Tooltip>
                                </Radio.Button>
                                <Radio.Button
                                    value={DisplayMode.CARDS}
                                    style={{
                                        display: "flex",
                                        alignItems: "center",
                                        justifyContent: "center",
                                    }}
                                >
                                    <Tooltip title="Ver como tarjetas">
                                        <Grid2x2 size={16} />
                                    </Tooltip>
                                </Radio.Button>
                            </Radio.Group>
                        </div>
                    </div>

                    <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg shadow-sm">
                        {STATUS_FILTER_TAGS.map((tag) => {
                            let TagIcon;
                            switch (tag.icon) {
                                case "Users":
                                    TagIcon = Users;
                                    break;
                                case "BookOpen":
                                    TagIcon = BookOpen;
                                    break;
                                case "Calendar":
                                    TagIcon = Calendar;
                                    break;
                                default:
                                    TagIcon = Users;
                            }

                            return (
                                <button
                                    key={tag.value}
                                    onClick={() => handleSetMainFilterTag(tag.value)}
                                    className={`px-4 py-2 text-sm font-medium rounded-full transition-all duration-200 flex items-center gap-1.5 ${
                                        mainFilterTag === tag.value
                                            ? "bg-blue-500 text-white-full shadow-md"
                                            : "bg-white text-gray-600 hover:bg-gray-100"
                                    }`}
                                >
                                    <TagIcon size={14} />
                                    {tag.label}
                                </button>
                            );
                        })}
                    </div>

                    {display === DisplayMode.LIST ? (
                        <ConfigProvider
                            theme={{
                                components: {
                                    Table: {
                                        headerBg: "#FBFCFD",
                                        borderColor: "#fff",
                                        headerSplitColor: "#fafafa",
                                        headerBorderRadius: 8,
                                        rowHoverBg: "#F6FAFD",
                                        rowSelectedBg: "#F6FAFD",
                                        rowSelectedHoverBg: "#F6FAFD",
                                        footerBg: "#F1F1F1",
                                    },
                                },
                            }}
                        >
                            <Table
                                className="rounded-lg shadow-sm"
                                footer={() => ""}
                                pagination={false}
                                columns={
                                    INITIAL_COLUMNS
                                        ? [...INITIAL_COLUMNS, ...actionColumns]
                                        : []
                                }
                                locale={{
                                    emptyText: (
                                        <>{isLoading ? <Spinner /> : <Empty />}</>
                                    ),
                                }}
                                dataSource={programs}
                                rowKey="oid"
                                scroll={{ x: "max-content" }}
                            />
                        </ConfigProvider>
                    ) : programs.length === 0 ? (
                        <div className="flex justify-center py-12">
                            {isLoading ? <Spinner /> : <Empty />}
                        </div>
                    ) : (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                            {programs.map((program) => (
                                <ProgramCard key={program.oid} program={program} />
                            ))}
                        </div>
                    )}

                    <div className="flex justify-between">
                        <div>
                            <Text className="text-sm text-gray-500">
                                Mostrando {programs.length} de {TOTAL_PROGRAMS}{" "}
                                programas
                            </Text>
                        </div>
                        <Pagination
                            showSizeChanger
                            total={TOTAL_PROGRAMS}
                            pageSize={pageSize}
                            current={page}
                            onChange={(page, pageSize) => {
                                handleSetPage(page, pageSize);
                            }}
                        />
                    </div>
                </div>
            </div>
        </LmsLayout>
    );
}
