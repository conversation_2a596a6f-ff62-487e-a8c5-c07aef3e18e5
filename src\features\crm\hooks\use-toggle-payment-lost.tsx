import { useMutation, useQueryClient } from "@tanstack/react-query";
import { App, Modal } from "antd";
import { updatePayment } from "../services/portals/payment";
import { XOctagon } from "lucide-react";
import { type AxiosError } from "axios";
import { useApiError } from "@hooks/use-api-error";

export const useTogglePaymentLost = (onSuccess?: (isLost: boolean) => void) => {
    const queryClient = useQueryClient();
    const { notification } = App.useApp();
    const { confirm } = Modal;

    const { handleError } = useApiError({
        title: "Error al actualizar el estado",
        genericMessage: "No se pudo actualizar el estado del pago",
    });

    const toggleMutation = useMutation({
        mutationFn: ({ pid, isLost }: { pid: string; isLost: boolean }) =>
            updatePayment(pid, { isLost }),
        onSuccess: (_, variables) => {
            notification.success({
                message: variables.isLost
                    ? "Pago marcado como perdido"
                    : "Pago marcado como activo",
                description: "El estado del pago se ha actualizado correctamente",
                duration: 3,
            });
            // Invalidate both the specific payment and the payments list
            queryClient.invalidateQueries({ queryKey: ["payments"] });
            queryClient.invalidateQueries({ queryKey: ["payment"] });

            // Call the optional callback to update form state
            if (onSuccess) {
                onSuccess(variables.isLost);
            }
        },
        onError: (error: AxiosError) => {
            handleError(error);
        },
    });

    const confirmToggle = (pid: string, currentStatus: boolean) => {
        const newStatus = !currentStatus;

        confirm({
            title: newStatus
                ? "¿Marcar pago como perdido?"
                : "¿Marcar pago como activo?",
            icon: <XOctagon className={newStatus ? "text-red-500" : "text-blue-500"} />,
            content: newStatus
                ? "Este pago será marcado como perdido y no se considerará para reportes de ingresos."
                : "Este pago será marcado como activo y se considerará nuevamente para reportes.",
            okText: newStatus ? "Sí, marcar como perdido" : "Sí, marcar como activo",
            okType: newStatus ? "danger" : "primary",
            cancelText: "Cancelar",
            onOk() {
                toggleMutation.mutate({ pid, isLost: newStatus });
            },
        });
    };

    return {
        togglePaymentLost: confirmToggle,
        isLoading: toggleMutation.isPending,
    };
};
