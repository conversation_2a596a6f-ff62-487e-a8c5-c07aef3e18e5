import { PaginatedResponse } from "@myTypes/base";
import type {
    ListTemplateTypesQueryParams,
    TemplateType,
} from "@/features/crm/types/template-type";
import { portalsApi } from "@services/portals";

const DEFAULT_PAGE_SIZE = 10;
const DEFAULT_PAGE = 1;

export const listTemplateType = async (
    params: ListTemplateTypesQueryParams = {
        page: DEFAULT_PAGE,
        pageSize: DEFAULT_PAGE_SIZE,
    },
): Promise<PaginatedResponse<TemplateType>> => {
    const response = await portalsApi.get("crm/template-types", {
        params,
    });
    return response.data;
};

export const retrieveTemplateType = async (ttid: string): Promise<TemplateType> => {
    const response = await portalsApi.get(`crm/templates-types/${ttid}`);
    return response.data;
};
