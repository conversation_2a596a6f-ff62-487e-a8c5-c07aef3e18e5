import React from "react";
import { Card, CardProps, Empty } from "antd";
import {
    LineChart,
    Line,
    XAxis,
    <PERSON>A<PERSON>s,
    CartesianGrid,
    <PERSON><PERSON><PERSON>,
    ResponsiveContainer,
    Legend,
} from "recharts";
import clsx from "clsx";

type LegendItem = {
    name: string;
    color: string;
};

interface LineChartCardProps extends CardProps {
    data: Array<Record<string, unknown>>;
    xAxisDataKey?: string;
    lineDataKeys?: string[]; // Para múltiples líneas
    lineColors?: string[]; // Colores para cada línea
    icon?: React.ReactNode;
    formatter?: (value: number) => string;
    legend?: LegendItem[];
    height?: number;
    showGrid?: boolean;
    showLegend?: boolean;
    strokeWidth?: number;
    connectNulls?: boolean;
    // Props para compatibilidad con uso simple (una sola línea)
    dataKey?: string;
    lineColor?: string;
}

const LineChartCard: React.FC<LineChartCardProps> = ({
    title,
    data,
    xAxisDataKey = "name",
    lineDataKeys = [],
    lineColors = ["#4096ff", "#36cfc9", "#faad14", "#73d13d", "#ff4d4f"],
    icon,
    formatter,
    legend,
    height = 300,
    showGrid = true,
    showLegend = true,
    strokeWidth = 2,
    connectNulls = false,
    dataKey,
    lineColor = "#4096ff",
    className,
    ...cardProps
}) => {
    // Si se proporciona dataKey, usar modo simple (una sola línea)
    const finalLineDataKeys = dataKey ? [dataKey] : lineDataKeys;
    const finalLineColors = dataKey ? [lineColor] : lineColors;

    interface CustomTooltipProps {
        active?: boolean;
        payload?: Array<{
            value: number;
            dataKey: string;
            name: string;
            color: string;
        }>;
        label?: string;
    }

    const CustomTooltip = ({ active, payload, label }: CustomTooltipProps) => {
        if (active && payload && payload.length) {
            return (
                <div className="bg-white-full p-3 border border-gray-200 shadow-md rounded">
                    <p className="font-medium mb-2">{`${label}`}</p>
                    {payload.map((entry, index) => {
                        // Buscar el índice real en lineDataKeys
                        const keyIndex = finalLineDataKeys.findIndex(
                            (k) => k === entry.dataKey,
                        );
                        const legendItem = legend && legend[keyIndex];
                        return (
                            <p
                                key={index}
                                style={{ color: entry.color }}
                                className="mb-1"
                            >
                                {legendItem
                                    ? `${legendItem.name}: `
                                    : `${entry.name}: `}
                                <span className="font-medium">
                                    {formatter ? formatter(entry.value) : entry.value}
                                </span>
                            </p>
                        );
                    })}
                </div>
            );
        }
        return null;
    };

    return (
        <Card
            title={
                <div className="flex items-center gap-2">
                    {icon}
                    <span>{title}</span>
                </div>
            }
            className={clsx("shadow-md h-full", className)}
            {...cardProps}
        >
            <div style={{ height: `${height}px` }}>
                {data.length ? (
                    <ResponsiveContainer width="100%" height="100%">
                        <LineChart
                            data={data}
                            margin={{
                                top: 5,
                                right: 30,
                                left: 20,
                                bottom: 5,
                            }}
                        >
                            {showGrid && <CartesianGrid strokeDasharray="3 3" />}
                            <XAxis
                                dataKey={xAxisDataKey}
                                angle={-45}
                                textAnchor="end"
                                height={80}
                                interval={0}
                                tick={{ fontSize: 12 }}
                            />
                            <YAxis
                                tickFormatter={(value) =>
                                    formatter
                                        ? formatter(value).split(" ")[0] // Para formatters de moneda
                                        : value
                                }
                            />
                            <Tooltip content={<CustomTooltip />} />
                            {showLegend && legend && <Legend />}

                            {/* Renderizar líneas dinámicamente */}
                            {finalLineDataKeys.map((key, index) => (
                                <Line
                                    key={key}
                                    type="monotone"
                                    dataKey={key}
                                    stroke={
                                        finalLineColors[index % finalLineColors.length]
                                    }
                                    strokeWidth={strokeWidth}
                                    name={
                                        legend && legend[index]
                                            ? legend[index].name
                                            : key
                                    }
                                    connectNulls={connectNulls}
                                    dot={{ r: 4 }}
                                    activeDot={{ r: 6 }}
                                    animationDuration={1500}
                                />
                            ))}
                        </LineChart>
                    </ResponsiveContainer>
                ) : (
                    <div className="h-full flex items-center justify-center">
                        <Empty description="No hay datos disponibles" />
                    </div>
                )}
            </div>
        </Card>
    );
};

export default LineChartCard;
