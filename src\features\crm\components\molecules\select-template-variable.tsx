import {
    UseTemplateVariableQuery,
    useTemplateVariables,
} from "@/features/crm/hooks/use-template-variable";
import { useDebounce } from "@hooks/use-debounce";
import { Select, Tooltip, type SelectProps } from "antd";
import { useState } from "react";
import { TemplateVariable } from "../../types/template-variable";
import { DefaultOptionType } from "antd/es/select";
import { InfoIcon } from "lucide-react";

interface SelectTemplateVariableProps extends Omit<SelectProps, "options"> {
    value?: string | string[]; // Add value prop for controlled component
    onChange?: (
        value: string | string[],
        option: DefaultOptionType | DefaultOptionType[] | undefined,
    ) => void; // Add onChange prop
    selectedTemplateVariable?: TemplateVariable;
    templateType?: string;
}

export default function SelectTemplateVariables({
    value,
    onChange,
    selectedTemplateVariable,
    templateType,
    ...restProps
}: SelectTemplateVariableProps) {
    const [query, setQuery] = useState<UseTemplateVariableQuery | null>(null);

    const debouncedQuery = useDebounce(query, 1000);

    const { templateVariables, isLoading } = useTemplateVariables({
        pageSize: 25,
        query: {
            search: debouncedQuery?.search,
            templateType,
        },
    });

    const templateVariablesOptions: SelectProps["options"] = templateVariables?.map(
        (templateVariable) => ({
            value: templateVariable.tvid,
            label: templateVariable.name,
            info: templateVariable,
            enabled: !!templateType,
        }),
    );

    const options = [...(templateVariablesOptions || [])];

    // Agregar valores seleccionados que no están en las opciones actuales
    if (value && selectedTemplateVariable) {
        const valuesToCheck = Array.isArray(value) ? value : [value];

        valuesToCheck.forEach((selectedValue) => {
            const isValueInOptions = templateVariablesOptions?.some(
                (option) => option.value === selectedValue,
            );

            if (!isValueInOptions) {
                // Verificar que no hayamos agregado ya esta opción
                const alreadyAdded = options.some(
                    (option) => option.value === selectedValue,
                );

                if (!alreadyAdded) {
                    options.unshift({
                        value: selectedValue,
                        label: selectedTemplateVariable.name,
                        info: selectedTemplateVariable,
                    });
                }
            }
        });
    }

    const handleSearch = (value: string) => {
        setQuery({ search: value });
    };

    const handleChange = (
        val: string | string[],
        option: DefaultOptionType | DefaultOptionType[] | undefined,
    ) => {
        // Limpiar el query después de seleccionar
        setQuery(null);

        // Llamar al onChange original
        if (onChange) {
            onChange(val, option);
        }
    };

    return (
        <>
            <Select
                {...restProps}
                value={value}
                onChange={handleChange}
                onSearch={handleSearch}
                options={options}
                optionRender={({ label, data: { info } }) => {
                    const tooltipTitle = (
                        <div className="space-y-1">
                            <div>
                                <strong>Descripción:</strong>{" "}
                                {info.description || "Sin descripción"}
                            </div>
                            <div>
                                <strong>Ejemplo:</strong>{" "}
                                {info.example || "Sin ejemplo"}
                            </div>
                        </div>
                    );

                    return (
                        <div className="flex justify-between items-center">
                            <div className="text-wrap flex flex-col">
                                <span>{label}</span>
                            </div>
                            <Tooltip title={tooltipTitle} rootClassName="text-xs">
                                <InfoIcon size={14} />
                            </Tooltip>
                        </div>
                    );
                }}
                filterOption={false}
                loading={isLoading}
                showSearch
                allowClear
            />
        </>
    );
}
