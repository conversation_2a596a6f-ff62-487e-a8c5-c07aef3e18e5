import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { App } from "antd";
import {
    EventReminderMetricsQueryParams,
    ListEventRemindersQueryParams,
} from "@/features/crm/types/event-reminder";
import {
    bulkRetryEventReminders,
    deleteEventReminder,
    getEventReminderMetrics,
    listEventReminders,
    retryEmailInvitation,
    retryWhatsappInvitation,
    sendEmailInvitation,
    sendWhatsappInvitation,
} from "@/features/crm/services/portals/event-reminder";

type UseEventRemindersProps = {
    queryParams?: ListEventRemindersQueryParams;
    enabled?: boolean;
};

export const useEventReminders = ({
    queryParams,
    enabled = true,
}: UseEventRemindersProps) => {
    const { data, isLoading, isError, refetch } = useQuery({
        queryKey: ["event-reminders", queryParams],
        queryFn: () => listEventReminders(queryParams),
        enabled,
        refetchOnWindowFocus: false,
    });

    const {
        count,
        results: reminders,
        next,
        previous,
    } = data || {
        count: 0,
        results: [],
        next: null,
        previous: null,
    };

    return {
        isLoading,
        isError,
        reminders,
        count,
        next,
        previous,
        refetch,
    };
};

type UseEventReminderMetricsProps = {
    queryParams?: EventReminderMetricsQueryParams;
    enabled?: boolean;
};

export const useEventReminderMetrics = ({
    queryParams,
    enabled = true,
}: UseEventReminderMetricsProps) => {
    const { data, isLoading, isError, refetch } = useQuery({
        queryKey: ["event-reminder-metrics", queryParams],
        queryFn: () => getEventReminderMetrics(queryParams),
        enabled,
        refetchOnWindowFocus: false,
    });

    return {
        metrics: data,
        isLoading,
        isError,
        refetch,
    };
};

// Mutation hooks
export const useDeleteEventReminder = () => {
    const queryClient = useQueryClient();
    const { notification } = App.useApp();

    return useMutation({
        mutationFn: deleteEventReminder,
        onSuccess: () => {
            notification.success({
                message: "Éxito",
                description: "Recordatorio eliminado correctamente",
            });
            queryClient.invalidateQueries({ queryKey: ["event-reminders"] });
            queryClient.invalidateQueries({ queryKey: ["event-reminder-metrics"] });
        },
        onError: () => {
            notification.error({
                message: "Error",
                description: "Error al eliminar el recordatorio",
            });
        },
    });
};

export const useBulkRetryEventReminders = () => {
    const queryClient = useQueryClient();
    const { notification } = App.useApp();

    return useMutation({
        mutationFn: bulkRetryEventReminders,
        onSuccess: () => {
            notification.success({
                message: "Éxito",
                description: "Reintento masivo iniciado correctamente",
            });
            queryClient.invalidateQueries({ queryKey: ["event-reminders"] });
            queryClient.invalidateQueries({ queryKey: ["event-reminder-metrics"] });
        },
        onError: () => {
            notification.error({
                message: "Error",
                description: "Error al reintentar las invitaciones",
            });
        },
    });
};

export const useRetryEmailInvitation = () => {
    const queryClient = useQueryClient();
    const { notification } = App.useApp();

    return useMutation({
        mutationFn: retryEmailInvitation,
        onSuccess: () => {
            notification.success({
                message: "Éxito",
                description: "Reintento de email iniciado correctamente",
            });
            queryClient.invalidateQueries({ queryKey: ["event-reminders"] });
            queryClient.invalidateQueries({ queryKey: ["event-reminder-metrics"] });
        },
        onError: () => {
            notification.error({
                message: "Error",
                description: "Error al reintentar el envío por email",
            });
        },
    });
};

export const useRetryWhatsappInvitation = () => {
    const queryClient = useQueryClient();
    const { notification } = App.useApp();

    return useMutation({
        mutationFn: retryWhatsappInvitation,
        onSuccess: () => {
            notification.success({
                message: "Éxito",
                description: "Reintento de WhatsApp iniciado correctamente",
            });
            queryClient.invalidateQueries({ queryKey: ["event-reminders"] });
            queryClient.invalidateQueries({ queryKey: ["event-reminder-metrics"] });
        },
        onError: () => {
            notification.error({
                message: "Error",
                description: "Error al reintentar el envío por WhatsApp",
            });
        },
    });
};

export const useSendEmailInvitation = () => {
    const queryClient = useQueryClient();
    const { notification } = App.useApp();

    return useMutation({
        mutationFn: sendEmailInvitation,
        onSuccess: () => {
            notification.success({
                message: "Éxito",
                description: "Invitación por email enviada correctamente",
            });
            queryClient.invalidateQueries({ queryKey: ["event-reminders"] });
            queryClient.invalidateQueries({ queryKey: ["event-reminder-metrics"] });
        },
        onError: () => {
            notification.error({
                message: "Error",
                description: "Error al enviar la invitación por email",
            });
        },
    });
};

export const useSendWhatsappInvitation = () => {
    const queryClient = useQueryClient();
    const { notification } = App.useApp();

    return useMutation({
        mutationFn: sendWhatsappInvitation,
        onSuccess: () => {
            notification.success({
                message: "Éxito",
                description: "Invitación por WhatsApp enviada correctamente",
            });
            queryClient.invalidateQueries({ queryKey: ["event-reminders"] });
            queryClient.invalidateQueries({ queryKey: ["event-reminder-metrics"] });
        },
        onError: () => {
            notification.error({
                message: "Error",
                description: "Error al enviar la invitación por WhatsApp",
            });
        },
    });
};
