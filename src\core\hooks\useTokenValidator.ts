import { useEffect } from "react";
import { useAuthStore } from "@store/authStore";

/**
 * Hook to automatically validate token on component mount
 * Use this in components that need fresh token validation
 */
export const useTokenValidator = () => {
    const { validateToken, isAuthenticated, key } = useAuthStore((state) => state);

    useEffect(() => {
        if (isAuthenticated && key) {
            validateToken();
        }
    }, [validateToken, isAuthenticated, key]);
};

/**
 * Manual token validation function
 * Use this when you need to trigger validation programmatically
 * NOTE: This will force logout if any data mismatch is detected
 */
export const validateUserToken = () => {
    const validateToken = useAuthStore.getState().validateToken;
    return validateToken();
};
