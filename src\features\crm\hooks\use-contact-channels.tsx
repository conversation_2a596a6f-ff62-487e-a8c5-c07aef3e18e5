import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
    createContactChannel,
    getContactChannels,
    ListContactChannelsParams,
} from "../services/portals/contact-channel";
import { ContactChannelCreate } from "../types/contact-channels";
import { useApiError } from "@hooks/use-api-error";
import { App } from "antd";
import type { AxiosError } from "axios";

export type UseContactChannelsQuery = Pick<ListContactChannelsParams, "search">;

type UseContactChannelsProps = {
    page?: number;
    pageSize?: number;
    query?: UseContactChannelsQuery;
};

export const useContactChannels = ({
    page = 1,
    pageSize = 20,
    query,
}: UseContactChannelsProps = {}) => {
    const { data, isLoading, isError, isFetching } = useQuery({
        queryKey: ["contact-channels", { page, pageSize, query }],
        queryFn: () =>
            getContactChannels({
                page,
                pageSize,
                search: query?.search,
            }),
        refetchOnWindowFocus: false,
    });

    const { count: COUNT, results: contactChannels } = data || {
        count: 0,
        results: [],
    };

    return {
        isLoading: isLoading || isFetching,
        isError,
        contactChannels,
        COUNT,
    };
};

interface CreateContactChannelProps {
    onSuccess?: () => void;
    onError?: (error: AxiosError) => void;
}

export const useCreateContactChannel = ({
    onSuccess,
    onError,
}: CreateContactChannelProps = {}) => {
    const { message } = App.useApp();
    const { handleError } = useApiError({
        title: "Error al crear el canal de contacto",
        genericMessage: "No se pudo crear el canal de contacto",
    });
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async (values: ContactChannelCreate) =>
            createContactChannel(values),
        onSuccess: () => {
            message.success({
                content: "Canal de contacto creado exitosamente",
            });
            queryClient.invalidateQueries({
                queryKey: ["contact-channels"],
            });
            onSuccess?.();
        },
        onError: (error: AxiosError) => {
            handleError(error);
            onError?.(error);
        },
    });
};
