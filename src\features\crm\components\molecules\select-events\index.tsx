import { useEvents } from "@/features/crm/hooks/use-event";
import { Select, type SelectProps } from "antd";
import type { DefaultOptionType } from "antd/es/select";
import { ExternalLink } from "lucide-react";
import { Link } from "react-router-dom";

interface SelectEventsProps extends Omit<SelectProps, "options"> {
    value?: string | string[]; // Add value prop for controlled component
    onChange?: (value: string | string[]) => void; // Add onChange prop
}

export default function SelectEvents({
    value,
    onChange,
    ...restProps
}: SelectEventsProps) {
    const { events, isLoading } = useEvents();

    const eventsOptions: SelectProps["options"] = events?.map((event) => ({
        value: event.eid,
        label: event.name,
        data: {
            ...event,
        },
    }));

    const filterOption = (input: string, option: DefaultOptionType | undefined) => {
        if (!option) return false;
        const searchText = input.toLowerCase();
        const label = String(option.label ?? "").toLowerCase() || "";
        const eventName = option.data?.name?.toLowerCase() || "";

        return label.includes(searchText) || eventName.includes(searchText);
    };

    return (
        <>
            <Select
                {...restProps}
                value={value}
                onChange={onChange}
                options={eventsOptions}
                filterOption={filterOption}
                showSearch
                optionFilterProp="label"
                allowClear
                optionRender={(option) => (
                    <div className="flex justify-between items-center">
                        <div className="text-wrap flex flex-col">
                            <span>{option.data.label}</span>{" "}
                            <span className="text-xs font-semibold text-gray-500">
                                {option.data?.data.offering?.name}
                            </span>
                        </div>
                        <Link
                            to={`/crm/events/${option.data.value}`}
                            title="View Event"
                        >
                            <ExternalLink size={14} />
                        </Link>
                    </div>
                )}
                loading={isLoading}
            />
        </>
    );
}
