import { PaginatedResponse, PaginationParams } from "@myTypes/base";
import {
    BlogCategory,
    CreateBlogCategoryBody,
    UpdateBlogCategoryBody,
} from "@myTypes/blog";
import { portalsApi } from "@services/portals";

export const DEFAULT_PAGE = 1;
export const DEFAULT_PAGE_SIZE = 10;

export type ListBlogCategoriesQuery = PaginationParams & {
    search?: string;
    parentId?: string;
    sortBy?: string;
    order?: "asc" | "desc";
};

/**
 * List blog categories with pagination and filtering
 */
export const listBlogCategories = async (
    params: ListBlogCategoriesQuery = {},
): Promise<PaginatedResponse<BlogCategory>> => {
    const response = await portalsApi.get("cms/blogs/categories", {
        params: {
            page: params.page || DEFAULT_PAGE,
            pageSize: params.pageSize || DEFAULT_PAGE_SIZE,
            ...params,
        },
    });
    return response.data;
};

export const createBlogCategory = async (
    data: CreateBlogCategoryBody,
): Promise<BlogCategory> => {
    const response = await portalsApi.post("cms/blogs/categories", data);
    return response.data;
};

/**
 * Retrieve a blog category by ID
 */
export const retrieveBlogCategory = async (bcid: string): Promise<BlogCategory> => {
    if (!bcid) {
        throw new Error("No blog category ID provided");
    }
    const response = await portalsApi.get(`cms/blogs/categories/${bcid}`);
    return response.data;
};

/**
 * Update a blog category
 */
export const updateBlogCategory = async (
    bcid: string,
    data: UpdateBlogCategoryBody,
): Promise<BlogCategory> => {
    if (!bcid) {
        throw new Error("No blog category ID provided");
    }
    const response = await portalsApi.patch(`cms/blogs/categories/${bcid}`, data);
    return response.data;
};

/**
 * Delete a blog category
 */
export const deleteBlogCategory = async (bcid: string): Promise<void> => {
    if (!bcid) {
        throw new Error("No blog category ID provided");
    }
    await portalsApi.delete(`cms/blogs/categories/${bcid}`);
};
