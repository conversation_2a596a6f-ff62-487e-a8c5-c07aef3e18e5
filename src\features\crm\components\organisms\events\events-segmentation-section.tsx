import React from "react";
import { Row, Col } from "antd";
import InterestsSegmentationChart from "@/features/crm/components/molecules/events/interests-segmentation-chart";
import ContactsSegmentationChart from "@/features/crm/components/molecules/events/contacts-segmentation-chart";
import { useDashboardEventsSegmentation } from "@/features/crm/hooks/use-dashboard-events";
import type { DashboardEventQueryParams } from "@/features/crm/types/dashboard/events";

interface EventsSegmentationSectionProps {
    queryParams: DashboardEventQueryParams;
}

const EventsSegmentationSection: React.FC<EventsSegmentationSectionProps> = ({
    queryParams,
}) => {
    const { data: segmentationData, isLoading } =
        useDashboardEventsSegmentation(queryParams);

    return (
        <Row gutter={[16, 16]}>
            <Col xs={24} lg={12}>
                <InterestsSegmentationChart
                    data={segmentationData?.interests}
                    isLoading={isLoading}
                />
            </Col>
            <Col xs={24} lg={12}>
                <ContactsSegmentationChart
                    data={segmentationData?.contacts}
                    isLoading={isLoading}
                />
            </Col>
        </Row>
    );
};

export default EventsSegmentationSection;
