import { Typo<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "antd";
import { Mail, Phone, Copy, CheckCircle } from "lucide-react";
import { useState } from "react";

const { Text } = Typography;

type ContactMethodsProps = {
    phoneNumbers?: string | string[];
    emails?: string | string[];
    showLabels?: boolean;
    maxVisible?: number;
};

/**
 * Component to display contact methods (phone numbers and emails) with proper formatting and copy functionality
 */
const ContactMethods = ({
    phoneNumbers,
    emails,
    showLabels = true,
    maxVisible = 2,
}: ContactMethodsProps) => {
    const [copiedPhone, setCopiedPhone] = useState<string | null>(null);
    const [copiedEmail, setCopiedEmail] = useState<string | null>(null);

    // Handle array or string input for phone numbers
    const phones = phoneNumbers
        ? (Array.isArray(phoneNumbers) ? phoneNumbers : [phoneNumbers]).filter(Boolean)
        : [];

    // Handle array or string input for emails
    const emailList = emails
        ? (Array.isArray(emails) ? emails : [emails]).filter(Boolean)
        : [];

    const handleCopyPhone = (phone: string) => {
        navigator.clipboard.writeText(phone);
        setCopiedPhone(phone);
        setTimeout(() => setCopiedPhone(null), 2000);
    };

    const handleCopyEmail = (email: string) => {
        navigator.clipboard.writeText(email);
        setCopiedEmail(email);
        setTimeout(() => setCopiedEmail(null), 2000);
    };

    return (
        <div className="flex flex-col gap-2">
            {/* Phone Numbers Section */}
            {phones.length > 0 && (
                <div className="flex flex-col gap-1">
                    {showLabels && (
                        <Text className="text-xs text-gray-500 font-medium">
                            {phones.length > 1 ? "Teléfonos" : "Teléfono"}
                        </Text>
                    )}

                    {phones.slice(0, maxVisible).map((phone, index) => (
                        <div
                            key={`phone-${index}`}
                            className="flex items-center justify-between"
                        >
                            <div className="flex items-center">
                                <Phone size={14} className="text-gray-400 mr-1.5" />
                                <Text className="text-xs">{phone}</Text>
                            </div>
                            <Tooltip
                                title={
                                    copiedPhone === phone
                                        ? "¡Copiado!"
                                        : "Copiar número"
                                }
                            >
                                <Button
                                    type="text"
                                    size="small"
                                    icon={
                                        copiedPhone === phone ? (
                                            <CheckCircle
                                                size={14}
                                                className="text-green-500"
                                            />
                                        ) : (
                                            <Copy size={14} />
                                        )
                                    }
                                    onClick={() => handleCopyPhone(phone)}
                                />
                            </Tooltip>
                        </div>
                    ))}

                    {phones.length > maxVisible && (
                        <Text type="secondary" className="text-xs">
                            +{phones.length - maxVisible} más
                        </Text>
                    )}
                </div>
            )}

            {/* Email Addresses Section */}
            {emailList.length > 0 && (
                <div className="flex flex-col gap-1">
                    {showLabels && (
                        <Text className="text-xs text-gray-500 font-medium">
                            {emailList.length > 1
                                ? "Correos Electrónicos"
                                : "Correo Electrónico"}
                        </Text>
                    )}

                    {emailList.slice(0, maxVisible).map((email, index) => (
                        <div
                            key={`email-${index}`}
                            className="flex items-center justify-between"
                        >
                            <div className="flex items-center">
                                <Mail size={14} className="text-gray-400 mr-1.5" />
                                <Tooltip title={email}>
                                    <Text className="text-xs truncate max-w-[180px]">
                                        {email}
                                    </Text>
                                </Tooltip>
                            </div>
                            <Tooltip
                                title={
                                    copiedEmail === email
                                        ? "¡Copiado!"
                                        : "Copiar correo"
                                }
                            >
                                <Button
                                    type="text"
                                    size="small"
                                    icon={
                                        copiedEmail === email ? (
                                            <CheckCircle
                                                size={14}
                                                className="text-green-500"
                                            />
                                        ) : (
                                            <Copy size={14} />
                                        )
                                    }
                                    onClick={() => handleCopyEmail(email)}
                                />
                            </Tooltip>
                        </div>
                    ))}

                    {emailList.length > maxVisible && (
                        <Text type="secondary" className="text-xs">
                            +{emailList.length - maxVisible} más
                        </Text>
                    )}
                </div>
            )}

            {phones.length === 0 && emailList.length === 0 && (
                <Text type="secondary" italic>
                    No hay métodos de contacto disponibles
                </Text>
            )}
        </div>
    );
};

export default ContactMethods;
