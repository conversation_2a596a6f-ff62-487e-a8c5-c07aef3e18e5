import { useMutation, useQueryClient } from "@tanstack/react-query";
import { deleteEnrollment } from "../services/portals/enrollment";
import { message } from "antd";
import { AxiosError } from "axios";

interface ErrorResponse {
    message?: string;
}

export const useDeleteEnrollment = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: deleteEnrollment,
        onSuccess: () => {
            message.success("Matrícula eliminada exitosamente");
            queryClient.invalidateQueries({ queryKey: ["enrollments"] });
        },
        onError: (error: AxiosError<ErrorResponse>) => {
            const errorMessage =
                error.response?.data?.message || "Error al eliminar la matrícula";
            message.error(errorMessage);
        },
    });
};
