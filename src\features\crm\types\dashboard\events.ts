// Events Dashboard Types

export type DashboardEventQueryParams = Partial<{
    startDate: string;
    endDate: string;
    events: string;
    stage: string;
    eventType: string;
    modality: string;
    partnerships: string;
    programs: string;
}>;

// Summary Types
export type EventStats = {
    planning: number;
    launched: number;
    enrollmentClosed: number;
    finished: number;
    total: number;
};

export type ConversionMetric = {
    percentage: number;
    converted: number;
    totalEnrollments: number;
};

export type DashboardEventSummaryData = {
    stats: EventStats;
    needsConciliation: number;
    alliancesEnrollments: number;
    conversion: ConversionMetric;
    filtersApplied: DashboardEventQueryParams;
};

// Analytics Types
type EventByTypeItem = {
    count: number;
    totalEnrollments: number;
};
export type EventByType = {
    workshop: EventByTypeItem;
    webinar: EventByTypeItem;
    handsOfWorkshop: EventByTypeItem;
};

export type DiffusionChannel = {
    channel: string;
    total: number;
    hasContact: number;
    needsConciliation: number;
    alreadyLead: number;
};

export type TopAlliance = {
    allianceName: string;
    associatedEventsCount: number;
    uniqueEnrollments: number;
    totalEnrollments: number;
    globalParticipationPercentage: number;
    participationPercentage: number;
};

export type DashboardEventAnalyticsData = {
    eventByType: EventByType;
    diffusionChannels: DiffusionChannel[];
    topAlliances: TopAlliance[];
    filtersApplied: DashboardEventQueryParams;
};

// Segmentation Types
export type InterestSegmentation = {
    specialization: string;
    count: number;
};

export type ContactSegmentation = {
    newContacts: {
        total: number;
        percentage: number;
    };
    hasContact: {
        total: number;
        percentage: number;
    };
};

export type DashboardEventSegmentationData = {
    interests: InterestSegmentation[];
    contacts: ContactSegmentation;
    filtersApplied: DashboardEventQueryParams;
};

// In Course Types
export type InvitationChannelStatus = {
    totalPending: number;
    totalSent: number;
    totalFailed: number;
};

export type InvitationStatus = {
    totalReminders: number;
    totalSent: number;
    totalPending: number;
    totalFailed: number;
    email: InvitationChannelStatus;
    whatsapp: InvitationChannelStatus;
};

export enum LaunchedEventStatus {
    IN_COURSE = "in_course",
    FUTURE = "future",
    PAST = "past",
}

export type LaunchedEvent = {
    esid: string;
    eventName: string;
    startDate: string;
    endDate: string;
    extEventLink: string;
    enrollmentCount: number;
    invitationStatus: InvitationStatus;
    eventStatus: LaunchedEventStatus;
    offering: string;
};

export type DashboardEventsLaunchedData = LaunchedEvent[];

export type EventHistoricalEducationalInstitution = {
    enrollments: number;
    name: string;
    acronym: string | null;
};

export type EventHistoricalItem = {
    esid: string;
    eventName: string;
    startDate: string;
    endDate: string;
    totalEnrollments: number;
    withouthEducationalInstitution: number;
    topEducationalInstitutions: EventHistoricalEducationalInstitution[];
};

export type DashboardEventsHistoricalData = {
    data: EventHistoricalItem[];
    filtersApplied: DashboardEventQueryParams;
};
