type AuditBaseType = {
    createdAt: string;
    updatedAt: string;
};

export enum OrderStage {
    PROSPECT = "prospect",
    INTERESTED = "interested",
    TO_PAY = "to_pay",
    SOLD = "sold",
    LOST = "lost",
}

export const OrderStageLabels: Record<OrderStage, string> = {
    [OrderStage.PROSPECT]: "Prospecto",
    [OrderStage.INTERESTED]: "Interesado",
    [OrderStage.TO_PAY]: "Por pagar",
    [OrderStage.SOLD]: "Vendido",
    [OrderStage.LOST]: "Perdido",
};

export enum OrderCurrency {
    USD = "usd",
    PEN = "pen",
}

export const OrderCurrencyLabels: Record<OrderCurrency, string> = {
    [OrderCurrency.USD]: "Dólares",
    [OrderCurrency.PEN]: "Soles",
};

export type OrderContact = {
    uid: string;
    firstName?: string;
    lastName?: string;
    fullName?: string;
    email?: string;
    phoneNumber: string;
};

export type OrderItemOffering = {
    oid: string;
    name: string;
    slug: string;
};

export enum OrderItemInvitationStatus {
    PENDING = "pending",
    SENDING = "sending",
    ERROR = "error",
    SENT = "sent",
}

export const OrderItemInvitationStatusLabels: Record<
    OrderItemInvitationStatus,
    string
> = {
    [OrderItemInvitationStatus.PENDING]: "Pendiente",
    [OrderItemInvitationStatus.SENDING]: "Enviando",
    [OrderItemInvitationStatus.ERROR]: "Error",
    [OrderItemInvitationStatus.SENT]: "Enviado",
};

export type OrderItemEnrollment = {
    eid: string;
    key: string;
    user: {
        uid: string;
        fullName: string;
        email: string;
        phoneNumber?: string;
    };
    certificateIssued?: boolean;
    isActive?: boolean;
} & AuditBaseType;

export type OrderItem = {
    oiid: string;
    key: string;
    offering: OrderItemOffering;

    quantity: number;
    basePrice: number;
    foreignBasePrice: number;
    discount: number;
    unitPrice: number;
    totalPrice: number;
    price: number;
    foreignUnitPrice: number;
    customAmount?: number | null; // custom price
    customAmountPrice: number; // custom amount * quantity
    effectiveTotalPrice?: number; // total price based on order's international status
    effectiveUnitPrice?: number; // price based on order's international status

    hasEnrollment: boolean;
    enrollment?: OrderItemEnrollment;

    extInvitationStatus?: OrderItemInvitationStatus;
} & Partial<AuditBaseType>;

export type CreateOrderItem = {
    order: string; // parent order UUID
    offering: string; // offering UUID
    quantity?: number; // default 1
    customAmount?: number | null; // custom price
};

export type Order = {
    oid: string;
    owner: OrderContact;
    stage: OrderStage;
    orderItems: OrderItem[];

    prospectAt?: string;
    interestedAt?: string;
    toPayAt?: string;
    soldAt?: string;
    lostAt?: string;

    isInternational?: boolean;
    hasFullScholarship?: boolean;

    total?: number;
    salesAgent?: OrderContact;
} & AuditBaseType;

export type StageDate = {
    stage: OrderStage;
    date: string;
};

export type OrderBenefit = {
    bid: string;
    name: string;
    description: string;
} & AuditBaseType;

export type OrderContactChannel = {
    lsid: string;
    name: string;
} & AuditBaseType;

export type OrderLeadOrigin = {
    loid: string;
    name: string;
} & AuditBaseType;

export type RetrieveOrder = {
    oid: string;
    owner: OrderContact;
    stage: OrderStage;
    salesAgent: OrderContact;
    orderItems: OrderItem[];
    stagesDates: StageDate[];
    benefits: OrderBenefit[];
    contactChannels: OrderContactChannel[];
    leadOrigins?: OrderLeadOrigin[];
    isInternational?: boolean;
    hasFullScholarship?: boolean;
    agreedTotal?: number;
} & AuditBaseType;

export type CreateOrderFormValues = {
    owner: string;
    stage: OrderStage;
    products: string[];
    benefits: string[];
};

export type CreateOrder = {
    owner: string;
    stage: OrderStage;
    products: string[];
    benefits: string[];
};

export type PartialUpdateOrderValues = {
    owner: string;
    salesAgent: string;
    benefits: string[];
    isInternational: boolean;
    hasFullScholarship: boolean;
    contactChannels: string[];
    leadOrigins: string[];
};

export type OrderPartialUpdate = {
    owner: string;
    salesAgent?: string;
    benefits?: string[];
    contactchannel?: string[]; // existing server naming
    leadOrigins?: string[];
    stage?: OrderStage;
    prospectAt?: string;
    interestedAt?: string;
    isInternational?: boolean;
    hasFullScholarship?: boolean;
    toPayAt?: string;
    soldAt?: string;
    lostAt?: string;
};
