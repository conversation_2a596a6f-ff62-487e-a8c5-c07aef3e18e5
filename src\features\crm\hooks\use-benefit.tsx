import { useMutation, useQuery } from "@tanstack/react-query";
import { createBenefit, getBenefits } from "../services/portals/benefit";
import queryClient from "@lib/queryClient";
import { AxiosError } from "axios";
import { Benefit, CreateBenefit } from "../types/benefit";

export const useBenefits = () => {
    const { data, isLoading, isError } = useQuery({
        queryKey: ["benefits"],
        queryFn: () => getBenefits(),
    });

    const { count: COUNT, results: benefits } = data || {
        count: 0,
        results: [],
    };

    return {
        isLoading,
        isError,
        benefits,
        COUNT,
    };
};

type UseCreateBenefitProps = {
    onSuccess?: () => void;
    onError?: () => void;
};

export const useCreateBenefit = ({ onSuccess, onError }: UseCreateBenefitProps) => {
    return useMutation<Benefit, AxiosError, CreateBenefit>({
        mutationFn: (newBenefit: CreateBenefit) => createBenefit(newBenefit),
        onSuccess: (benefit) => {
            if (benefit) {
                queryClient.invalidateQueries({ queryKey: ["benefits"] });
            }
            onSuccess && onSuccess();
        },
        onError: (err) => {
            console.error(err);
            onError && onError();
        },
    });
};
