import React from "react";
import { Card } from "antd";
import {
    Composed<PERSON><PERSON>,
    Bar,
    XAxis,
    YAxis,
    CartesianGrid,
    Tooltip,
    ResponsiveContainer,
    Legend,
    Area,
} from "recharts";

type LegendItem = {
    name: string;
    color: string;
};

interface ComposedChartCardProps {
    title: string;
    data: Array<Record<string, unknown>>;
    barDataKey: string;
    lineDataKey: string;
    xAxisDataKey?: string;
    barColor?: string;
    lineColor?: string;
    icon?: React.ReactNode;
    formatter?: (value: number) => string;
    legend?: LegendItem[];
}

const ComposedChartCard: React.FC<ComposedChartCardProps> = ({
    title,
    data,
    barDataKey,
    lineDataKey,
    xAxisDataKey = "name",
    barColor = "#1890ff",
    lineColor = "#ff4d4f",
    icon,
    formatter,
    legend,
}) => {
    interface CustomTooltipProps {
        active?: boolean;
        payload?: Array<{
            value: number;
            dataKey: string;
            name: string;
            color: string;
        }>;
        label?: string;
    }

    const CustomTooltip = ({ active, payload, label }: CustomTooltipProps) => {
        if (active && payload && payload.length) {
            // Separa los datos del área (acumulado) y los demás
            const areaEntries = payload.filter(
                (entry) => entry.dataKey === lineDataKey,
            );
            const barEntries = payload.filter((entry) => entry.dataKey !== lineDataKey);

            return (
                <div className="bg-white-full p-2 border border-gray-200 shadow-md rounded">
                    <p className="font-medium">{`${label}`}</p>
                    {barEntries.map((entry, index) => (
                        <p key={index} style={{ color: entry.color }}>
                            Nuevos: {" "}
                            {legend && legend[index] ? `${legend[index].name}: ` : ""}
                            {formatter ? formatter(entry.value) : entry.value}
                        </p>
                    ))}
                    {areaEntries.map((entry, index) => (
                        <p key={`accumulated-${index}`} style={{ color: entry.color }}>
                            {"Total acumulado: "}
                            {formatter ? formatter(entry.value) : entry.value}
                        </p>
                    ))}
                </div>
            );
        }
        return null;
    };

    return (
        <Card
            title={
                <div className="flex items-center gap-2">
                    {icon}
                    <span>{title}</span>
                </div>
            }
            className="shadow-md h-full"
        >
            <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                    <ComposedChart
                        data={data}
                        margin={{
                            top: 5,
                            right: 30,
                            left: 20,
                            bottom: 5,
                        }}
                    >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis
                            dataKey={xAxisDataKey}
                            angle={-45}
                            textAnchor="end"
                            height={80}
                            interval={0}
                            tick={{ fontSize: 12 }}
                        />
                        <YAxis
                            yAxisId="left"
                            tickFormatter={(value) =>
                                formatter
                                    ? formatter(value).split(" ")[0] // For currency formatters
                                    : value
                            }
                        />
                        <YAxis
                            yAxisId="right"
                            orientation="right"
                            tickFormatter={(value) =>
                                formatter ? formatter(value).split(" ")[0] : value
                            }
                        />
                        <Tooltip content={<CustomTooltip />} />
                        {legend && <Legend />}
                        <Area
                            type="monotone"
                            dataKey={lineDataKey}
                            fill={lineColor}
                            stroke={lineColor}
                            yAxisId="right"
                        />
                        <Bar
                            yAxisId="left"
                            dataKey={barDataKey}
                            fill={barColor}
                            name={legend ? legend[0].name : barDataKey}
                            radius={[4, 4, 0, 0]}
                            animationDuration={1500}
                        />
                    </ComposedChart>
                </ResponsiveContainer>
            </div>
        </Card>
    );
};

export default ComposedChartCard;
