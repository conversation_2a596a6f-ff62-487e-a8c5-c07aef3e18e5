import { useMemo, useState } from "react";
import {
    <PERSON><PERSON>,
    <PERSON>ton,
    ConfigProvider,
    Dropdown,
    Empty,
    GetProps,
    Input,
    Modal,
    Pagination,
    Radio,
    Table,
    TableProps,
    Tooltip,
    Typography,
} from "antd";
import CrmLayout from "@/features/crm/layout";
import {
    Grid2x2,
    Hash,
    MoreVertical,
    Plus,
    Rows3,
    Users,
    Briefcase,
    BadgeCheck,
    Scissors,
    SlidersHorizontal,
} from "lucide-react";
import WelcomeBar from "@components/shared/molecules/WelcomeBar";
import { ContactListItem } from "@/features/crm/types/contact";
import { useQuery } from "@tanstack/react-query";
import {
    ContactQueryParams,
    getContacts,
} from "@/features/crm/services/portals/contact";
import Spinner from "@components/shared/atoms/Spinner";
import { Link, useNavigate, useSearchParams } from "react-router-dom";

import DeleteStroke from "@assets/icons/huge/delete-stroke.svg?react";
import EditStroke from "@assets/icons/huge/edit-stroke.svg?react";
import CreateContactForm from "@/features/crm/components/organisms/create-contact-form";
import ContactsFilters from "@/features/crm/components/organisms/contacts-filters";
import ContactCard from "@/features/crm/components/molecules/contact-card";
import { useDeleteContact } from "@hooks/crm/useDeleteContact";
import ContactInfoCell from "@/features/crm/components/atoms/contact-info-cell";
import OccupationTag from "@/features/crm/components/atoms/occupation-tag";
import EducationInfoCell from "@/features/crm/components/atoms/education-info-cell";
import WorkInfoCell from "@/features/crm/components/atoms/work-info-cell";
import SystemInfoCell from "@/features/crm/components/atoms/system-info-cell";
import ActivityInfoCell from "@/features/crm/components/atoms/activity-info-cell";
import DateCell from "@/features/crm/components/atoms/date-cell";

const { Search } = Input;
const { Text } = Typography;

type SearchProps = GetProps<typeof Input.Search>;

const STATUS_FILTER_TAGS = [
    {
        value: "all",
        label: "Todos",
        icon: "Users",
    },
    {
        value: "staff",
        label: "Staff",
        icon: "Briefcase",
    },
    {
        value: "active",
        label: "Activos",
        icon: "BadgeCheck",
    },
];

enum DisplayMode {
    LIST = "list",
    DECK = "deck",
}

const INITIAL_COLUMNS: TableProps<ContactListItem>["columns"] = [
    {
        title: "ID",
        dataIndex: "uid",
        key: "uid",
        width: 80,
        render: (uid: string) => (
            <Tooltip title="Ver detalles del contacto">
                <Link
                    to={`/crm/contacts/${uid}`}
                    className="font-semibold text-blue-full flex items-center gap-1"
                >
                    <Hash size={14} strokeWidth={2.5} className="text-gray-500" />
                    {uid.slice(-6)}
                </Link>
            </Tooltip>
        ),
    },
    {
        title: "INFORMACIÓN",
        dataIndex: "fullName",
        key: "fullName",
        render: (_: string, record: ContactListItem) => (
            <ContactInfoCell contact={record} />
        ),
    },
    {
        title: "OCUPACIÓN",
        dataIndex: "ocupation",
        key: "ocupation",
        width: 150,
        render: (ocupation?: string) => <OccupationTag occupation={ocupation} />,
    },
    {
        title: "EDUCACIÓN",
        key: "education",
        width: 250,
        render: (_: string, record: ContactListItem) => (
            <EducationInfoCell contact={record} />
        ),
    },
    {
        title: "LABORAL",
        key: "work",
        width: 250,
        render: (_: string, record: ContactListItem) => (
            <WorkInfoCell contact={record} />
        ),
    },
    {
        title: "CUENTA",
        key: "account",
        width: 150,
        render: (_: string, record: ContactListItem) => (
            <SystemInfoCell contact={record} />
        ),
    },
    {
        title: "GOOGLE",
        width: 200,
        render: (_: string, record: ContactListItem) => (
            <ActivityInfoCell contact={record} />
        ),
    },
    {
        title: "FECHAS",
        dataIndex: "createdAt",
        key: "dates",
        render: (_: string, record: ContactListItem) => (
            <div className="space-y-2">
                <DateCell date={record.createdAt} label="Creación" />
                <DateCell date={record.updatedAt} label="Actualización" />
            </div>
        ),
    },
];

const PAGE_SIZE: number = 10;
const DEFAULT_PAGE: number = 1;

export default function ContactsListPage() {
    const [searchParams, setSearchParams] = useSearchParams();

    const mainFilterTag = searchParams.get("mainFilterTag") || "all";
    const display = searchParams.get("display") || DisplayMode.LIST;
    const page = searchParams.get("page")
        ? Number(searchParams.get("page"))
        : DEFAULT_PAGE;
    const pageSize = searchParams.get("pageSize")
        ? Number(searchParams.get("pageSize"))
        : PAGE_SIZE;
    const search = searchParams.get("search");
    const ocupation = searchParams.get("ocupation") || "";
    const major = searchParams.get("major") || "";
    const term = searchParams.get("term") || "";
    const educationalInstitution = searchParams.get("educationalInstitution") || "";
    const createdAtAfter = searchParams.get("createdAtAfter") || "";
    const createdAtBefore = searchParams.get("createdAtBefore") || "";

    const queryParams: ContactQueryParams = useMemo(
        () => ({
            page: Number(page),
            pageSize: Number(pageSize),
            ...(search ? { search } : {}),
            ...(mainFilterTag === "staff" ? { isStaff: true } : {}),
            ...(mainFilterTag === "active" ? { isActive: true } : {}),
            ...(ocupation ? { ocupation } : {}),
            ...(major ? { major } : {}),
            ...(term ? { term } : {}),
            ...(educationalInstitution ? { educationalInstitution } : {}),
            ...(createdAtAfter ? { createdAtAfter } : {}),
            ...(createdAtBefore ? { createdAtBefore } : {}),
        }),
        [page, pageSize, mainFilterTag, search, ocupation, major, term, educationalInstitution, createdAtAfter, createdAtBefore],
    );

    const { isLoading, data } = useQuery({
        queryKey: ["contacts", queryParams],
        queryFn: async () => {
            return getContacts(queryParams);
        },
    });
    const navigate = useNavigate();
    const [modalOpen, setModalOpen] = useState(false);
    const [isFilterDrawerOpen, setIsFilterDrawerOpen] = useState<boolean>(false);

    const { count: TOTAL_CONTACTS, results: contacts } = data || {
        count: 0,
        results: [],
    };

    const { mutate: deleteMutate } = useDeleteContact();

    const onSearch: SearchProps["onSearch"] = (value, _e, info) => {
        if (info?.source === "input") {
            setSearchParams((prev) => {
                prev.set("search", value);
                return prev;
            });
        }
    };

    const tableColumns = INITIAL_COLUMNS;

    const handleRowAction = (key: string, record: ContactListItem) => {
        if (key === "edit") {
            navigate(`/crm/contacts/${record.uid}`);
        }
        if (key === "delete") {
            deleteMutate(record.uid);
        }
    };

    const handleCloseModal = () => {
        setModalOpen(false);
    };

    const handleSetDisplayMode = (value: DisplayMode) => {
        setSearchParams((prev) => {
            prev.set("display", value);
            return prev;
        });
    };

    const handleSetPage = (value: number, pageSize: number) => {
        setSearchParams((prev) => {
            prev.set("page", value.toString());
            prev.set("pageSize", pageSize.toString());
            return prev;
        });
    };

    const actionColumns: TableProps<ContactListItem>["columns"] = [
        {
            title: <Scissors />,
            key: "actions",
            render: (record: ContactListItem) => (
                <Dropdown
                    trigger={["click"]}
                    menu={{
                        items: [
                            {
                                key: "edit",
                                label: (
                                    <>
                                        <div className="flex items-center gap-2 text-blue-full">
                                            <EditStroke className="w-5 h-5" /> Editar
                                        </div>
                                    </>
                                ),
                            },
                            {
                                key: "delete",
                                label: (
                                    <>
                                        <div className="flex items-center gap-2 text-state-red-full">
                                            <DeleteStroke className="w-5 h-5" />{" "}
                                            Eliminar
                                        </div>
                                    </>
                                ),
                            },
                        ],
                        onClick: ({ key }) => {
                            handleRowAction(key, record);
                        },
                    }}
                    placement="bottomRight"
                >
                    <Button
                        icon={<MoreVertical className="w-5 h-5" />}
                        type="text"
                        size="small"
                    />
                </Dropdown>
            ),
        },
    ];

    const handleSetMainFilterTag = (value: string) => {
        setSearchParams((prev) => {
            prev.set("mainFilterTag", value);
            return prev;
        });
    };

    const clearFilters = () => {
        setSearchParams((prev) => {
            prev.delete("ocupation");
            prev.delete("major");
            prev.delete("term");
            prev.delete("educationalInstitution");
            prev.delete("createdAtAfter");
            prev.delete("createdAtBefore");
            prev.set("page", "1");
            return prev;
        });
    };

    return (
        <CrmLayout>
            <div className="w-full h-full space-y-5">
                <div className="flex justify-between items-center">
                    <WelcomeBar helperText="Gestiona aquí los contactos de CEU" />
                    <div className="flex gap-3">
                        <Button
                            type="primary"
                            size="large"
                            style={{ fontSize: 16 }}
                            icon={<Plus />}
                            onClick={() => {
                                setModalOpen(true);
                            }}
                        >
                            Agregar
                        </Button>
                        <Modal
                            centered
                            open={modalOpen}
                            onCancel={handleCloseModal}
                            footer={false}
                            title={
                                <div className="w-full flex justify-center text-2xl py-4">
                                    Agregar nuevo Contacto
                                </div>
                            }
                        >
                            <CreateContactForm handleCloseModal={handleCloseModal} />
                        </Modal>
                    </div>
                </div>
                <div className="p-5 bg-white-full rounded-lg space-y-5">
                    <div className="flex flex-col lg:flex-row justify-between items-center">
                        <Text className="text-black-medium text-2xl font-semibold">
                            Contactos{" "}
                            <Badge count={TOTAL_CONTACTS} color="blue" size="default" />
                        </Text>
                        <Search
                            size="large"
                            placeholder="Buscar por nombre, email o teléfono"
                            onSearch={onSearch}
                            enterButton
                            allowClear
                            className="max-w-screen-sm"
                        />
                        <div className="flex items-center gap-3">
                            <Radio.Group
                                value={display}
                                onChange={(e) => handleSetDisplayMode(e.target.value)}
                                style={{ display: "flex" }}
                            >
                                <Radio.Button
                                    value={DisplayMode.LIST}
                                    style={{
                                        display: "flex",
                                        alignItems: "center",
                                        justifyContent: "center",
                                        marginRight: "-1px",
                                    }}
                                >
                                    <Tooltip title="Ver como lista">
                                        <Rows3 size={16} />
                                    </Tooltip>
                                </Radio.Button>
                                <Radio.Button
                                    value={DisplayMode.DECK}
                                    style={{
                                        display: "flex",
                                        alignItems: "center",
                                        justifyContent: "center",
                                    }}
                                >
                                    <Tooltip title="Ver como tarjetas">
                                        <Grid2x2 size={16} />
                                    </Tooltip>
                                </Radio.Button>
                            </Radio.Group>

                            <div className="relative">
                                <Button
                                    icon={<SlidersHorizontal size={16} />}
                                    onClick={() => setIsFilterDrawerOpen(true)}
                                >
                                    Filtros
                                </Button>
                                {/* Active filters indicator */}
                                {(ocupation ||
                                    major ||
                                    term ||
                                    educationalInstitution ||
                                    createdAtAfter ||
                                    createdAtBefore) && (
                                    <Badge
                                        count={
                                            [
                                                ocupation,
                                                major,
                                                term,
                                                educationalInstitution,
                                                createdAtAfter || createdAtBefore,
                                            ].filter(Boolean).length
                                        }
                                        size="small"
                                        className="absolute -top-1 -right-1"
                                    />
                                )}
                            </div>
                        </div>
                    </div>

                    <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg shadow-sm">
                        {STATUS_FILTER_TAGS.map((tag) => {
                            let TagIcon;
                            switch (tag.icon) {
                                case "Users":
                                    TagIcon = Users;
                                    break;
                                case "Briefcase":
                                    TagIcon = Briefcase;
                                    break;
                                case "BadgeCheck":
                                    TagIcon = BadgeCheck;
                                    break;
                                default:
                                    TagIcon = Users;
                            }

                            return (
                                <button
                                    key={tag.value}
                                    onClick={() => handleSetMainFilterTag(tag.value)}
                                    className={`px-4 py-2 text-sm font-medium rounded-full transition-all duration-200 flex items-center gap-1.5 ${
                                        mainFilterTag === tag.value
                                            ? "bg-blue-500 text-white-full shadow-md"
                                            : "bg-white text-gray-600 hover:bg-gray-100"
                                    }`}
                                >
                                    <TagIcon size={14} />
                                    {tag.label}
                                </button>
                            );
                        })}
                    </div>

                    {/* Active Filters Summary */}
                    {(ocupation ||
                        major ||
                        term ||
                        educationalInstitution ||
                        createdAtAfter ||
                        createdAtBefore) && (
                        <div className="flex flex-wrap items-center gap-2 p-3 bg-blue-50 rounded-lg border border-blue-200">
                            <Typography.Text type="secondary" className="text-sm font-medium">
                                Filtros activos:
                            </Typography.Text>
                            {ocupation && (
                                <Badge
                                    count="Ocupación"
                                    color="blue"
                                    style={{ backgroundColor: "#1890ff" }}
                                />
                            )}
                            {major && (
                                <Badge
                                    count="Carrera"
                                    color="blue"
                                    style={{ backgroundColor: "#1890ff" }}
                                />
                            )}
                            {term && (
                                <Badge
                                    count="Ciclo"
                                    color="blue"
                                    style={{ backgroundColor: "#1890ff" }}
                                />
                            )}
                            {educationalInstitution && (
                                <Badge
                                    count="Universidad"
                                    color="blue"
                                    style={{ backgroundColor: "#1890ff" }}
                                />
                            )}
                            {(createdAtAfter || createdAtBefore) && (
                                <Badge
                                    count="Fecha de creación"
                                    color="blue"
                                    style={{ backgroundColor: "#52c41a" }}
                                />
                            )}
                            <Button
                                type="link"
                                size="small"
                                onClick={clearFilters}
                                className="text-blue-600 hover:text-blue-800 p-0 h-auto"
                            >
                                Limpiar todos
                            </Button>
                        </div>
                    )}

                    {display === DisplayMode.LIST ? (
                        <ConfigProvider
                            theme={{
                                components: {
                                    Table: {
                                        headerBg: "#FBFCFD",
                                        borderColor: "#fff",
                                        headerSplitColor: "#fafafa",
                                        headerBorderRadius: 8,
                                        rowHoverBg: "#F6FAFD",
                                        rowSelectedBg: "#F6FAFD",
                                        rowSelectedHoverBg: "#F6FAFD",
                                        footerBg: "#F1F1F1",
                                    },
                                },
                            }}
                        >
                            <Table
                                className="rounded-lg shadow-sm"
                                footer={() => ""}
                                pagination={false}
                                columns={
                                    tableColumns
                                        ? [...tableColumns, ...actionColumns]
                                        : []
                                }
                                locale={{
                                    emptyText: (
                                        <>{isLoading ? <Spinner /> : <Empty />}</>
                                    ),
                                }}
                                dataSource={contacts}
                                rowKey="uid"
                                scroll={{ x: "max-content" }}
                            />
                        </ConfigProvider>
                    ) : contacts.length === 0 ? (
                        <Empty />
                    ) : (
                        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-x-2">
                            {contacts.map((contact) => (
                                <ContactCard key={contact.key} info={contact} />
                            ))}
                        </div>
                    )}
                    <div className="flex justify-between">
                        <div>
                            <Text className="text-sm text-gray-500">
                                Mostrando {contacts.length} de {TOTAL_CONTACTS}{" "}
                                contactos
                            </Text>
                        </div>
                        <Pagination
                            showSizeChanger
                            total={TOTAL_CONTACTS}
                            pageSize={pageSize}
                            current={page}
                            onChange={(page, pageSize) => {
                                handleSetPage(page, pageSize);
                            }}
                        />
                    </div>
                </div>
            </div>

            {/* Filters Drawer */}
            <ContactsFilters
                isOpen={isFilterDrawerOpen}
                onClose={() => setIsFilterDrawerOpen(false)}
                onClearFilters={clearFilters}
            />
        </CrmLayout>
    );
}
