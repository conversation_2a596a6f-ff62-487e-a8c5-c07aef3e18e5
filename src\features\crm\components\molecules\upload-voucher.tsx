import { App, DraggerProps, Image, Typography, Upload, UploadFile } from "antd";
import { CloudUpload } from "lucide-react";
import { removeVoucher, uploadVoucher } from "../../services/portals/payment";
import { RcFile } from "antd/es/upload";
import { PaymentVoucher } from "../../types/payment";
import { useState } from "react";

const { Dragger } = Upload;
const { Text } = Typography;

interface UploadVoucherProps {
    initialVoucher?: PaymentVoucher;
    value?: UploadFile[];
    onChange?: (fileList: UploadFile[]) => void;
}

export default function UploadVoucher({
    initialVoucher,
    value,
    onChange,
}: UploadVoucherProps) {
    const { notification } = App.useApp();

    const [previewOpen, setPreviewOpen] = useState(false);
    const [fileList, setFileList] = useState<UploadFile[]>(
        initialVoucher
            ? [
                  {
                      uid: initialVoucher.fid,
                      name: initialVoucher.name,
                      url: initialVoucher.url,
                      status: "done",
                  },
              ]
            : value || [],
    );

    const draggerProps: DraggerProps = {
        multiple: false,
        maxCount: 1,
        accept: ".pdf, .jpg, .jpeg, .png, .webp, .gif",
        listType: "picture",
        customRequest: async (options) => {
            const { file, onSuccess, onError } = options;
            try {
                const fileToUpload = file as RcFile & Partial<UploadFile>;
                const fileRes = await uploadVoucher(fileToUpload);
                if (fileRes && onSuccess) {
                    const updatedFile = { ...fileToUpload, uid: fileRes.fid };
                    onSuccess(fileRes, updatedFile);
                    notification.success({
                        message: "Voucher subido correctamente",
                        duration: 2,
                    });
                }
            } catch (error) {
                if (onError)
                    onError(
                        new Error(
                            error instanceof Error ? error.message : "Unknown error",
                        ),
                        file,
                    );
            }
        },
        onChange(info) {
            setFileList(info.fileList);
            if (onChange) {
                onChange(info.fileList);
            }
        },
        beforeUpload: (file) => {
            const isLt2M = file.size / 1024 / 1024 < 2;
            if (!isLt2M) {
                notification.error({
                    message: "El archivo debe ser menor a 2MB",
                    duration: 2,
                });
            }
            return isLt2M;
        },
        onPreview: async (file) => {
            if (file.url) {
                if (file.url.toLowerCase().includes(".pdf")) {
                    window.open(file.url, "_blank");
                } else {
                    setPreviewOpen(true);
                }
            }
        },
        onRemove: async (file) => {
            const fid = file?.response?.fid || file?.uid;
            try {
                await removeVoucher(fid);
                notification.success({
                    message: "Voucher eliminado correctamente",
                    duration: 2,
                });
            } catch (error) {
                notification.error({
                    message: "Error al eliminar el voucher",
                    duration: 2,
                });
            }
        },
        showUploadList: {
            showDownloadIcon: true,
            showRemoveIcon: true,
            showPreviewIcon: true,
        },
        fileList: fileList,
    };

    return (
        <>
            <Dragger {...draggerProps}>
                <div className="flex flex-col justify-center items-center">
                    <CloudUpload />
                    <Text className="font-medium text-black-full">
                        Arrastre una imagen o PDF aquí
                    </Text>
                    <Text className="text-xs text-black-medium">
                        Solo una imagen o PDF (Máx. 2MB)
                    </Text>
                </div>
            </Dragger>
            {/* Image preview */}
            <Image
                wrapperStyle={{ display: "none" }}
                preview={{
                    visible: previewOpen,
                    onVisibleChange: setPreviewOpen,
                }}
                src={fileList[0]?.url}
            />
        </>
    );
}
