// Module permissions configuration
export const modulePermissions = {
    erp: ["management"],
    crm: ["commercial"],
    cms: ["marketing"],
    lms: ["management", "academic_coordination"],
};

// Helper function to check if user has access to a module
export const hasModuleAccess = (
    moduleGroups: string[],
    userGroups?: string[],
): boolean => {
    if (!userGroups || userGroups.length === 0) return false;
    return userGroups.some((group) => moduleGroups.includes(group));
};

// Helper function to get accessible modules for a user
export const getAccessibleModules = (userGroups?: string[]): string[] => {
    if (!userGroups || userGroups.length === 0) return [];

    return Object.entries(modulePermissions)
        .filter(([, moduleGroups]) => hasModuleAccess(moduleGroups, userGroups))
        .map(([moduleKey]) => moduleKey);
};

// Type for module keys
export type ModuleKey = keyof typeof modulePermissions;
