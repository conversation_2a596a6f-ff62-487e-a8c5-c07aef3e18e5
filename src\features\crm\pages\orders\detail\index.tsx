import EditOrderForm from "@/features/crm/components/organisms/edit-order-form";
import OrderPaymentsTabs from "@/features/crm/components/organisms/order-payments-tabs";
import OrderActivitiesTabs from "@/features/crm/components/organisms/order-activities-tabs";
import { useOrder } from "@/features/crm/hooks/use-order";
import CrmLayout from "@/features/crm/layout";
import Spinner from "@components/shared/atoms/Spinner";
import WelcomeBar from "@components/shared/molecules/WelcomeBar";
import { Breadcrumb, Tabs, TabsProps, Typography } from "antd";
import { useEffect } from "react";
import { Link, useNavigate, useParams } from "react-router-dom";

const { Text } = Typography;

export default function OrdersDetailPage() {
    const { oid } = useParams<{ oid: string }>();
    const { isLoading, order, isError } = useOrder(oid as string);

    const navigate = useNavigate();

    useEffect(() => {
        if (isError) {
            navigate("/crm/orders");
        }
    }, [isError, navigate]);

    const tabItems: TabsProps["items"] = [
        {
            key: "general",
            label: "General",
            children: order && <EditOrderForm order={order} />,
        },
        {
            key: "payments",
            label: "Pagos/Deudas",
            children: order && <OrderPaymentsTabs order={order} />,
        },
        {
            key: "activities",
            label: "Actividades",
            children: order && <OrderActivitiesTabs order={order} />,
        },
    ];

    return (
        <CrmLayout>
            <div className="w-full h-full space-y-5 max-w-7xl">
                <div className="flex justify-between items-center">
                    <WelcomeBar helperText="Visualiza & Edita los campos necesarios" />
                </div>
                {isLoading ? (
                    <Spinner />
                ) : (
                    <>
                        <div className="p-5 bg-white-full rounded-lg space-y-5 shadow-sm">
                            <div className="flex items-center">
                                <Breadcrumb
                                    separator=">"
                                    items={[
                                        {
                                            title: (
                                                <Link
                                                    to="/crm/orders"
                                                    className="text-base"
                                                >
                                                    Órdenes
                                                </Link>
                                            ),
                                        },
                                        {
                                            title: (
                                                <Link
                                                    to={`/crm/orders/${oid}`}
                                                    className="text-base"
                                                >
                                                    {order?.owner?.fullName}
                                                    {" #"}
                                                    {order?.oid?.slice(-6)}
                                                </Link>
                                            ),
                                        },
                                        {
                                            title: (
                                                <Text className="text-base">
                                                    Ver & Editar
                                                </Text>
                                            ),
                                        },
                                    ]}
                                />
                            </div>
                        </div>
                        <div className="space-y-5">
                            <Tabs items={tabItems} />
                        </div>
                    </>
                )}
            </div>
        </CrmLayout>
    );
}
