import { Card, Skeleton } from "antd";
import { DollarSign } from "lucide-react";
import { Re<PERSON>ons<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Cell, Tooltip, Legend } from "recharts";

import {
    useDashboardPaymentsCurrencyDistribution,
    createDashboardPaymentsQueryParams,
} from "../../hooks/use-dashboard-payments";
import { useSearchParams } from "react-router-dom";

interface PaymentCurrencyDistributionChartProps {
    className?: string;
}

export default function PaymentCurrencyDistributionChart({
    className,
}: PaymentCurrencyDistributionChartProps) {
    const [searchParams] = useSearchParams();

    // Create query parameters for currency distribution data
    const queryParams = createDashboardPaymentsQueryParams(searchParams);

    // Fetch currency distribution data
    const { data: currencyData, isLoading } =
        useDashboardPaymentsCurrencyDistribution(queryParams);

    // Transform data for chart
    const chartData =
        currencyData?.data?.map((item) => ({
            name: item.currency.label,
            value: item.count,
            percentage: item.percentage,
            code: item.currency.code,
        })) || [];

    // Colors for different currencies
    const colors = ["#73d13d", "#4096ff", "#fa8c16"];

    interface TooltipProps {
        active?: boolean;
        payload?: Array<{
            payload: {
                name: string;
                value: number;
                percentage: number;
                code: string;
            };
        }>;
    }

    const CustomTooltip = ({ active, payload }: TooltipProps) => {
        if (active && payload && payload.length) {
            const data = payload[0].payload;
            return (
                <div
                    style={{
                        backgroundColor: "white",
                        padding: "12px",
                        border: "1px solid #d9d9d9",
                        borderRadius: "8px",
                        boxShadow:
                            "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
                        minWidth: "150px",
                        zIndex: 1000,
                    }}
                >
                    <p
                        style={{
                            fontWeight: 600,
                            color: "#262626",
                            marginBottom: "8px",
                            fontSize: "14px",
                        }}
                    >
                        {data.name}
                    </p>
                    <div style={{ marginBottom: "4px" }}>
                        <span style={{ fontSize: "12px", color: "#595959" }}>
                            Cantidad:{" "}
                        </span>
                        <span style={{ fontWeight: 500, color: "#262626" }}>
                            {data.value.toLocaleString()} pagos
                        </span>
                    </div>
                    <div>
                        <span style={{ fontSize: "12px", color: "#595959" }}>
                            Porcentaje:{" "}
                        </span>
                        <span style={{ fontWeight: 500, color: "#262626" }}>
                            {data.percentage.toFixed(1)}%
                        </span>
                    </div>
                </div>
            );
        }
        return null;
    };

    return (
        <Card
            title={
                <div className="flex items-center">
                    <DollarSign className="mr-2 h-5 w-5 text-green-500" />
                    <span>Por Moneda</span>
                </div>
            }
            className={className}
            extra={
                <div className="text-sm text-gray-500">
                    Total: {currencyData?.totalPayments?.toLocaleString() || 0} pagos
                </div>
            }
        >
            {isLoading ? (
                <Skeleton active paragraph={{ rows: 6 }} />
            ) : (
                <div>
                    <div style={{ width: "100%", height: 300 }}>
                        <ResponsiveContainer>
                            <PieChart>
                                <Pie
                                    data={chartData}
                                    cx="50%"
                                    cy="50%"
                                    outerRadius={80}
                                    dataKey="value"
                                    label={(entry) => `${entry.percentage.toFixed(1)}%`}
                                    labelLine={false}
                                >
                                    {chartData.map((_, index) => (
                                        <Cell
                                            key={`cell-${index}`}
                                            fill={colors[index % colors.length]}
                                        />
                                    ))}
                                </Pie>
                                <Tooltip content={<CustomTooltip />} />
                                <Legend />
                            </PieChart>
                        </ResponsiveContainer>
                    </div>

                    {chartData.length === 0 && !isLoading && (
                        <div className="text-center py-8 text-gray-500">
                            No hay datos disponibles para el período seleccionado
                        </div>
                    )}
                </div>
            )}
        </Card>
    );
}
