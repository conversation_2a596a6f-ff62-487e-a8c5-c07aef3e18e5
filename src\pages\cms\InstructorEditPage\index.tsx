import { useEffect, useState } from "react";
import { <PERSON>, useNavigate, useParams } from "react-router-dom";
import {
    Breadcrumb,
    Button,
    Form,
    Input,
    Image,
    Typography,
    Upload,
    Select,
    Tag,
    message,
    Modal,
} from "antd";
import { useMutation, useQuery } from "@tanstack/react-query";
import { AxiosError } from "axios";
import ImgCrop from "antd-img-crop";

import type { UploadFile, UploadProps, GetProp, FormProps } from "antd";

const { Text } = Typography;
const { Dragger } = Upload;
const { Option } = Select;

import CmsLayout from "@layouts/cms/CmsLayout";
import Spinner from "@components/shared/atoms/Spinner";
import WelcomeBar from "@components/shared/molecules/WelcomeBar";

import {
    deleteInstructor,
    retrieveInstructor,
    updateInstructor,
} from "@services/portals/cms/instructor";

import CloudUpload from "@assets/shapes/cloud-upload.svg?react";
import Save from "@assets/icons/general/save-stroke.svg?react";
import Trash from "@assets/icons/huge/trash-white.svg?react";

import {
    UpdateInstructor,
    Instructor,
    InstructorStatusEnum,
} from "@myTypes/instructor";

import { getBase64 } from "@lib/helpers";
import { onErrorMessage, onSuccessMessage } from "@lib/message";

type FileType = Parameters<GetProp<UploadProps, "beforeUpload">>[0];

type DraggerProps = {
    customRequest: UploadProps["customRequest"];
    onRemove?: UploadProps["onRemove"];
    onPreview?: UploadProps["onPreview"];
    fileList?: UploadFile[];
};

type DetectChangesArgs<T, Q> = {
    data: T | undefined;
    values: Q;
    fieldsToCompare: (keyof T)[];
};

const detectChanges = ({
    data,
    values,
    fieldsToCompare,
}: DetectChangesArgs<Instructor, UpdateInstructor>): boolean => {
    if (data === undefined) {
        throw new Error("No data provided");
    }
    for (const field of fieldsToCompare) {
        if (data[field] !== values[field]) {
            return true;
        }
    }

    if (data.profilePhoto?.fid !== values.profilePhotoFile![0].uid) {
        return true;
    }

    return false;
};

const PROFILE_IMAGE_WIDTH = 276;
const PROFILE_IMAGE_HEIGHT = 386;

export default function InstructorEditPage() {
    const { iid } = useParams<{ iid: string }>();
    const [form] = Form.useForm<UpdateInstructor>();
    const navigate = useNavigate();

    const [messageApi, messageContextHolder] = message.useMessage();
    const [modal, modalContextHolder] = Modal.useModal();

    const { isLoading, isError, data, refetch } = useQuery({
        queryKey: ["instructor", iid],
        queryFn: () => retrieveInstructor(iid),
        enabled: iid !== undefined,
        refetchOnWindowFocus: false,
    });

    const updateMutation = useMutation({
        mutationFn: (payload: UpdateInstructor) =>
            updateInstructor(iid as string, payload),
        onSuccess: async () => {
            onSuccessMessage("Instructor actualizado correctamente", messageApi);
            await refetch();
        },
        onError: (error: AxiosError) => {
            console.error(error);
            onErrorMessage("Error al actualizar el Instructor", messageApi);
        },
    });

    const deleteMutation = useMutation({
        mutationFn: () => deleteInstructor(iid as string),
        onSuccess: () => {
            navigate("/cms/instructor", { replace: true });
        },
        onError: (error: AxiosError) => {
            console.error(error);
        },
    });

    const [previewOpen, setPreviewOpen] = useState(false);
    const [previewImage, setPreviewImage] = useState<string | undefined>(undefined);
    const [fileList, setFileList] = useState<UploadFile[]>([]);

    useEffect(() => {
        if (data) {
            setFileList([
                {
                    uid: data.profilePhoto?.fid as string,
                    name: data?.profilePhoto?.name as string,
                    status: "done",
                    url: data?.profilePhoto?.url,
                },
            ]);
        }
    }, [data]);

    const handleSave = () => {
        form.submit();
    };

    const handleFormFinish: FormProps<UpdateInstructor>["onFinish"] = (values) => {
        const fieldsToCompare: (keyof Instructor)[] = [
            "fullName",
            "title",
            "biography",
            "highlightedInfo",
            "facebookUrl",
            "linkedinUrl",
            "instagramUrl",
            "status",
            "order",
        ];

        if (detectChanges({ data, values, fieldsToCompare })) {
            if (data?.profilePhoto?.fid === values.profilePhotoFile![0].uid) {
                delete values.profilePhotoFile;
            }
            updateMutation.mutate(values);
        }
    };

    const draggerProps: DraggerProps = {
        customRequest: async (options) => {
            const { file, onSuccess, onError } = options;
            try {
                typeof onSuccess === "function" && onSuccess(file);
                const base64file = await getBase64(file as FileType);

                const uploadFile = file as UploadFile;
                uploadFile.url = base64file;

                form.setFieldsValue({
                    profilePhotoFile: [uploadFile],
                });
                setFileList([uploadFile]);
            } catch (error) {
                typeof onError === "function" && onError(error as Error);
            }
        },
        onRemove: () => {
            form.setFieldsValue({
                profilePhotoFile: [],
            });
            setFileList([]);
        },
        onPreview: async (file: UploadFile) => {
            if (!file.url && !file.preview) {
                file.preview = await getBase64(file.originFileObj as FileType);
            }
            setPreviewImage(file.url || (file.preview as string));
            setPreviewOpen(true);
        },
        fileList: fileList,
    };

    const handleOnDelete = () => {
        modal.confirm({
            title: "¿Estás seguro de eliminar el presente registro?",
            content: "Esta acción no se puede deshacer",
            okText: "Eliminar",
            okButtonProps: { danger: true },
            cancelText: "Cancelar",
            onOk: () => deleteMutation.mutate(),
        });
    };

    return (
        <>
            {modalContextHolder}
            {messageContextHolder}
            <CmsLayout>
                <div className="w-full h-full space-y-5 max-w-7xl">
                    <div className="flex justify-between items-center">
                        <WelcomeBar helperText="Visualiza & Edita los campos necesarios" />
                        <div className="flex gap-3">
                            <Button
                                type="primary"
                                size="large"
                                style={{ fontSize: 16 }}
                                icon={<Trash />}
                                danger
                                disabled={isError}
                                onClick={handleOnDelete}
                            >
                                Eliminar
                            </Button>
                            <Button
                                type="primary"
                                size="large"
                                style={{ fontSize: 16 }}
                                icon={<Save />}
                                disabled={isError}
                                onClick={handleSave}
                            >
                                Guardar
                            </Button>
                        </div>
                    </div>
                    {isLoading ? (
                        <Spinner />
                    ) : (
                        <>
                            <div className="p-5 bg-white-full rounded-lg space-y-5">
                                <div className="flex items-center">
                                    <Breadcrumb
                                        separator=">"
                                        items={[
                                            {
                                                title: (
                                                    <Link
                                                        to="/cms/instructor"
                                                        className="text-base"
                                                    >
                                                        Instructor
                                                    </Link>
                                                ),
                                            },
                                            {
                                                title: (
                                                    <Link
                                                        to={`/cms/instructor/${iid}`}
                                                        className="text-base"
                                                    >
                                                        {data?.fullName !== null
                                                            ? `${data?.fullName}`
                                                            : "Testimonio"}
                                                    </Link>
                                                ),
                                            },
                                            {
                                                title: (
                                                    <Text className="text-base">
                                                        Editar
                                                    </Text>
                                                ),
                                            },
                                        ]}
                                    />
                                </div>
                            </div>
                            <div className="space-y-5">
                                <Form
                                    name="instructorEdit"
                                    layout="vertical"
                                    form={form}
                                    initialValues={{
                                        ...data,
                                        profilePhotoFile: [
                                            {
                                                uid: data?.profilePhoto?.fid as string,
                                                name: data?.profilePhoto
                                                    ?.name as string,
                                                status: "done",
                                                url: data?.profilePhoto?.url,
                                            },
                                        ],
                                    }}
                                    onFinish={handleFormFinish}
                                >
                                    <div className="grid grid-cols-1 lg:grid-cols-6 gap-y-6 lg:gap-6">
                                        <div className="bg-white-full col-span-4 p-5 rounded-lg shadow-sm">
                                            <p className="text-gray-400 font-semibold text-sm">
                                                INFORMACIÓN PERSONAL
                                            </p>
                                            <Form.Item<UpdateInstructor>
                                                name="fullName"
                                                label={
                                                    <span className="font-semibold text-base">
                                                        Nombres completos
                                                    </span>
                                                }
                                                rules={[
                                                    {
                                                        required: true,
                                                        message:
                                                            "Por favor ingrese los nombres del Instructor",
                                                    },
                                                ]}
                                            >
                                                <Input placeholder="Nombres del Instructor" />
                                            </Form.Item>
                                            <Form.Item<UpdateInstructor>
                                                name="title"
                                                label={
                                                    <span className="font-semibold text-base">
                                                        Título o Rol
                                                    </span>
                                                }
                                            >
                                                <Input placeholder="Título o Rol del Instructor" />
                                            </Form.Item>
                                            <Form.Item<UpdateInstructor>
                                                name="biography"
                                                label={
                                                    <span className="font-semibold text-base">
                                                        Biografía
                                                    </span>
                                                }
                                            >
                                                <Input.TextArea placeholder="Biografía" />
                                            </Form.Item>
                                            <Form.Item<UpdateInstructor>
                                                name="highlightedInfo"
                                                label={
                                                    <span className="font-semibold text-base">
                                                        Información destacada
                                                    </span>
                                                }
                                            >
                                                <Input placeholder="Información destacada del instructor" />
                                            </Form.Item>
                                            <div className="grid grid-cols-3 gap-4">
                                                <Form.Item<UpdateInstructor>
                                                    name="facebookUrl"
                                                    label={
                                                        <span className="font-semibold text-base">
                                                            Link de Facebook
                                                        </span>
                                                    }
                                                >
                                                    <Input placeholder="Ej. https://facebook.com/username" />
                                                </Form.Item>
                                                <Form.Item<UpdateInstructor>
                                                    name="linkedinUrl"
                                                    label={
                                                        <span className="font-semibold text-base">
                                                            Link de LinkedIn
                                                        </span>
                                                    }
                                                >
                                                    <Input placeholder="Ej. https://linkedin.com/in/username" />
                                                </Form.Item>
                                                <Form.Item<UpdateInstructor>
                                                    name="instagramUrl"
                                                    label={
                                                        <span className="font-semibold text-base">
                                                            Link de Instagram
                                                        </span>
                                                    }
                                                >
                                                    <Input placeholder="Ej. https://instagram.com/username" />
                                                </Form.Item>
                                            </div>
                                        </div>
                                        <div className="col-span-2 space-y-6">
                                            <div className="bg-white-full p-5 rounded-lg shadow-sm">
                                                <p className="text-gray-400 font-semibold text-sm">
                                                    CONTENIDO MULTIMEDIA
                                                </p>
                                                <Form.Item<UpdateInstructor>
                                                    name="profilePhotoFile"
                                                    label={
                                                        <span className="font-semibold text-base">
                                                            Foto de Perfil
                                                        </span>
                                                    }
                                                    rules={[
                                                        {
                                                            required: true,
                                                            message:
                                                                "Por favor ingrese una imagen de perfil",
                                                        },
                                                    ]}
                                                    valuePropName="fileList"
                                                    getValueFromEvent={(e) => {
                                                        if (Array.isArray(e)) {
                                                            return e;
                                                        }
                                                        return e && e.fileList;
                                                    }}
                                                >
                                                    <ImgCrop
                                                        aspect={
                                                            PROFILE_IMAGE_WIDTH /
                                                            PROFILE_IMAGE_HEIGHT
                                                        }
                                                        showGrid={true}
                                                        modalTitle="Editar imagen de perfil"
                                                        modalOk="Guardar"
                                                        modalCancel="Cancelar"
                                                    >
                                                        <Dragger
                                                            maxCount={1}
                                                            multiple={false}
                                                            listType="picture"
                                                            {...draggerProps}
                                                        >
                                                            <div className="flex flex-col justify-center items-center">
                                                                <CloudUpload />
                                                                <Text className="font-medium text-black-full">
                                                                    Arrastre una imagen
                                                                    de perfil o haga
                                                                    click aquí
                                                                </Text>
                                                                <Text className="text-xs text-black-medium">
                                                                    Este campo admite
                                                                    formatos de imagen.
                                                                    Solo una imagen
                                                                </Text>
                                                            </div>
                                                        </Dragger>
                                                    </ImgCrop>
                                                </Form.Item>
                                                <Image
                                                    wrapperStyle={{ display: "none" }}
                                                    preview={{
                                                        visible: previewOpen,
                                                        onVisibleChange: setPreviewOpen,
                                                    }}
                                                    src={previewImage}
                                                />
                                            </div>
                                            <div className="bg-white-full p-5 rounded-lg shadow-sm">
                                                <p className="text-gray-400 font-semibold text-sm">
                                                    VISIBILIDAD DEL CONTENIDO
                                                </p>
                                                <Form.Item<UpdateInstructor>
                                                    name="status"
                                                    label={
                                                        <span className="font-semibold text-base">
                                                            Estado del Instructor
                                                        </span>
                                                    }
                                                >
                                                    <Select>
                                                        <Option
                                                            value={
                                                                InstructorStatusEnum.Published
                                                            }
                                                        >
                                                            <Tag
                                                                color="green"
                                                                className="rounded-full px-3"
                                                                bordered={false}
                                                            >
                                                                Publicado
                                                            </Tag>
                                                        </Option>
                                                        <Option
                                                            value={
                                                                InstructorStatusEnum.Draft
                                                            }
                                                        >
                                                            <Tag
                                                                color="volcano"
                                                                className="rounded-full px-3"
                                                                bordered={false}
                                                            >
                                                                Borrador
                                                            </Tag>
                                                        </Option>
                                                    </Select>
                                                </Form.Item>
                                                <Form.Item<UpdateInstructor>
                                                    name="order"
                                                    label={
                                                        <span className="font-semibold text-base">
                                                            Orden
                                                        </span>
                                                    }
                                                >
                                                    <Input placeholder="Ej. 2" />
                                                </Form.Item>
                                            </div>
                                        </div>
                                    </div>
                                </Form>
                            </div>
                        </>
                    )}
                </div>
            </CmsLayout>
        </>
    );
}
